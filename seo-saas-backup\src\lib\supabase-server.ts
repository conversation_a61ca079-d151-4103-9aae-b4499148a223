// Supabase Server Component Client
// This file is for server-side operations only

import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/types/database';

// Server component client (for use in server components only)
export const createSupabaseServerClient = () =>
  createServerComponentClient<Database>({ cookies });

// Server-side auth helpers
export async function getServerSession() {
  const supabase = createSupabaseServerClient();
  const { data: { session }, error } = await supabase.auth.getSession();
  
  if (error) {
    console.error('Error getting server session:', error);
    return null;
  }
  
  return session;
}

export async function getServerUser() {
  const supabase = createSupabaseServerClient();
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error) {
    console.error('Error getting server user:', error);
    return null;
  }
  
  return user;
}

// Server-side profile helpers
export async function getServerProfile(userId: string) {
  const supabase = createSupabaseServerClient();
  
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  
  if (error) {
    console.error('Error getting server profile:', error);
    return null;
  }
  
  return data;
}

// Server-side subscription helpers
export async function getServerUserSubscription(userId: string) {
  const supabase = createSupabaseServerClient();
  
  const { data, error } = await supabase
    .from('user_subscriptions')
    .select('*')
    .eq('user_id', userId)
    .single();
  
  if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
    console.error('Error getting server subscription:', error);
    return null;
  }
  
  return data;
}
