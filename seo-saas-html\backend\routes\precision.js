const express = require('express');
const router = express.Router();
const PrecisionSEOEngine = require('../services/precisionSEOEngine');
const MultiKeywordOptimizer = require('../services/multiKeywordOptimizer');
const EEATOptimizer = require('../services/eeatOptimizer');
const NLPSemanticIntegrator = require('../services/nlpSemanticIntegrator');
const GrammarReadabilityOptimizer = require('../services/grammarReadabilityOptimizer');
const AdvancedSEOOptimizer = require('../services/advancedSEOOptimizer');
const ContentQualityValidator = require('../services/contentQualityValidator');
const CompetitorBenchmarkMatcher = require('../services/competitorBenchmarkMatcher');
const MultiEngineCompatibilityTester = require('../services/multiEngineCompatibilityTester');
const PrecisionSystemIntegrator = require('../services/precisionSystemIntegrator');
const PerformanceOptimizer = require('../services/performanceOptimizer');
const { verifyToken } = require('../middleware/auth');
const rateLimit = require('express-rate-limit');

// Initialize precision SEO engine and optimizers
const precisionEngine = new PrecisionSEOEngine();
const multiKeywordOptimizer = new MultiKeywordOptimizer();
const eeatOptimizer = new EEATOptimizer();
const nlpSemanticIntegrator = new NLPSemanticIntegrator();
const grammarReadabilityOptimizer = new GrammarReadabilityOptimizer();
const advancedSEOOptimizer = new AdvancedSEOOptimizer();
const contentQualityValidator = new ContentQualityValidator();
const competitorBenchmarkMatcher = new CompetitorBenchmarkMatcher();
const multiEngineCompatibilityTester = new MultiEngineCompatibilityTester();
const precisionSystemIntegrator = new PrecisionSystemIntegrator();
const performanceOptimizer = new PerformanceOptimizer();

// Rate limiting for precision content generation (more restrictive)
const precisionRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 5, // Limit to 5 precision generations per hour
  message: {
    error: 'Too many precision content generation requests. Please try again later.',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Generate precision SEO content
router.post('/generate-content', verifyToken, precisionRateLimit, async (req, res) => {
  try {
    const { 
      keyword, 
      location = 'United States',
      searchEngine = 'google',
      industry = 'general',
      contentType = 'comprehensive guide',
      targetAudience = 'professionals',
      tone = 'professional'
    } = req.body;

    // Validate required parameters
    if (!keyword) {
      return res.status(400).json({
        success: false,
        error: 'Keyword is required for precision content generation'
      });
    }

    // Validate keyword length and format
    if (keyword.length < 2 || keyword.length > 100) {
      return res.status(400).json({
        success: false,
        error: 'Keyword must be between 2 and 100 characters'
      });
    }

    console.log(`Starting precision SEO content generation for user ${req.user.id}, keyword: ${keyword}`);

    // Generate precision content
    const result = await precisionEngine.generatePrecisionSEOContent(keyword, {
      location,
      searchEngine,
      industry,
      contentType,
      targetAudience,
      tone
    });

    if (!result.success) {
      console.error('Precision content generation failed:', result.error);
      return res.status(500).json({
        success: false,
        error: result.error || 'Failed to generate precision content'
      });
    }

    // Log successful generation
    console.log(`Precision content generated successfully for keyword: ${keyword}`, {
      wordCount: result.metadata.actualWordCount,
      precisionScore: result.metadata.finalValidation?.overallScore,
      processingTime: result.metadata.processingTimeMs
    });

    // Save to database if needed (optional)
    // await savePrecisionContentGeneration(req.user.id, result);

    res.json({
      success: true,
      data: {
        keyword: result.keyword,
        content: result.content,
        metadata: {
          wordCount: result.metadata.actualWordCount,
          targetWordCount: result.metadata.targetWordCount,
          keywordDensity: result.metadata.keywordDensity,
          headingStructure: result.metadata.headingStructure,
          precisionScore: result.metadata.precisionScore,
          eeatCompliance: result.metadata.eeatCompliance,
          processingTimeMs: result.metadata.processingTimeMs,
          competitorsAnalyzed: result.metadata.competitorAnalysis.competitorsAnalyzed,
          semanticTermsIntegrated: result.metadata.headingAnalysis.semanticData ? 
            result.metadata.headingAnalysis.semanticData.headingOptimizedTerms.length : 0,
          generatedAt: result.metadata.generatedAt
        },
        validation: result.metadata.finalValidation,
        recommendations: result.metadata.finalValidation.recommendations
      }
    });

  } catch (error) {
    console.error('Precision content generation route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during precision content generation'
    });
  }
});

// Get precision content analysis (without generating new content)
router.post('/analyze-precision', verifyToken, async (req, res) => {
  try {
    const { keyword, content } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for precision analysis'
      });
    }

    // Analyze existing content for precision compliance
    const keywordDensityCalculator = precisionEngine.keywordDensityCalculator;
    const analysis = keywordDensityCalculator.calculateSurgicalDensity(content, keyword);

    if (!analysis.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to analyze content precision'
      });
    }

    res.json({
      success: true,
      data: {
        keyword,
        analysis: analysis.analysis,
        recommendations: analysis.analysis.recommendations || [],
        generatedAt: analysis.generatedAt
      }
    });

  } catch (error) {
    console.error('Precision analysis route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during precision analysis'
    });
  }
});

// Get competitor benchmarks for a keyword
router.post('/competitor-benchmarks', verifyToken, async (req, res) => {
  try {
    const { 
      keyword, 
      location = 'United States',
      searchEngine = 'google'
    } = req.body;

    if (!keyword) {
      return res.status(400).json({
        success: false,
        error: 'Keyword is required for competitor benchmark analysis'
      });
    }

    console.log(`Analyzing competitor benchmarks for keyword: ${keyword}`);

    // Analyze competitors
    const competitorAnalysis = await precisionEngine.competitorAnalyzer.analyzePrecisionCompetitors(keyword, {
      location,
      searchEngine
    });

    if (!competitorAnalysis.success) {
      return res.status(500).json({
        success: false,
        error: competitorAnalysis.error || 'Failed to analyze competitors'
      });
    }

    // Calculate benchmarks
    const benchmarks = precisionEngine.calculatePrecisionBenchmarks(
      competitorAnalysis.competitorMetrics, 
      keyword
    );

    res.json({
      success: true,
      data: {
        keyword,
        benchmarks,
        competitors: competitorAnalysis.competitorMetrics.map(competitor => ({
          position: competitor.position,
          domain: competitor.domain,
          title: competitor.title,
          wordCount: competitor.wordCount,
          keywordDensity: competitor.keywordDensity,
          headingStructure: competitor.headingStructure?.statistics
        })),
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Competitor benchmarks route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during competitor analysis'
    });
  }
});

// Get semantic keyword research for a keyword
router.post('/semantic-research', verifyToken, async (req, res) => {
  try {
    const { keyword } = req.body;

    if (!keyword) {
      return res.status(400).json({
        success: false,
        error: 'Keyword is required for semantic research'
      });
    }

    console.log(`Performing semantic research for keyword: ${keyword}`);

    // Get mock competitor data for semantic analysis
    const mockCompetitorMetrics = [{
      domain: 'example.com',
      semanticAnalysis: {
        semanticWords: ['professional', 'quality', 'expert', 'service'],
        lsiKeywords: ['solution', 'strategy', 'implementation', 'optimization'],
        entities: ['Company', 'Industry', 'Professional']
      },
      lsiKeywords: ['best', 'top', 'guide', 'tips'],
      headingStructure: {
        headings: {
          h2: [{ text: `Best ${keyword} Practices` }],
          h3: [{ text: `${keyword} Implementation Guide` }]
        }
      }
    }];

    // Analyze heading structure and semantic data
    const headingAnalysis = await precisionEngine.headingAnalyzer.analyzeCompetitorHeadingStructures(
      mockCompetitorMetrics,
      keyword
    );

    if (!headingAnalysis.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to perform semantic research'
      });
    }

    res.json({
      success: true,
      data: {
        keyword,
        semanticData: headingAnalysis.semanticData,
        headingRecommendations: headingAnalysis.recommendations,
        optimalTemplate: headingAnalysis.optimalTemplate,
        generatedAt: headingAnalysis.generatedAt
      }
    });

  } catch (error) {
    console.error('Semantic research route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during semantic research'
    });
  }
});

// Validate content against precision requirements
router.post('/validate-content', verifyToken, async (req, res) => {
  try {
    const { keyword, content, targetWordCount, targetKeywordDensity } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for validation'
      });
    }

    // Create mock benchmarks for validation
    const mockBenchmarks = {
      wordCount: { target: targetWordCount || 1000 },
      keywordDensity: { main: targetKeywordDensity || 2.0 },
      headingStructure: { h2Count: 5, h3Count: 8 }
    };

    // Perform validation
    const validation = await precisionEngine.performFinalValidation(
      { content, metadata: { actualWordCount: content.split(/\s+/).length } },
      mockBenchmarks,
      null,
      keyword
    );

    res.json({
      success: true,
      data: {
        keyword,
        validation,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Content validation route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during content validation'
    });
  }
});

// Optimize multi-keyword density for existing content
router.post('/optimize-keywords', verifyToken, async (req, res) => {
  try {
    const { 
      keyword, 
      content,
      competitorBenchmarks,
      semanticData,
      options = {}
    } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for multi-keyword optimization'
      });
    }

    console.log(`Starting multi-keyword optimization for user ${req.user.id}, keyword: ${keyword}`);

    // Perform multi-keyword density optimization
    const optimizationResult = await multiKeywordOptimizer.optimizeMultiKeywordDensity(
      content,
      keyword,
      competitorBenchmarks || {
        keywordDensity: {
          main: 2.0,
          components: {}
        }
      },
      semanticData,
      {
        preserveReadability: true,
        maintainNaturalFlow: true,
        maxIterations: options.maxIterations || 3,
        ...options
      }
    );

    if (!optimizationResult.success) {
      return res.status(500).json({
        success: false,
        error: optimizationResult.error || 'Multi-keyword optimization failed'
      });
    }

    // Log successful optimization
    console.log(`Multi-keyword optimization completed for keyword: ${keyword}`, {
      keywordsOptimized: optimizationResult.metadata.totalKeywordsOptimized,
      accuracy: optimizationResult.metadata.optimizationAccuracy
    });

    res.json({
      success: true,
      data: {
        keyword,
        originalContent: content,
        optimizedContent: optimizationResult.content,
        optimization: {
          keywordTargets: optimizationResult.optimization.keywordTargets,
          improvements: optimizationResult.optimization.improvements,
          validationResults: optimizationResult.optimization.validationResults
        },
        metadata: {
          totalKeywordsOptimized: optimizationResult.metadata.totalKeywordsOptimized,
          optimizationAccuracy: optimizationResult.metadata.optimizationAccuracy,
          processedAt: optimizationResult.metadata.processedAt
        }
      }
    });

  } catch (error) {
    console.error('Multi-keyword optimization route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during multi-keyword optimization'
    });
  }
});

// Analyze keyword density for multiple keywords
router.post('/analyze-multi-keywords', verifyToken, async (req, res) => {
  try {
    const { 
      primaryKeyword,
      content,
      lsiKeywords = [],
      entities = [],
      variations = []
    } = req.body;

    if (!primaryKeyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Primary keyword and content are required for analysis'
      });
    }

    // Create keyword targets for analysis
    const keywordTargets = [
      {
        type: 'primary',
        keyword: primaryKeyword,
        targetDensity: 2.0,
        priority: 'critical',
        tolerance: 0.1
      }
    ];

    // Add LSI keywords
    lsiKeywords.forEach(lsi => {
      keywordTargets.push({
        type: 'lsi',
        keyword: lsi,
        targetDensity: 0.5,
        priority: 'medium',
        tolerance: 0.2
      });
    });

    // Add entities
    entities.forEach(entity => {
      keywordTargets.push({
        type: 'entity',
        keyword: entity,
        targetDensity: 0.3,
        priority: 'medium',
        tolerance: 0.25
      });
    });

    // Add variations
    variations.forEach(variation => {
      keywordTargets.push({
        type: 'variation',
        keyword: variation,
        targetDensity: 0.2,
        priority: 'low',
        tolerance: 0.3
      });
    });

    // Analyze current densities
    const analysis = multiKeywordOptimizer.analyzeCurrentDensities(content, keywordTargets);

    res.json({
      success: true,
      data: {
        primaryKeyword,
        analysis,
        summary: {
          totalKeywords: keywordTargets.length,
          needsOptimization: Object.values(analysis).filter(a => a.needsOptimization).length,
          overallAccuracy: multiKeywordOptimizer.calculateOptimizationAccuracy(content, keywordTargets)
        },
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Multi-keyword analysis route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during multi-keyword analysis'
    });
  }
});

// Optimize content for E-E-A-T compliance
router.post('/optimize-eeat', verifyToken, async (req, res) => {
  try {
    const { 
      keyword, 
      content,
      industry = 'general',
      contentType = 'guide',
      targetAudience = 'professionals'
    } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for E-E-A-T optimization'
      });
    }

    console.log(`Starting E-E-A-T optimization for user ${req.user.id}, keyword: ${keyword}`);

    // Perform E-E-A-T optimization
    const eeatResult = await eeatOptimizer.optimizeEEATCompliance(content, keyword, {
      industry,
      contentType,
      targetAudience
    });

    if (!eeatResult.success) {
      return res.status(500).json({
        success: false,
        error: eeatResult.error || 'E-E-A-T optimization failed'
      });
    }

    // Log successful optimization
    console.log(`E-E-A-T optimization completed for keyword: ${keyword}`, {
      eeatScore: eeatResult.metadata.eeatScore,
      improvementScore: eeatResult.metadata.improvementScore,
      signalsAdded: eeatResult.metadata.signalsAdded
    });

    res.json({
      success: true,
      data: {
        keyword,
        originalContent: content,
        optimizedContent: eeatResult.content,
        eeatOptimization: {
          currentEEAT: eeatResult.eeatOptimization.currentEEAT,
          finalEEAT: eeatResult.eeatOptimization.finalEEAT,
          enhancementPlan: eeatResult.eeatOptimization.enhancementPlan,
          complianceReport: eeatResult.eeatOptimization.complianceReport,
          authorityResearch: eeatResult.eeatOptimization.authorityResearch
        },
        metadata: {
          eeatScore: eeatResult.metadata.eeatScore,
          improvementScore: eeatResult.metadata.improvementScore,
          signalsAdded: eeatResult.metadata.signalsAdded,
          processedAt: eeatResult.metadata.processedAt
        }
      }
    });

  } catch (error) {
    console.error('E-E-A-T optimization route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during E-E-A-T optimization'
    });
  }
});

// Analyze content for E-E-A-T compliance (without optimization)
router.post('/analyze-eeat', verifyToken, async (req, res) => {
  try {
    const { keyword, content } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for E-E-A-T analysis'
      });
    }

    console.log(`Analyzing E-E-A-T compliance for keyword: ${keyword}`);

    // Analyze current E-E-A-T signals
    const eeatAnalysis = eeatOptimizer.analyzeCurrentEEAT(content, keyword);

    res.json({
      success: true,
      data: {
        keyword,
        eeatAnalysis: {
          experience: eeatAnalysis.experience,
          expertise: eeatAnalysis.expertise,
          authoritativeness: eeatAnalysis.authoritativeness,
          trustworthiness: eeatAnalysis.trustworthiness,
          overallScore: eeatAnalysis.overallScore,
          strengths: eeatAnalysis.strengths,
          weaknesses: eeatAnalysis.weaknesses
        },
        recommendations: eeatAnalysis.weaknesses.map(weakness => ({
          area: weakness,
          suggestion: `Improve ${weakness} signals by adding relevant content elements`,
          priority: eeatAnalysis.overallScore < 60 ? 'high' : 'medium'
        })),
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('E-E-A-T analysis route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during E-E-A-T analysis'
    });
  }
});

// Perform NLP semantic integration on content
router.post('/semantic-integration', verifyToken, async (req, res) => {
  try {
    const { 
      keyword, 
      content,
      semanticData,
      industry = 'general',
      contentType = 'guide',
      targetAudience = 'professionals'
    } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for semantic integration'
      });
    }

    console.log(`Starting NLP semantic integration for user ${req.user.id}, keyword: ${keyword}`);

    // Perform semantic integration
    const semanticResult = await nlpSemanticIntegrator.performSemanticIntegration(
      content, 
      keyword, 
      semanticData || {},
      {
        industry,
        contentType,
        targetAudience
      }
    );

    if (!semanticResult.success) {
      return res.status(500).json({
        success: false,
        error: semanticResult.error || 'Semantic integration failed'
      });
    }

    // Log successful integration
    console.log(`Semantic integration completed for keyword: ${keyword}`, {
      semanticScore: semanticResult.metadata.semanticScore,
      topicalAuthorityScore: semanticResult.metadata.topicalAuthorityScore,
      entitiesIntegrated: semanticResult.metadata.entitiesIntegrated
    });

    res.json({
      success: true,
      data: {
        keyword,
        originalContent: content,
        optimizedContent: semanticResult.content,
        semanticIntegration: {
          analysis: semanticResult.semanticIntegration.analysis,
          topicalAuthorityMap: semanticResult.semanticIntegration.topicalAuthorityMap,
          enhancements: semanticResult.semanticIntegration.enhancements,
          coherenceValidation: semanticResult.semanticIntegration.coherenceValidation,
          integrationReport: semanticResult.semanticIntegration.integrationReport
        },
        metadata: {
          semanticScore: semanticResult.metadata.semanticScore,
          topicalAuthorityScore: semanticResult.metadata.topicalAuthorityScore,
          entitiesIntegrated: semanticResult.metadata.entitiesIntegrated,
          semanticRelationships: semanticResult.metadata.semanticRelationships,
          processedAt: semanticResult.metadata.processedAt
        }
      }
    });

  } catch (error) {
    console.error('Semantic integration route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during semantic integration'
    });
  }
});

// Analyze semantic structure without optimization
router.post('/analyze-semantics', verifyToken, async (req, res) => {
  try {
    const { keyword, content, semanticData } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for semantic analysis'
      });
    }

    console.log(`Analyzing semantic structure for keyword: ${keyword}`);

    // Analyze semantic structure
    const semanticAnalysis = await nlpSemanticIntegrator.analyzeSemanticStructure(
      content, 
      keyword, 
      semanticData || {}
    );

    // Build topical authority map
    const topicalAuthorityMap = await nlpSemanticIntegrator.buildTopicalAuthorityMap(
      keyword,
      semanticData || {},
      semanticAnalysis
    );

    // Validate semantic coherence
    const coherenceValidation = nlpSemanticIntegrator.validateSemanticCoherence(
      content,
      keyword,
      topicalAuthorityMap
    );

    res.json({
      success: true,
      data: {
        keyword,
        semanticAnalysis: {
          contentStructure: semanticAnalysis.contentStructure,
          semanticDensity: semanticAnalysis.semanticDensity,
          entityRelationships: semanticAnalysis.entityRelationships,
          topicalCoverage: semanticAnalysis.topicalCoverage,
          semanticClusters: semanticAnalysis.semanticClusters,
          contextualRelevance: semanticAnalysis.contextualRelevance,
          linguisticPatterns: semanticAnalysis.linguisticPatterns,
          overallSemanticStrength: semanticAnalysis.overallSemanticStrength
        },
        topicalAuthorityMap: {
          entities: topicalAuthorityMap.entities,
          concepts: topicalAuthorityMap.concepts,
          relationships: topicalAuthorityMap.relationships,
          hierarchicalStructure: topicalAuthorityMap.hierarchicalStructure,
          authorityScore: topicalAuthorityMap.authorityScore
        },
        coherenceValidation: {
          coherenceScore: coherenceValidation.coherenceScore,
          topicalConsistency: coherenceValidation.topicalConsistency,
          semanticFlow: coherenceValidation.semanticFlow,
          conceptualCompleteness: coherenceValidation.conceptualCompleteness,
          relationshipStrength: coherenceValidation.relationshipStrength,
          issues: coherenceValidation.issues,
          recommendations: coherenceValidation.recommendations
        },
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Semantic analysis route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during semantic analysis'
    });
  }
});

// Optimize content for grammar and readability
router.post('/optimize-grammar', verifyToken, async (req, res) => {
  try {
    const { 
      keyword, 
      content,
      targetAudience = 'general',
      industry = 'general',
      contentType = 'guide'
    } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for grammar optimization'
      });
    }

    console.log(`Starting grammar and readability optimization for user ${req.user.id}, keyword: ${keyword}`);

    // Perform grammar and readability optimization
    const grammarResult = await grammarReadabilityOptimizer.optimizeGrammarAndReadability(
      content, 
      keyword, 
      {
        targetAudience,
        industry,
        contentType
      }
    );

    if (!grammarResult.success) {
      return res.status(500).json({
        success: false,
        error: grammarResult.error || 'Grammar optimization failed'
      });
    }

    // Log successful optimization
    console.log(`Grammar optimization completed for keyword: ${keyword}`, {
      grammarScore: grammarResult.metadata.grammarScore,
      readabilityScore: grammarResult.metadata.readabilityScore,
      fleschReadingEase: grammarResult.metadata.fleschReadingEase
    });

    res.json({
      success: true,
      data: {
        keyword,
        originalContent: content,
        optimizedContent: grammarResult.content,
        optimization: {
          grammarAnalysis: grammarResult.optimization.grammarAnalysis,
          readabilityAnalysis: grammarResult.optimization.readabilityAnalysis,
          corrections: grammarResult.optimization.corrections,
          finalValidation: grammarResult.optimization.finalValidation,
          metrics: grammarResult.optimization.metrics
        },
        metadata: {
          grammarScore: grammarResult.metadata.grammarScore,
          readabilityScore: grammarResult.metadata.readabilityScore,
          fleschReadingEase: grammarResult.metadata.fleschReadingEase,
          gradeLevel: grammarResult.metadata.gradeLevel,
          processedAt: grammarResult.metadata.processedAt
        }
      }
    });

  } catch (error) {
    console.error('Grammar optimization route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during grammar optimization'
    });
  }
});

// Analyze grammar and readability without optimization
router.post('/analyze-grammar', verifyToken, async (req, res) => {
  try {
    const { keyword, content } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for grammar analysis'
      });
    }

    console.log(`Analyzing grammar and readability for keyword: ${keyword}`);

    // Analyze grammar
    const grammarAnalysis = await grammarReadabilityOptimizer.analyzeGrammar(content);
    
    // Analyze readability
    const readabilityAnalysis = grammarReadabilityOptimizer.analyzeReadability(content);

    res.json({
      success: true,
      data: {
        keyword,
        grammarAnalysis: {
          errors: grammarAnalysis.errors,
          warnings: grammarAnalysis.warnings,
          suggestions: grammarAnalysis.suggestions,
          sentenceStructure: grammarAnalysis.sentenceStructure,
          overallScore: grammarAnalysis.overallScore
        },
        readabilityAnalysis: {
          metrics: readabilityAnalysis.metrics,
          scores: readabilityAnalysis.scores,
          analysis: readabilityAnalysis.analysis,
          overallScore: readabilityAnalysis.overallScore,
          recommendations: readabilityAnalysis.recommendations
        },
        summary: {
          grammarScore: grammarAnalysis.overallScore,
          readabilityScore: readabilityAnalysis.overallScore,
          fleschReadingEase: readabilityAnalysis.scores.fleschReadingEase,
          gradeLevel: readabilityAnalysis.scores.fleschKincaidGrade,
          needsImprovement: grammarAnalysis.overallScore < 85 || readabilityAnalysis.overallScore < 70
        },
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Grammar analysis route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during grammar analysis'
    });
  }
});

// Optimize content for advanced SEO signals
router.post('/optimize-seo', verifyToken, async (req, res) => {
  try {
    const { 
      keyword, 
      content,
      competitorBenchmarks,
      semanticData,
      location = 'United States',
      searchEngine = 'google',
      industry = 'general',
      contentType = 'guide',
      targetAudience = 'professionals'
    } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for SEO optimization'
      });
    }

    console.log(`Starting advanced SEO optimization for user ${req.user.id}, keyword: ${keyword}`);

    // Perform advanced SEO optimization
    const seoResult = await advancedSEOOptimizer.optimizeSEOSignals(
      content, 
      keyword, 
      competitorBenchmarks || {},
      semanticData || {},
      {
        location,
        searchEngine,
        industry,
        contentType,
        targetAudience
      }
    );

    if (!seoResult.success) {
      return res.status(500).json({
        success: false,
        error: seoResult.error || 'Advanced SEO optimization failed'
      });
    }

    // Log successful optimization
    console.log(`Advanced SEO optimization completed for keyword: ${keyword}`, {
      seoScore: seoResult.metadata.seoScore,
      searchEngineCompatibility: seoResult.metadata.searchEngineCompatibility,
      rankingPotential: seoResult.metadata.rankingPotential,
      featuredSnippetPotential: seoResult.metadata.featuredSnippetPotential
    });

    res.json({
      success: true,
      data: {
        keyword,
        originalContent: content,
        optimizedContent: seoResult.content,
        seoOptimization: {
          currentAnalysis: seoResult.seoOptimization.currentAnalysis,
          serpAnalysis: seoResult.seoOptimization.serpAnalysis,
          multiEngineOptimization: seoResult.seoOptimization.multiEngineOptimization,
          technicalOptimization: seoResult.seoOptimization.technicalOptimization,
          richResultsOptimization: seoResult.seoOptimization.richResultsOptimization,
          voiceSearchOptimization: seoResult.seoOptimization.voiceSearchOptimization,
          mobileOptimization: seoResult.seoOptimization.mobileOptimization,
          finalValidation: seoResult.seoOptimization.finalValidation
        },
        metadata: {
          seoScore: seoResult.metadata.seoScore,
          searchEngineCompatibility: seoResult.metadata.searchEngineCompatibility,
          rankingPotential: seoResult.metadata.rankingPotential,
          featuredSnippetPotential: seoResult.metadata.featuredSnippetPotential,
          processedAt: seoResult.metadata.processedAt
        }
      }
    });

  } catch (error) {
    console.error('Advanced SEO optimization route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during advanced SEO optimization'
    });
  }
});

// Analyze SEO signals without optimization
router.post('/analyze-seo', verifyToken, async (req, res) => {
  try {
    const { keyword, content, semanticData, location = 'United States' } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for SEO analysis'
      });
    }

    console.log(`Analyzing SEO signals for keyword: ${keyword}`);

    // Analyze current SEO signals
    const seoAnalysis = await advancedSEOOptimizer.analyzeSEOSignals(content, keyword, semanticData || {});
    
    // Analyze SERP features
    const serpAnalysis = await advancedSEOOptimizer.analyzeSERPFeatures(keyword, location);
    
    // Perform SEO validation
    const seoValidation = await advancedSEOOptimizer.performSEOValidation(content, keyword, serpAnalysis);

    res.json({
      success: true,
      data: {
        keyword,
        seoAnalysis: {
          titleOptimization: seoAnalysis.titleOptimization,
          metaDescriptionOptimization: seoAnalysis.metaDescriptionOptimization,
          headingStructureOptimization: seoAnalysis.headingStructureOptimization,
          internalLinkingOptimization: seoAnalysis.internalLinkingOptimization,
          keywordDistributionOptimization: seoAnalysis.keywordDistributionOptimization,
          technicalSEOFactors: seoAnalysis.technicalSEOFactors,
          overallSEOScore: seoAnalysis.overallSEOScore
        },
        serpAnalysis: {
          searchIntent: serpAnalysis.searchIntent,
          featuredSnippetOpportunity: serpAnalysis.featuredSnippetOpportunity,
          peopleAlsoAsk: serpAnalysis.peopleAlsoAsk,
          richResults: serpAnalysis.richResults,
          competitorAnalysis: serpAnalysis.competitorAnalysis
        },
        validation: {
          overallSEOScore: seoValidation.overallSEOScore,
          searchEngineCompatibility: seoValidation.searchEngineCompatibility,
          rankingPotential: seoValidation.rankingPotential,
          featuredSnippetPotential: seoValidation.featuredSnippetPotential,
          recommendations: seoValidation.recommendations
        },
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('SEO analysis route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during SEO analysis'
    });
  }
});

// Validate content quality comprehensively
router.post('/validate-quality', verifyToken, async (req, res) => {
  try {
    const { 
      keyword, 
      content,
      competitorBenchmarks,
      semanticData,
      industry = 'general',
      contentType = 'guide',
      targetAudience = 'professionals'
    } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for quality validation'
      });
    }

    console.log(`Starting comprehensive quality validation for user ${req.user.id}, keyword: ${keyword}`);

    // Perform comprehensive quality validation
    const validationResult = await contentQualityValidator.validateContentQuality(
      content, 
      keyword, 
      competitorBenchmarks || {},
      {
        semanticData,
        industry,
        contentType,
        targetAudience
      }
    );

    if (!validationResult.success) {
      return res.status(500).json({
        success: false,
        error: validationResult.error || 'Content quality validation failed'
      });
    }

    // Log successful validation
    console.log(`Quality validation completed for keyword: ${keyword}`, {
      overallQualityScore: validationResult.validation.overallQualityScore,
      qualityGrade: validationResult.metadata.qualityGrade,
      passed: validationResult.validation.passed,
      criticalIssues: validationResult.metadata.criticalIssues.length
    });

    res.json({
      success: true,
      data: {
        keyword,
        validation: {
          overallQualityScore: validationResult.validation.overallQualityScore,
          passed: validationResult.validation.passed,
          qualityGrade: validationResult.metadata.qualityGrade,
          precisionValidation: validationResult.validation.precisionValidation,
          keywordValidation: validationResult.validation.keywordValidation,
          structureValidation: validationResult.validation.structureValidation,
          readabilityValidation: validationResult.validation.readabilityValidation,
          seoValidation: validationResult.validation.seoValidation,
          eeatValidation: validationResult.validation.eeatValidation,
          semanticValidation: validationResult.validation.semanticValidation,
          competitiveValidation: validationResult.validation.competitiveValidation,
          validationReport: validationResult.validation.validationReport
        },
        metadata: {
          validatedAt: validationResult.metadata.validatedAt,
          qualityGrade: validationResult.metadata.qualityGrade,
          criticalIssues: validationResult.metadata.criticalIssues.length,
          majorIssues: validationResult.metadata.majorIssues.length,
          minorIssues: validationResult.metadata.minorIssues.length,
          improvementAreas: validationResult.metadata.improvementAreas,
          recommendations: validationResult.validation.validationReport.recommendations,
          nextSteps: validationResult.validation.validationReport.nextSteps
        }
      }
    });

  } catch (error) {
    console.error('Quality validation route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during quality validation'
    });
  }
});

// Get quality validation metrics for content
router.post('/quality-metrics', verifyToken, async (req, res) => {
  try {
    const { keyword, content, competitorBenchmarks } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for quality metrics'
      });
    }

    console.log(`Calculating quality metrics for keyword: ${keyword}`);

    // Calculate precision compliance
    const precisionCompliance = contentQualityValidator.validatePrecisionCompliance(
      content, 
      keyword, 
      competitorBenchmarks || {}
    );

    // Calculate keyword optimization score
    const keywordOptimization = contentQualityValidator.validateKeywordOptimization(
      content, 
      keyword, 
      competitorBenchmarks || {}
    );

    // Calculate content structure score
    const contentStructure = contentQualityValidator.validateContentStructure(
      content, 
      keyword, 
      competitorBenchmarks || {}
    );

    // Calculate readability metrics
    const readabilityMetrics = contentQualityValidator.validateReadabilityStandards(content);

    // Calculate SEO compliance score
    const seoCompliance = contentQualityValidator.validateSEOCompliance(content, keyword);

    res.json({
      success: true,
      data: {
        keyword,
        metrics: {
          precisionCompliance: {
            wordCountAccuracy: precisionCompliance.wordCount.accuracy,
            keywordDensityAccuracy: precisionCompliance.keywordDensity.accuracy,
            overallPrecision: precisionCompliance.overallPrecision,
            passed: precisionCompliance.passed
          },
          keywordOptimization: {
            optimizationScore: keywordOptimization.overallOptimization,
            titleOptimized: keywordOptimization.mainKeyword.placement.title,
            distributionScore: keywordOptimization.mainKeyword.distribution.distributionRatio,
            passed: keywordOptimization.passed
          },
          contentStructure: {
            structureScore: contentStructure.overallStructure,
            headingCompliance: contentStructure.headings.compliance,
            passed: contentStructure.passed
          },
          readability: {
            overallScore: readabilityMetrics.overallReadability,
            fleschScore: readabilityMetrics.metrics.fleschReadingEase,
            gradeLevel: readabilityMetrics.metrics.fleschKincaidGrade,
            passed: readabilityMetrics.passed
          },
          seoCompliance: {
            overallScore: seoCompliance.overallSEO,
            titleOptimization: seoCompliance.title.passed,
            internalLinking: seoCompliance.internalLinking.passed,
            passed: seoCompliance.passed
          }
        },
        summary: {
          overallQuality: (precisionCompliance.overallPrecision + keywordOptimization.overallOptimization + 
                          contentStructure.overallStructure + readabilityMetrics.overallReadability + 
                          seoCompliance.overallSEO) / 5,
          allCriteriaMet: precisionCompliance.passed && keywordOptimization.passed && 
                         contentStructure.passed && readabilityMetrics.passed && seoCompliance.passed
        },
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Quality metrics route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during quality metrics calculation'
    });
  }
});

// Get precision compliance check
router.post('/precision-compliance', verifyToken, async (req, res) => {
  try {
    const { keyword, content, targetWordCount, targetKeywordDensity } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for precision compliance check'
      });
    }

    console.log(`Checking precision compliance for keyword: ${keyword}`);

    // Create mock benchmarks if not provided
    const mockBenchmarks = {
      wordCount: { target: targetWordCount || 1000 },
      keywordDensity: { main: targetKeywordDensity || 2.0 }
    };

    // Check precision compliance
    const precisionCompliance = contentQualityValidator.validatePrecisionCompliance(
      content, 
      keyword, 
      mockBenchmarks
    );

    res.json({
      success: true,
      data: {
        keyword,
        compliance: {
          wordCount: {
            actual: precisionCompliance.wordCount.actual,
            target: precisionCompliance.wordCount.target,
            deviation: precisionCompliance.wordCount.deviation,
            accuracy: precisionCompliance.wordCount.accuracy,
            passed: precisionCompliance.wordCount.passed,
            status: precisionCompliance.wordCount.passed ? 'COMPLIANT' : 'NON-COMPLIANT'
          },
          keywordDensity: {
            actual: precisionCompliance.keywordDensity.actual,
            target: precisionCompliance.keywordDensity.target,
            deviation: precisionCompliance.keywordDensity.deviation,
            accuracy: precisionCompliance.keywordDensity.accuracy,
            passed: precisionCompliance.keywordDensity.passed,
            status: precisionCompliance.keywordDensity.passed ? 'COMPLIANT' : 'NON-COMPLIANT'
          },
          overall: {
            precisionScore: precisionCompliance.overallPrecision,
            passed: precisionCompliance.passed,
            status: precisionCompliance.passed ? 'PRECISION COMPLIANT' : 'PRECISION NON-COMPLIANT'
          }
        },
        recommendations: [
          ...(precisionCompliance.wordCount.passed ? [] : [
            `Adjust word count by ${precisionCompliance.wordCount.deviation > 0 ? 'removing' : 'adding'} ${Math.abs(precisionCompliance.wordCount.deviation)} words to match target exactly`
          ]),
          ...(precisionCompliance.keywordDensity.passed ? [] : [
            `Adjust keyword density by ${precisionCompliance.keywordDensity.deviation > 0 ? 'reducing' : 'increasing'} keyword occurrences to match target density of ${precisionCompliance.keywordDensity.target}%`
          ])
        ],
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Precision compliance route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during precision compliance check'
    });
  }
});

// Match content to competitor benchmarks with surgical precision
router.post('/match-benchmarks', verifyToken, async (req, res) => {
  try {
    const { 
      keyword, 
      content,
      competitorBenchmarks,
      industry = 'general',
      contentType = 'guide',
      targetAudience = 'professionals',
      location = 'United States'
    } = req.body;

    if (!keyword || !content || !competitorBenchmarks) {
      return res.status(400).json({
        success: false,
        error: 'Keyword, content, and competitor benchmarks are required for benchmark matching'
      });
    }

    console.log(`Starting competitor benchmark matching for user ${req.user.id}, keyword: ${keyword}`);

    // Perform competitor benchmark matching
    const matchingResult = await competitorBenchmarkMatcher.matchCompetitorBenchmarks(
      content, 
      keyword, 
      competitorBenchmarks,
      {
        industry,
        contentType,
        targetAudience,
        location
      }
    );

    if (!matchingResult.success) {
      return res.status(500).json({
        success: false,
        error: matchingResult.error || 'Competitor benchmark matching failed'
      });
    }

    // Log successful matching
    console.log(`Benchmark matching completed for keyword: ${keyword}`, {
      matchingAccuracy: matchingResult.metadata.matchingAccuracy,
      benchmarksMatched: matchingResult.metadata.benchmarksMatched,
      totalBenchmarks: matchingResult.metadata.totalBenchmarks,
      perfectMatch: matchingResult.metadata.perfectMatch
    });

    res.json({
      success: true,
      data: {
        keyword,
        originalContent: content,
        optimizedContent: matchingResult.content,
        matching: {
          originalMetrics: matchingResult.matching.originalMetrics,
          targetBenchmarks: matchingResult.matching.targetBenchmarks,
          finalMetrics: matchingResult.matching.finalMetrics,
          adjustmentsMade: matchingResult.matching.adjustmentsMade,
          complianceValidation: matchingResult.matching.complianceValidation,
          matchingReport: matchingResult.matching.matchingReport
        },
        metadata: {
          matchingAccuracy: matchingResult.metadata.matchingAccuracy,
          benchmarksMatched: matchingResult.metadata.benchmarksMatched,
          totalBenchmarks: matchingResult.metadata.totalBenchmarks,
          perfectMatch: matchingResult.metadata.perfectMatch,
          adjustmentsApplied: matchingResult.metadata.adjustmentsApplied,
          processedAt: matchingResult.metadata.processedAt
        }
      }
    });

  } catch (error) {
    console.error('Benchmark matching route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during benchmark matching'
    });
  }
});

// Analyze current content metrics against competitor benchmarks
router.post('/analyze-benchmark-compliance', verifyToken, async (req, res) => {
  try {
    const { keyword, content, competitorBenchmarks } = req.body;

    if (!keyword || !content || !competitorBenchmarks) {
      return res.status(400).json({
        success: false,
        error: 'Keyword, content, and competitor benchmarks are required for compliance analysis'
      });
    }

    console.log(`Analyzing benchmark compliance for keyword: ${keyword}`);

    // Analyze current content metrics
    const currentMetrics = await competitorBenchmarkMatcher.analyzeCurrentContentMetrics(content, keyword);
    
    // Calculate required adjustments
    const requiredAdjustments = competitorBenchmarkMatcher.calculateRequiredAdjustments(currentMetrics, competitorBenchmarks);
    
    // Validate benchmark compliance
    const complianceValidation = competitorBenchmarkMatcher.validateBenchmarkCompliance(content, keyword, competitorBenchmarks);

    res.json({
      success: true,
      data: {
        keyword,
        currentMetrics: {
          wordCount: currentMetrics.wordCount,
          keywordDensity: currentMetrics.keywordMetrics.mainKeyword.density,
          headingStructure: currentMetrics.headingStructure,
          readabilityMetrics: currentMetrics.readabilityMetrics
        },
        targetBenchmarks: {
          wordCount: competitorBenchmarks.wordCount,
          keywordDensity: competitorBenchmarks.keywordDensity,
          headingStructure: competitorBenchmarks.headingStructure
        },
        compliance: {
          wordCount: complianceValidation.wordCount,
          keywordDensity: complianceValidation.keywordDensity,
          headingStructure: complianceValidation.headingStructure,
          overallAccuracy: complianceValidation.overallAccuracy,
          perfectMatch: complianceValidation.perfectMatch,
          complianceGrade: complianceValidation.complianceGrade
        },
        requiredAdjustments: requiredAdjustments.map(adj => ({
          type: adj.type,
          priority: adj.priority,
          action: adj.action,
          current: adj.current,
          target: adj.target,
          difference: adj.difference,
          method: adj.method
        })),
        recommendations: [
          ...(complianceValidation.wordCount.compliant ? [] : [
            `Adjust word count by ${Math.abs(complianceValidation.wordCount.deviation)} words to match competitor average of ${complianceValidation.wordCount.target} words exactly`
          ]),
          ...(complianceValidation.keywordDensity.compliant ? [] : [
            `Adjust keyword density by ${complianceValidation.keywordDensity.deviation.toFixed(2)}% to match competitor average of ${complianceValidation.keywordDensity.target}%`
          ]),
          ...(complianceValidation.headingStructure.compliant ? [] : [
            'Restructure headings to match competitor heading distribution patterns'
          ])
        ],
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Benchmark compliance analysis route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during benchmark compliance analysis'
    });
  }
});

// Get surgical precision adjustment recommendations
router.post('/precision-adjustments', verifyToken, async (req, res) => {
  try {
    const { keyword, content, competitorBenchmarks } = req.body;

    if (!keyword || !content || !competitorBenchmarks) {
      return res.status(400).json({
        success: false,
        error: 'Keyword, content, and competitor benchmarks are required for precision adjustments'
      });
    }

    console.log(`Calculating precision adjustments for keyword: ${keyword}`);

    // Analyze current content metrics
    const currentMetrics = await competitorBenchmarkMatcher.analyzeCurrentContentMetrics(content, keyword);
    
    // Calculate required adjustments with surgical precision
    const requiredAdjustments = competitorBenchmarkMatcher.calculateRequiredAdjustments(currentMetrics, competitorBenchmarks);

    // Categorize adjustments by priority and type
    const criticalAdjustments = requiredAdjustments.filter(adj => adj.priority === 'critical');
    const highPriorityAdjustments = requiredAdjustments.filter(adj => adj.priority === 'high');
    const mediumPriorityAdjustments = requiredAdjustments.filter(adj => adj.priority === 'medium');

    res.json({
      success: true,
      data: {
        keyword,
        adjustmentSummary: {
          totalAdjustments: requiredAdjustments.length,
          criticalAdjustments: criticalAdjustments.length,
          highPriorityAdjustments: highPriorityAdjustments.length,
          mediumPriorityAdjustments: mediumPriorityAdjustments.length
        },
        adjustmentsByPriority: {
          critical: criticalAdjustments.map(adj => ({
            type: adj.type,
            action: adj.action,
            current: adj.current,
            target: adj.target,
            difference: adj.difference,
            method: adj.method,
            description: this.generateAdjustmentDescription(adj)
          })),
          high: highPriorityAdjustments.map(adj => ({
            type: adj.type,
            action: adj.action,
            current: adj.current,
            target: adj.target,
            difference: adj.difference,
            method: adj.method,
            description: this.generateAdjustmentDescription(adj)
          })),
          medium: mediumPriorityAdjustments.map(adj => ({
            type: adj.type,
            action: adj.action,
            current: adj.current,
            target: adj.target,
            difference: adj.difference,
            method: adj.method,
            description: this.generateAdjustmentDescription(adj)
          }))
        },
        executionPlan: {
          step1: criticalAdjustments.length > 0 ? 'Execute critical word count and keyword density adjustments' : 'No critical adjustments needed',
          step2: highPriorityAdjustments.length > 0 ? 'Apply heading structure modifications' : 'No heading adjustments needed',
          step3: mediumPriorityAdjustments.length > 0 ? 'Fine-tune component keyword densities' : 'No component adjustments needed',
          step4: 'Validate final compliance and deploy'
        },
        estimatedTime: this.estimateAdjustmentTime(requiredAdjustments),
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Precision adjustments route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during precision adjustments calculation'
    });
  }
});

// Helper method for adjustment descriptions
function generateAdjustmentDescription(adjustment) {
  switch (adjustment.type) {
    case 'wordCount':
      return `${adjustment.action === 'expand' ? 'Add' : 'Remove'} ${Math.abs(adjustment.difference)} words to match competitor average of ${adjustment.target} words exactly`;
    case 'keywordDensity':
      return `${adjustment.action === 'increase' ? 'Increase' : 'Decrease'} keyword density by ${Math.abs(adjustment.difference).toFixed(2)}% to reach ${adjustment.target}%`;
    case 'headingStructure':
      return `${adjustment.action === 'add' ? 'Add' : 'Remove'} ${Math.abs(adjustment.difference)} ${adjustment.subtype} heading${Math.abs(adjustment.difference) > 1 ? 's' : ''}`;
    case 'componentKeywordDensity':
      return `${adjustment.action === 'increase' ? 'Increase' : 'Decrease'} "${adjustment.keyword}" density by ${Math.abs(adjustment.difference).toFixed(2)}%`;
    default:
      return `Apply ${adjustment.method} to ${adjustment.action} ${adjustment.type}`;
  }
}

// Helper method for time estimation
function estimateAdjustmentTime(adjustments) {
  const baseTime = 30; // 30 seconds base
  const timePerAdjustment = {
    critical: 60, // 1 minute per critical adjustment
    high: 45, // 45 seconds per high priority
    medium: 30 // 30 seconds per medium priority
  };
  
  const totalTime = adjustments.reduce((time, adj) => {
    return time + (timePerAdjustment[adj.priority] || 30);
  }, baseTime);
  
  return {
    seconds: totalTime,
    formatted: `${Math.floor(totalTime / 60)}m ${totalTime % 60}s`
  };
}

// Test content compatibility across multiple AI/LLM engines
router.post('/test-compatibility', verifyToken, async (req, res) => {
  try {
    const { 
      keyword, 
      content,
      engines = ['openai', 'anthropic', 'google', 'microsoft', 'cohere'],
      industry = 'general',
      contentType = 'guide',
      targetAudience = 'professionals',
      testDepth = 'comprehensive'
    } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for compatibility testing'
      });
    }

    console.log(`Starting multi-engine compatibility testing for user ${req.user.id}, keyword: ${keyword}`);

    // Perform multi-engine compatibility testing
    const compatibilityResult = await multiEngineCompatibilityTester.testMultiEngineCompatibility(
      content, 
      keyword, 
      {
        engines,
        industry,
        contentType,
        targetAudience,
        testDepth
      }
    );

    if (!compatibilityResult.success) {
      return res.status(500).json({
        success: false,
        error: compatibilityResult.error || 'Multi-engine compatibility testing failed'
      });
    }

    // Log successful testing
    console.log(`Compatibility testing completed for keyword: ${keyword}`, {
      overallScore: compatibilityResult.compatibility.overallScore,
      grade: compatibilityResult.compatibility.grade,
      testedEngines: compatibilityResult.metadata.testedEngines,
      passedEngines: compatibilityResult.metadata.passedEngines
    });

    res.json({
      success: true,
      data: {
        keyword,
        compatibility: {
          overallScore: compatibilityResult.compatibility.overallScore,
          grade: compatibilityResult.compatibility.grade,
          engineTestResults: compatibilityResult.compatibility.engineTestResults,
          structureCompatibility: compatibilityResult.compatibility.structureCompatibility,
          formatCompatibility: compatibilityResult.compatibility.formatCompatibility,
          semanticCompatibility: compatibilityResult.compatibility.semanticCompatibility,
          keywordCompatibility: compatibilityResult.compatibility.keywordCompatibility,
          readabilityCompatibility: compatibilityResult.compatibility.readabilityCompatibility,
          recommendations: compatibilityResult.compatibility.recommendations
        },
        metadata: {
          testedEngines: compatibilityResult.metadata.testedEngines,
          passedEngines: compatibilityResult.metadata.passedEngines,
          universalCompatibility: compatibilityResult.metadata.universalCompatibility,
          highestPerformingEngine: compatibilityResult.metadata.highestPerformingEngine,
          lowestPerformingEngine: compatibilityResult.metadata.lowestPerformingEngine,
          testedAt: compatibilityResult.metadata.testedAt
        }
      }
    });

  } catch (error) {
    console.error('Compatibility testing route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during compatibility testing'
    });
  }
});

// Analyze engine-specific performance
router.post('/analyze-engine-performance', verifyToken, async (req, res) => {
  try {
    const { keyword, content, targetEngine } = req.body;

    if (!keyword || !content || !targetEngine) {
      return res.status(400).json({
        success: false,
        error: 'Keyword, content, and target engine are required for engine performance analysis'
      });
    }

    console.log(`Analyzing ${targetEngine} performance for keyword: ${keyword}`);

    // Test specific engine compatibility
    const compatibilityResult = await multiEngineCompatibilityTester.testMultiEngineCompatibility(
      content, 
      keyword, 
      {
        engines: [targetEngine],
        testDepth: 'detailed'
      }
    );

    if (!compatibilityResult.success) {
      return res.status(500).json({
        success: false,
        error: 'Engine performance analysis failed'
      });
    }

    const engineResult = compatibilityResult.compatibility.engineTestResults[targetEngine];

    if (!engineResult) {
      return res.status(404).json({
        success: false,
        error: `Engine '${targetEngine}' not found or not supported`
      });
    }

    res.json({
      success: true,
      data: {
        keyword,
        targetEngine,
        performance: {
          overallScore: engineResult.score,
          passed: engineResult.passed,
          engineName: engineResult.engineName,
          testResults: {
            structureRecognition: engineResult.tests.structureRecognition,
            keywordProcessing: engineResult.tests.keywordProcessing,
            semanticUnderstanding: engineResult.tests.semanticUnderstanding,
            outputQuality: engineResult.tests.outputQuality,
            performance: engineResult.tests.performance
          },
          strengths: engineResult.strengths,
          weaknesses: engineResult.weaknesses,
          recommendations: engineResult.recommendations
        },
        engineInfo: multiEngineCompatibilityTester.supportedEngines[targetEngine],
        optimizations: this.generateEngineOptimizations(engineResult, targetEngine),
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Engine performance analysis route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during engine performance analysis'
    });
  }
});

// Get compatibility score breakdown
router.post('/compatibility-breakdown', verifyToken, async (req, res) => {
  try {
    const { keyword, content } = req.body;

    if (!keyword || !content) {
      return res.status(400).json({
        success: false,
        error: 'Keyword and content are required for compatibility breakdown'
      });
    }

    console.log(`Generating compatibility breakdown for keyword: ${keyword}`);

    // Perform quick compatibility assessment
    const compatibilityResult = await multiEngineCompatibilityTester.testMultiEngineCompatibility(
      content, 
      keyword, 
      {
        engines: ['openai', 'anthropic', 'google'],
        testDepth: 'quick'
      }
    );

    if (!compatibilityResult.success) {
      return res.status(500).json({
        success: false,
        error: 'Compatibility breakdown analysis failed'
      });
    }

    // Calculate detailed breakdown
    const breakdown = {
      overallCompatibility: {
        score: compatibilityResult.compatibility.overallScore,
        grade: compatibilityResult.compatibility.grade,
        universalCompatibility: compatibilityResult.metadata.universalCompatibility
      },
      categoryBreakdown: {
        structural: {
          score: compatibilityResult.compatibility.structureCompatibility.score,
          passed: compatibilityResult.compatibility.structureCompatibility.passed,
          details: compatibilityResult.compatibility.structureCompatibility.details
        },
        semantic: {
          score: compatibilityResult.compatibility.semanticCompatibility.score,
          passed: compatibilityResult.compatibility.semanticCompatibility.passed,
          details: compatibilityResult.compatibility.semanticCompatibility.details
        },
        keyword: {
          score: compatibilityResult.compatibility.keywordCompatibility.score,
          passed: compatibilityResult.compatibility.keywordCompatibility.passed,
          details: compatibilityResult.compatibility.keywordCompatibility.details
        },
        format: {
          score: compatibilityResult.compatibility.formatCompatibility.score,
          passed: compatibilityResult.compatibility.formatCompatibility.passed,
          details: compatibilityResult.compatibility.formatCompatibility.details
        },
        readability: {
          score: compatibilityResult.compatibility.readabilityCompatibility.score,
          passed: compatibilityResult.compatibility.readabilityCompatibility.passed,
          details: compatibilityResult.compatibility.readabilityCompatibility.details
        }
      },
      enginePerformance: Object.entries(compatibilityResult.compatibility.engineTestResults).map(([engineId, result]) => ({
        engineId,
        engineName: result.engineName,
        score: result.score,
        passed: result.passed,
        strengths: result.strengths,
        weaknesses: result.weaknesses
      })),
      recommendations: {
        immediate: compatibilityResult.compatibility.recommendations.filter(r => r.priority === 'high'),
        suggested: compatibilityResult.compatibility.recommendations.filter(r => r.priority === 'medium'),
        optional: compatibilityResult.compatibility.recommendations.filter(r => r.priority === 'low')
      }
    };

    res.json({
      success: true,
      data: {
        keyword,
        breakdown,
        summary: {
          totalEnginesTested: compatibilityResult.metadata.testedEngines,
          enginesPassed: compatibilityResult.metadata.passedEngines,
          compatibilityRate: (compatibilityResult.metadata.passedEngines / compatibilityResult.metadata.testedEngines) * 100,
          bestPerformingEngine: compatibilityResult.metadata.highestPerformingEngine,
          worstPerformingEngine: compatibilityResult.metadata.lowestPerformingEngine,
          readyForProduction: compatibilityResult.compatibility.overallScore >= 85
        },
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Compatibility breakdown route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during compatibility breakdown'
    });
  }
});

// Get supported engines and their capabilities
router.get('/supported-engines', async (req, res) => {
  try {
    const supportedEngines = multiEngineCompatibilityTester.supportedEngines;
    const engineList = Object.entries(supportedEngines).map(([engineId, engine]) => ({
      engineId,
      name: engine.name,
      versions: engine.versions,
      capabilities: engine.capabilities,
      tokenLimits: engine.tokenLimits,
      strengths: engine.strengths,
      weaknesses: engine.weaknesses,
      status: 'available'
    }));

    res.json({
      success: true,
      data: {
        engines: engineList,
        totalEngines: engineList.length,
        defaultEngines: ['openai', 'anthropic', 'google', 'microsoft', 'cohere'],
        testingCapabilities: [
          'structure_recognition',
          'keyword_processing', 
          'semantic_understanding',
          'output_quality',
          'performance_metrics'
        ],
        compatibilityMetrics: multiEngineCompatibilityTester.compatibilityMetrics,
        performanceThresholds: multiEngineCompatibilityTester.performanceThresholds
      }
    });

  } catch (error) {
    console.error('Supported engines route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error while fetching supported engines'
    });
  }
});

// Helper function for engine optimizations
function generateEngineOptimizations(engineResult, targetEngine) {
  const optimizations = [];
  
  // Structure optimizations
  if (engineResult.tests.structureRecognition.score < 85) {
    optimizations.push({
      category: 'Structure',
      issue: 'Poor structure recognition',
      solution: `Optimize content structure for ${targetEngine} compatibility`,
      priority: 'high'
    });
  }
  
  // Keyword optimizations
  if (engineResult.tests.keywordProcessing.score < 90) {
    optimizations.push({
      category: 'Keywords',
      issue: 'Suboptimal keyword processing',
      solution: `Enhance keyword placement and density for ${targetEngine}`,
      priority: 'medium'
    });
  }
  
  // Semantic optimizations
  if (engineResult.tests.semanticUnderstanding.score < 85) {
    optimizations.push({
      category: 'Semantic',
      issue: 'Limited semantic understanding',
      solution: `Improve semantic clarity and concept relationships for ${targetEngine}`,
      priority: 'medium'
    });
  }
  
  // Performance optimizations
  if (engineResult.tests.performance.score < 80) {
    optimizations.push({
      category: 'Performance',
      issue: 'Performance bottlenecks detected',
      solution: `Optimize content length and complexity for ${targetEngine} efficiency`,
      priority: 'low'
    });
  }
  
  return optimizations;
}

// Generate content with unified precision system integration
router.post('/generate-unified', verifyToken, precisionRateLimit, async (req, res) => {
  try {
    const { 
      keyword, 
      location = 'United States',
      searchEngine = 'google',
      industry = 'general',
      contentType = 'comprehensive guide',
      targetAudience = 'professionals',
      tone = 'professional',
      engines = ['openai', 'anthropic', 'google', 'microsoft', 'cohere'],
      testDepth = 'comprehensive'
    } = req.body;

    // Validate required parameters
    if (!keyword) {
      return res.status(400).json({
        success: false,
        error: 'Keyword is required for unified precision content generation'
      });
    }

    // Validate keyword length and format
    if (keyword.length < 2 || keyword.length > 100) {
      return res.status(400).json({
        success: false,
        error: 'Keyword must be between 2 and 100 characters'
      });
    }

    console.log(`Starting unified precision system integration for user ${req.user.id}, keyword: ${keyword}`);

    // Generate unified precision content with complete system integration
    const result = await precisionSystemIntegrator.generateUnifiedPrecisionContent(keyword, {
      location,
      searchEngine,
      industry,
      contentType,
      targetAudience,
      tone,
      engines,
      testDepth
    });

    if (!result.success) {
      console.error('Unified precision system integration failed:', result.error);
      return res.status(500).json({
        success: false,
        error: result.error || 'Failed to generate unified precision content'
      });
    }

    // Log successful unified generation
    console.log(`Unified precision content generated successfully for keyword: ${keyword}`, {
      sessionId: result.systemIntegration.sessionId,
      totalProcessingTime: result.metadata.totalProcessingTime,
      finalQualityScore: result.metadata.finalQualityScore,
      systemEfficiency: result.metadata.systemEfficiency,
      universalCompatibility: result.metadata.universalCompatibility,
      deploymentReady: result.metadata.deploymentReady
    });

    res.json({
      success: true,
      data: {
        keyword: result.metadata.keyword,
        content: result.content,
        systemIntegration: {
          sessionId: result.systemIntegration.sessionId,
          integrationStatus: result.systemIntegration.integrationStatus,
          phases: {
            dataCollection: {
              executionTime: result.systemIntegration.phases.dataCollection.phaseExecutionTime,
              qualityScore: result.systemIntegration.phases.dataCollection.qualityScore,
              competitorsAnalyzed: result.systemIntegration.phases.dataCollection.competitorsAnalyzed,
              benchmarksCalculated: result.systemIntegration.phases.dataCollection.benchmarksCalculated
            },
            contentGeneration: {
              executionTime: result.systemIntegration.phases.contentGeneration.phaseExecutionTime,
              qualityScore: result.systemIntegration.phases.contentGeneration.qualityScore,
              wordCount: result.systemIntegration.phases.contentGeneration.wordCount,
              keywordDensity: result.systemIntegration.phases.contentGeneration.keywordDensity
            },
            qualityAssurance: {
              executionTime: result.systemIntegration.phases.qualityAssurance.phaseExecutionTime,
              qualityScore: result.systemIntegration.phases.qualityAssurance.qualityScore,
              optimizationsApplied: result.systemIntegration.phases.qualityAssurance.optimizationsApplied
            },
            finalIntegration: {
              executionTime: result.systemIntegration.phases.finalIntegration.phaseExecutionTime,
              qualityScore: result.systemIntegration.phases.finalIntegration.qualityScore,
              finalQualityScore: result.systemIntegration.phases.finalIntegration.finalQualityScore,
              benchmarkAccuracy: result.systemIntegration.phases.finalIntegration.benchmarkAccuracy,
              compatibilityScore: result.systemIntegration.phases.finalIntegration.compatibilityScore,
              deploymentReady: result.systemIntegration.phases.finalIntegration.deploymentReady
            }
          },
          systemReport: {
            sessionSummary: result.systemIntegration.systemReport.sessionSummary,
            qualityMetrics: result.systemIntegration.systemReport.qualityMetrics,
            performanceMetrics: result.systemIntegration.systemReport.performanceMetrics,
            deploymentAssessment: result.systemIntegration.systemReport.deploymentAssessment,
            systemHealth: result.systemIntegration.systemReport.systemHealth
          }
        },
        metadata: {
          totalProcessingTime: result.metadata.totalProcessingTime,
          componentsUsed: result.metadata.componentsUsed,
          optimizationsApplied: result.metadata.optimizationsApplied,
          finalQualityScore: result.metadata.finalQualityScore,
          systemEfficiency: result.metadata.systemEfficiency,
          universalCompatibility: result.metadata.universalCompatibility,
          deploymentReady: result.metadata.deploymentReady,
          generatedAt: result.metadata.generatedAt
        }
      }
    });

  } catch (error) {
    console.error('Unified precision system integration route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during unified precision system integration'
    });
  }
});

// Get system integration analytics and monitoring
router.get('/system-analytics', verifyToken, async (req, res) => {
  try {
    console.log(`Getting system analytics for user ${req.user.id}`);

    // Get system configuration and capabilities
    const systemConfig = precisionSystemIntegrator.systemConfig;
    const integrationPipeline = precisionSystemIntegrator.integrationPipeline;
    const monitoringSystem = precisionSystemIntegrator.monitoringSystem;
    const errorHandlingSystem = precisionSystemIntegrator.errorHandlingSystem;

    res.json({
      success: true,
      data: {
        systemConfiguration: {
          performance: systemConfig.performance,
          quality: systemConfig.quality,
          integration: systemConfig.integration,
          deployment: systemConfig.deployment
        },
        integrationPipeline: {
          phases: integrationPipeline.phases,
          parallelExecution: integrationPipeline.parallelExecution,
          fallbackStrategies: integrationPipeline.fallbackStrategies
        },
        monitoringCapabilities: {
          realTimeMetrics: monitoringSystem.realTimeMetrics,
          performanceAnalytics: {
            componentEfficiency: Object.keys(monitoringSystem.performanceAnalytics.componentEfficiency),
            bottleneckDetection: monitoringSystem.performanceAnalytics.bottleneckDetection,
            throughputMeasurement: monitoringSystem.performanceAnalytics.throughputMeasurement
          },
          qualityTracking: {
            continuousValidation: monitoringSystem.qualityTracking.continuousValidation,
            qualityGates: monitoringSystem.qualityTracking.qualityGates
          }
        },
        errorHandling: {
          errorClassification: errorHandlingSystem.errorClassification,
          recoveryStrategies: errorHandlingSystem.recoveryStrategies
        },
        supportedComponents: [
          'precisionSEOEngine',
          'multiKeywordOptimizer', 
          'eeatOptimizer',
          'nlpSemanticIntegrator',
          'grammarReadabilityOptimizer',
          'advancedSEOOptimizer',
          'contentQualityValidator',
          'competitorBenchmarkMatcher',
          'multiEngineCompatibilityTester'
        ],
        capabilities: {
          unifiedContentGeneration: true,
          realTimeMonitoring: true,
          systemIntegration: true,
          deploymentReadiness: true,
          performanceOptimization: true,
          qualityAssurance: true,
          errorRecovery: true,
          componentHealthTracking: true
        }
      }
    });

  } catch (error) {
    console.error('System analytics route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during system analytics retrieval'
    });
  }
});

// Optimize system performance comprehensively
router.post('/optimize-performance', verifyToken, async (req, res) => {
  try {
    const { 
      optimizationLevel = 'comprehensive',
      targetMetrics = {},
      enableAdvancedOptimizations = true,
      monitoringEnabled = true,
      autoScalingEnabled = true
    } = req.body;

    console.log(`Starting comprehensive performance optimization for user ${req.user.id}`);

    // Perform comprehensive system performance optimization
    const optimizationResult = await performanceOptimizer.optimizeSystemPerformance({
      optimizationLevel,
      targetMetrics: {
        responseTime: targetMetrics.responseTime || 500, // 500ms target
        throughput: targetMetrics.throughput || 500, // 500 RPS target
        cpuUtilization: targetMetrics.cpuUtilization || 60, // 60% CPU target
        memoryUtilization: targetMetrics.memoryUtilization || 70, // 70% memory target
        cacheHitRate: targetMetrics.cacheHitRate || 90, // 90% cache hit rate target
        errorRate: targetMetrics.errorRate || 0.1, // 0.1% error rate target
        ...targetMetrics
      },
      enableAdvancedOptimizations,
      monitoringEnabled,
      autoScalingEnabled
    });

    if (!optimizationResult.success) {
      return res.status(500).json({
        success: false,
        error: optimizationResult.error || 'Performance optimization failed'
      });
    }

    // Log successful optimization
    console.log(`Performance optimization completed successfully`, {
      sessionId: optimizationResult.optimization.sessionId,
      totalOptimizationTime: optimizationResult.metadata.totalOptimizationTime,
      optimizationsApplied: optimizationResult.metadata.optimizationsApplied,
      performanceGains: optimizationResult.metadata.performanceGains,
      systemHealthScore: optimizationResult.metadata.systemHealthScore
    });

    res.json({
      success: true,
      data: {
        optimization: {
          sessionId: optimizationResult.optimization.sessionId,
          phases: {
            systemAnalysis: {
              systemMetrics: optimizationResult.optimization.phases.systemAnalysis.systemMetrics,
              bottleneckAnalysis: optimizationResult.optimization.phases.systemAnalysis.bottleneckAnalysis,
              optimizationOpportunities: optimizationResult.optimization.phases.systemAnalysis.optimizationOpportunities,
              performanceScores: optimizationResult.optimization.phases.systemAnalysis.performanceScores
            },
            resourceOptimization: {
              optimizationsApplied: optimizationResult.optimization.phases.resourceOptimization.applied.length,
              resourceImprovements: optimizationResult.optimization.phases.resourceOptimization.resourceImprovements
            },
            cacheOptimization: {
              improvementsApplied: optimizationResult.optimization.phases.cacheOptimization.applied.length,
              cachePerformanceGains: optimizationResult.optimization.phases.cacheOptimization.cachePerformanceGains
            },
            memoryOptimization: {
              optimizationsApplied: optimizationResult.optimization.phases.memoryOptimization.applied.length,
              memoryEfficiencyGains: optimizationResult.optimization.phases.memoryOptimization.memoryEfficiencyGains
            },
            loadBalancingOptimization: {
              configurationsApplied: optimizationResult.optimization.phases.loadBalancingOptimization.applied.length,
              scalabilityImprovements: optimizationResult.optimization.phases.loadBalancingOptimization.scalabilityImprovements
            },
            bottleneckResolution: {
              resolutionsApplied: optimizationResult.optimization.phases.bottleneckResolution.applied.length,
              bottleneckImprovements: optimizationResult.optimization.phases.bottleneckResolution.bottleneckImprovements
            }
          },
          optimizationReport: {
            sessionSummary: optimizationResult.optimization.optimizationReport.sessionSummary,
            performanceImprovements: optimizationResult.optimization.optimizationReport.performanceImprovements,
            systemEfficiency: optimizationResult.optimization.optimizationReport.systemEfficiency,
            reliabilityEnhancements: optimizationResult.optimization.optimizationReport.reliabilityEnhancements,
            futureRecommendations: optimizationResult.optimization.optimizationReport.futureRecommendations
          }
        },
        metadata: {
          totalOptimizationTime: optimizationResult.metadata.totalOptimizationTime,
          optimizationsApplied: optimizationResult.metadata.optimizationsApplied,
          performanceGains: optimizationResult.metadata.performanceGains,
          systemHealthScore: optimizationResult.metadata.systemHealthScore,
          reliabilityScore: optimizationResult.metadata.reliabilityScore,
          optimizedAt: optimizationResult.metadata.optimizedAt
        }
      }
    });

  } catch (error) {
    console.error('Performance optimization route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during performance optimization'
    });
  }
});

// Get current system performance metrics
router.get('/performance-metrics', verifyToken, async (req, res) => {
  try {
    console.log(`Getting performance metrics for user ${req.user.id}`);

    // Capture current system metrics
    const systemMetrics = performanceOptimizer.captureSystemMetrics();
    const performanceBaseline = performanceOptimizer.capturePerformanceBaseline();
    const systemState = performanceOptimizer.captureSystemState();

    // Analyze current bottlenecks
    const bottleneckAnalysis = performanceOptimizer.analyzeBottlenecks(systemMetrics);
    
    // Calculate performance scores
    const performanceScores = performanceOptimizer.calculatePerformanceScores(systemMetrics);
    
    // Identify optimization opportunities
    const optimizationOpportunities = performanceOptimizer.identifyOptimizationOpportunities(systemMetrics, bottleneckAnalysis);

    res.json({
      success: true,
      data: {
        currentMetrics: {
          timestamp: systemMetrics.timestamp,
          cpu: {
            usage: systemMetrics.cpu.usage,
            loadAverage: systemMetrics.cpu.loadAverage,
            status: systemMetrics.cpu.usage > 80 ? 'high' : systemMetrics.cpu.usage > 60 ? 'moderate' : 'low'
          },
          memory: {
            usage: systemMetrics.memory.usage,
            heapUsed: Math.round(systemMetrics.memory.heapUsed / 1024 / 1024), // MB
            heapTotal: Math.round(systemMetrics.memory.heapTotal / 1024 / 1024), // MB
            rss: Math.round(systemMetrics.memory.rss / 1024 / 1024), // MB
            status: systemMetrics.memory.usage > 85 ? 'high' : systemMetrics.memory.usage > 70 ? 'moderate' : 'low'
          },
          system: {
            uptime: Math.round(performanceBaseline.uptime / 60), // Minutes
            platform: systemState.platform,
            cpus: systemState.cpus,
            nodeVersion: systemState.nodeVersion,
            pid: systemState.pid
          }
        },
        performanceScores: {
          cpu: performanceScores.cpu,
          memory: performanceScores.memory,
          io: performanceScores.io,
          network: performanceScores.network,
          overall: performanceScores.overall,
          grade: performanceScores.grade
        },
        bottleneckAnalysis: {
          totalBottlenecks: bottleneckAnalysis.totalBottlenecks,
          criticalBottlenecks: bottleneckAnalysis.criticalBottlenecks,
          warningBottlenecks: bottleneckAnalysis.warningBottlenecks,
          overallSeverity: bottleneckAnalysis.overallSeverity,
          bottlenecks: bottleneckAnalysis.bottlenecks.map(b => ({
            type: b.type,
            severity: b.severity,
            value: b.value,
            threshold: b.threshold,
            impact: b.impact,
            recommendations: b.recommendations.slice(0, 2) // First 2 recommendations
          }))
        },
        optimizationOpportunities: {
          totalOpportunities: optimizationOpportunities.totalOpportunities,
          highPriorityOpportunities: optimizationOpportunities.highPriorityOpportunities,
          totalEstimatedImprovement: optimizationOpportunities.totalEstimatedImprovement,
          topOpportunities: optimizationOpportunities.opportunities.slice(0, 3).map(o => ({
            category: o.category,
            priority: o.priority,
            description: o.description,
            estimatedImprovement: o.estimatedImprovement,
            implementationEffort: o.implementationEffort
          }))
        },
        healthStatus: {
          overall: performanceScores.overall >= 85 ? 'healthy' : performanceScores.overall >= 70 ? 'warning' : 'critical',
          needsOptimization: performanceScores.overall < 80 || bottleneckAnalysis.totalBottlenecks > 0,
          criticalIssues: bottleneckAnalysis.criticalBottlenecks,
          recommendedActions: bottleneckAnalysis.totalBottlenecks > 0 ? 
            ['Run performance optimization', 'Address critical bottlenecks', 'Monitor resource usage'] :
            ['System performing well', 'Continue monitoring', 'Consider proactive optimizations']
        },
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Performance metrics route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error while retrieving performance metrics'
    });
  }
});

// Get performance optimization configuration
router.get('/performance-config', verifyToken, async (req, res) => {
  try {
    console.log(`Getting performance configuration for user ${req.user.id}`);

    // Get performance optimization configuration
    const performanceMetrics = performanceOptimizer.performanceMetrics;
    const optimizationStrategies = performanceOptimizer.optimizationStrategies;
    const cacheSystem = performanceOptimizer.cacheSystem;
    const resourceManagement = performanceOptimizer.resourceManagement;

    res.json({
      success: true,
      data: {
        performanceMetrics: {
          realTimeMetrics: performanceMetrics.realTimeMetrics,
          performanceBaselines: performanceMetrics.performanceBaselines,
          alerting: performanceMetrics.alerting,
          historicalTracking: performanceMetrics.historicalTracking
        },
        optimizationStrategies: {
          responseTimeOptimization: {
            strategies: optimizationStrategies.responseTimeOptimization.strategies,
            targets: optimizationStrategies.responseTimeOptimization.targets
          },
          throughputOptimization: {
            strategies: optimizationStrategies.throughputOptimization.strategies,
            targets: optimizationStrategies.throughputOptimization.targets
          },
          resourceOptimization: {
            strategies: optimizationStrategies.resourceOptimization.strategies,
            targets: optimizationStrategies.resourceOptimization.targets
          },
          reliabilityOptimization: {
            strategies: optimizationStrategies.reliabilityOptimization.strategies,
            targets: optimizationStrategies.reliabilityOptimization.targets
          }
        },
        cacheConfiguration: {
          strategies: Object.keys(cacheSystem.cacheStrategies),
          optimization: cacheSystem.cacheOptimization,
          enabledFeatures: [
            ...(cacheSystem.cacheStrategies.inMemoryCache.enabled ? ['inMemoryCache'] : []),
            ...(cacheSystem.cacheStrategies.distributedCache.enabled ? ['distributedCache'] : []),
            ...(cacheSystem.cacheStrategies.contentCache.enabled ? ['contentCache'] : []),
            ...(cacheSystem.cacheStrategies.apiResponseCache.enabled ? ['apiResponseCache'] : [])
          ]
        },
        resourceManagement: {
          cpuManagement: resourceManagement.cpuManagement,
          memoryManagement: {
            pooling: resourceManagement.memoryManagement.pooling,
            garbageCollection: resourceManagement.memoryManagement.garbageCollection,
            leakDetection: resourceManagement.memoryManagement.leakDetection
          },
          connectionManagement: resourceManagement.connectionManagement,
          ioOptimization: resourceManagement.ioOptimization
        },
        capabilities: {
          realTimeMonitoring: true,
          automaticOptimization: true,
          bottleneckDetection: true,
          resourceOptimization: true,
          cacheOptimization: true,
          memoryManagement: true,
          loadBalancing: true,
          autoScaling: true,
          performanceReporting: true
        },
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Performance configuration route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error while retrieving performance configuration'
    });
  }
});

// Get precision generation status/health check
router.get('/health', async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      services: {
        competitorAnalyzer: 'operational',
        keywordDensityCalculator: 'operational',
        headingAnalyzer: 'operational',
        contentGenerator: 'operational',
        multiKeywordOptimizer: 'operational',
        eeatOptimizer: 'operational',
        nlpSemanticIntegrator: 'operational',
        grammarReadabilityOptimizer: 'operational',
        advancedSEOOptimizer: 'operational',
        contentQualityValidator: 'operational',
        competitorBenchmarkMatcher: 'operational',
        multiEngineCompatibilityTester: 'operational',
        precisionSystemIntegrator: 'operational',
        performanceOptimizer: 'operational'
      },
      features: {
        precisionWordCount: 'enabled',
        surgicalKeywordDensity: 'enabled',
        multiKeywordOptimization: 'enabled',
        semanticIntegration: 'enabled',
        eeatOptimization: 'enabled',
        competitorBenchmarking: 'enabled',
        lsiKeywordIntegration: 'enabled',
        entityOptimization: 'enabled',
        nlpSemanticIntegration: 'enabled',
        topicalAuthorityMapping: 'enabled',
        grammarOptimization: 'enabled',
        readabilityOptimization: 'enabled',
        advancedSEOOptimization: 'enabled',
        multiEngineOptimization: 'enabled',
        serpAnalysis: 'enabled',
        technicalSEOOptimization: 'enabled',
        richResultsOptimization: 'enabled',
        voiceSearchOptimization: 'enabled',
        mobileOptimization: 'enabled',
        contentQualityValidation: 'enabled',
        precisionCompliance: 'enabled',
        qualityMetrics: 'enabled',
        comprehensiveValidation: 'enabled',
        competitorBenchmarkMatching: 'enabled',
        surgicalPrecisionAdjustments: 'enabled',
        benchmarkComplianceAnalysis: 'enabled',
        perfectMatchOptimization: 'enabled',
        multiEngineCompatibilityTesting: 'enabled',
        aiLlmCompatibility: 'enabled',
        enginePerformanceAnalysis: 'enabled',
        universalCompatibilityValidation: 'enabled',
        unifiedSystemIntegration: 'enabled',
        realTimeSystemMonitoring: 'enabled',
        systemPerformanceAnalytics: 'enabled',
        componentHealthTracking: 'enabled',
        deploymentReadinessAssessment: 'enabled',
        systemErrorRecovery: 'enabled',
        comprehensivePerformanceOptimization: 'enabled',
        realTimePerformanceMonitoring: 'enabled',
        automaticBottleneckDetection: 'enabled',
        resourceUtilizationOptimization: 'enabled',
        cachePerformanceOptimization: 'enabled',
        memoryManagementOptimization: 'enabled',
        loadBalancingOptimization: 'enabled',
        autoScalingConfiguration: 'enabled',
        systemReliabilityEnhancement: 'enabled'
      },
      rateLimit: {
        precisionGeneration: '5 per hour',
        analysis: '20 per hour'
      },
      timestamp: new Date().toISOString()
    };

    res.json({
      success: true,
      data: health
    });

  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      success: false,
      error: 'Health check failed'
    });
  }
});

module.exports = router;