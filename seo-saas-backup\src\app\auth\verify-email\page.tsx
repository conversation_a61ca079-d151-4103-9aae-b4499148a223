'use client';

import * as React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircleIcon, EnvelopeIcon } from "@heroicons/react/24/outline";

export default function VerifyEmailPage() {
  const [resendLoading, setResendLoading] = React.useState(false);
  const [resendSuccess, setResendSuccess] = React.useState(false);

  const handleResendVerification = async () => {
    setResendLoading(true);
    try {
      const { createSupabaseComponentClient } = await import('@/lib/supabase');
      const supabase = createSupabaseComponentClient();
      
      // Get the current user's email from localStorage or URLSearchParams
      const email = localStorage.getItem('pending_verification_email') || 
                   new URLSearchParams(window.location.search).get('email');
      
      if (!email) {
        throw new Error('Email address not found. Please try signing up again.');
      }
      
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });
      
      if (error) {
        throw new Error(error.message);
      }
      
      setResendSuccess(true);
    } catch (error: any) {
      console.error('Failed to resend verification email:', error);
      // You could also show the error to the user here
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-block">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">SEO SAAS</h1>
          </Link>
          <p className="text-gray-600">Email verification required</p>
        </div>

        {/* Verification Card */}
        <Card className="shadow-lg">
          <CardHeader className="space-y-1 text-center">
            <div className="mx-auto mb-4 flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full">
              <EnvelopeIcon className="h-8 w-8 text-blue-600" />
            </div>
            <CardTitle className="text-2xl font-bold">Check Your Email</CardTitle>
            <CardDescription>
              We've sent a verification link to your email address
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center space-y-4">
              <p className="text-sm text-gray-600">
                To complete your registration, please click the verification link in the email we just sent you.
              </p>
              
              <div className="space-y-2">
                <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                  <CheckCircleIcon className="h-4 w-4" />
                  <span>Check your inbox</span>
                </div>
                <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                  <CheckCircleIcon className="h-4 w-4" />
                  <span>Check your spam folder</span>
                </div>
                <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                  <CheckCircleIcon className="h-4 w-4" />
                  <span>Click the verification link</span>
                </div>
              </div>
            </div>

            {/* Resend Button */}
            <div className="space-y-4">
              {resendSuccess ? (
                <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-sm text-green-600 text-center">
                    Verification email sent successfully!
                  </p>
                </div>
              ) : (
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-3">
                    Didn't receive the email?
                  </p>
                  <Button 
                    variant="outline" 
                    onClick={handleResendVerification}
                    loading={resendLoading}
                    disabled={resendLoading}
                    className="w-full"
                  >
                    {resendLoading ? 'Sending...' : 'Resend Verification Email'}
                  </Button>
                </div>
              )}
            </div>

            {/* Back to Sign In */}
            <div className="text-center pt-4 border-t">
              <p className="text-sm text-gray-600">
                Already verified?{' '}
                <Link
                  href="/auth/signin"
                  className="font-medium text-blue-600 hover:text-blue-500"
                >
                  Sign in to your account
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-xs text-gray-500">
            Having trouble? Contact our{' '}
            <Link href="/support" className="text-blue-600 hover:text-blue-500">
              support team
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}