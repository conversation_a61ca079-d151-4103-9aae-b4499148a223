/* Premium Features Design System */
/* Modern, visually stunning design for SEO SaaS features */

/* Feature Section Badge */
.section-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 2rem;
    margin-bottom: 1rem;
}

.badge-text {
    font-size: 0.875rem;
    font-weight: 600;
    background: linear-gradient(135deg, #3b82f6 0%, #7c3aed 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Feature Categories Navigation */
.feature-categories-nav {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 3rem 0;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.category-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: none;
    border-radius: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.category-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #3b82f6 0%, #7c3aed 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.category-tab:hover::before,
.category-tab.active::before {
    opacity: 1;
}

.category-tab:hover,
.category-tab.active {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.tab-icon {
    font-size: 1.25rem;
}

/* Features Grid Container */
.features-grid-container {
    position: relative;
    min-height: 600px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
    position: absolute;
    width: 100%;
}

.features-grid.active {
    opacity: 1;
    transform: translateY(0);
    position: relative;
}

/* Premium Feature Cards */
.feature-card.premium-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 1.5rem;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 1px 3px rgba(0, 0, 0, 0.1);
}

.feature-card.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6 0%, #7c3aed 50%, #06b6d4 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card.premium-card:hover::before {
    opacity: 1;
}

.feature-card.premium-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 16px rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
}

/* Feature Header */
.feature-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.feature-icon {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #3b82f6 0%, #7c3aed 100%);
    color: white;
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 30px rgba(59, 130, 246, 0.4);
}

.feature-icon svg {
    width: 1.75rem;
    height: 1.75rem;
}

/* Feature Badges */
.feature-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.feature-badge {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.new-badge {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.global-badge {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.expert-badge {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

/* Feature Content */
.feature-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.feature-description {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

/* Feature List */
.feature-list {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    color: #4b5563;
    font-weight: 500;
}

.check-icon {
    width: 1.25rem;
    height: 1.25rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    flex-shrink: 0;
}

/* Feature Stats */
.feature-stats {
    display: flex;
    gap: 1.5rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(229, 231, 235, 0.5);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #3b82f6 0%, #7c3aed 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-top: 0.25rem;
}

/* Features CTA Section */
.features-cta-section {
    margin-top: 4rem;
    text-align: center;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(124, 58, 237, 0.05) 100%);
    border-radius: 2rem;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.features-cta-title {
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1rem;
}

.features-cta-description {
    color: #6b7280;
    font-size: 1.125rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .feature-categories-nav {
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .category-tab {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .feature-card.premium-card {
        padding: 1.5rem;
    }
    
    .feature-stats {
        gap: 1rem;
    }
    
    .features-cta-section {
        padding: 2rem 1rem;
        margin-top: 2rem;
    }
    
    .features-cta-title {
        font-size: 1.5rem;
    }
}

/* Features Page Specific Styles */
.features-hero {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(124, 58, 237, 0.05) 100%);
    padding: 8rem 0 6rem;
    position: relative;
    overflow: hidden;
}

.features-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(59,130,246,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.features-hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.features-hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: #1f2937;
}

.features-hero-description {
    font-size: 1.25rem;
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.features-hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.hero-stat {
    text-align: center;
}

.hero-stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #3b82f6 0%, #7c3aed 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.hero-stat-label {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-top: 0.5rem;
}

.features-hero-cta {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Feature Categories */
.features-categories {
    margin-top: 4rem;
}

.feature-category {
    margin-bottom: 4rem;
    padding: 3rem;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.category-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid rgba(59, 130, 246, 0.1);
}

.category-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, #3b82f6 0%, #7c3aed 100%);
    border-radius: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.category-emoji {
    font-size: 2rem;
}

.category-title {
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.category-description {
    color: #6b7280;
    font-size: 1.125rem;
    line-height: 1.6;
}

/* Feature Items */
.category-features {
    display: grid;
    gap: 2rem;
}

.feature-item {
    padding: 2rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 0.8);
}

.feature-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.feature-item-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.feature-item-description {
    color: #4b5563;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.feature-item-benefits {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
}

.feature-item-benefits li {
    padding: 0.5rem 0;
    color: #374151;
    font-weight: 500;
    position: relative;
    padding-left: 1.5rem;
}

.feature-item-benefits li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

.feature-item-stats {
    display: flex;
    gap: 1.5rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(229, 231, 235, 0.5);
}

.feature-stat {
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #3b82f6;
}

/* Animation Enhancements */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.feature-icon {
    animation: float 6s ease-in-out infinite;
}

.feature-card:nth-child(2n) .feature-icon {
    animation-delay: -2s;
}

.feature-card:nth-child(3n) .feature-icon {
    animation-delay: -4s;
}

/* Responsive Design for Features Page */
@media (max-width: 768px) {
    .features-hero {
        padding: 6rem 0 4rem;
    }

    .features-hero-title {
        font-size: 2.5rem;
    }

    .features-hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        padding: 1.5rem;
    }

    .hero-stat-number {
        font-size: 2rem;
    }

    .features-hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .feature-category {
        padding: 2rem 1.5rem;
    }

    .category-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .category-title {
        font-size: 1.5rem;
    }

    .feature-item-stats {
        flex-direction: column;
        gap: 0.5rem;
    }
}
