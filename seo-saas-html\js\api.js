// API integration utilities for SEO Pro
// This file handles all API calls through the secure backend

class APIClient {
    constructor() {
        this.baseURL = CONFIG.APIS.BACKEND.BASE_URL;
        this.endpoints = CONFIG.APIS.BACKEND.ENDPOINTS;
        this.authToken = localStorage.getItem('seo_pro_auth_token');
        this.requestQueue = [];
        this.isProcessing = false;
    }

    // Set authentication token
    setAuthToken(token) {
        this.authToken = token;
        if (token) {
            localStorage.setItem('seo_pro_auth_token', token);
        } else {
            localStorage.removeItem('seo_pro_auth_token');
        }
    }

    // Get authentication headers
    getAuthHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (this.authToken) {
            headers['Authorization'] = `Bearer ${this.authToken}`;
        }
        
        return headers;
    }

    // Generic API request method with error handling and retries
    async makeRequest(endpoint, options = {}, retries = 3) {
        const url = `${this.baseURL}${endpoint}`;
        const defaultOptions = {
            method: 'GET',
            headers: this.getAuthHeaders(),
            ...options
        };

        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                const response = await fetch(url, defaultOptions);
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    
                    // Handle specific error codes
                    if (response.status === 401) {
                        this.setAuthToken(null);
                        window.location.href = '/login.html';
                        throw new Error('Authentication required');
                    }
                    
                    if (response.status === 429) {
                        throw new Error(errorData.error || 'Rate limit exceeded. Please try again later.');
                    }
                    
                    throw new Error(`HTTP ${response.status}: ${errorData.error || response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                console.error(`API request attempt ${attempt} failed:`, error);
                
                if (attempt === retries) {
                    throw error;
                }
                
                // Wait before retrying (exponential backoff)
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
            }
        }
    }

    // Authentication methods
    async login(email, password) {
        try {
            const response = await this.makeRequest(this.endpoints.LOGIN, {
                method: 'POST',
                body: JSON.stringify({ email, password })
            });

            if (response.token) {
                this.setAuthToken(response.token);
            }

            return {
                success: true,
                user: response.user,
                token: response.token
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async register(email, password, fullName) {
        try {
            const response = await this.makeRequest(this.endpoints.REGISTER, {
                method: 'POST',
                body: JSON.stringify({ email, password, full_name: fullName })
            });

            if (response.token) {
                this.setAuthToken(response.token);
            }

            return {
                success: true,
                user: response.user,
                token: response.token
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async verifyAuth() {
        try {
            const response = await this.makeRequest(this.endpoints.VERIFY);
            return {
                success: true,
                user: response.user
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Content generation methods
    async generateContent(params) {
        try {
            const response = await this.makeRequest(this.endpoints.GENERATE_CONTENT, {
                method: 'POST',
                body: JSON.stringify(params)
            });

            return {
                success: true,
                content: response.content,
                id: response.id,
                metadata: response.metadata
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Generate SEO-optimized content with specific parameters
    async generateSEOContent(params) {
        const {
            keywords,
            industry,
            contentType,
            tone,
            wordCount,
            targetAudience,
            location,
            searchEngine,
            intent,
            competitorData
        } = params;

        const keyword = Array.isArray(keywords) ? keywords[0] : keywords;
        
        return await this.generateContent({
            keyword,
            contentType,
            tone,
            wordCount,
            industry,
            location,
            searchEngine,
            intent,
            competitorData
        });
    }

    // Get user's generated content
    async getContentList(page = 1, limit = 10) {
        try {
            const response = await this.makeRequest(
                `${this.endpoints.LIST_CONTENT}?page=${page}&limit=${limit}`
            );

            return {
                success: true,
                content: response.content,
                pagination: response.pagination
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get single content item
    async getContent(contentId) {
        try {
            const response = await this.makeRequest(
                `${this.endpoints.GET_CONTENT}/${contentId}`
            );

            return {
                success: true,
                content: response
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Competitor analysis methods
    async analyzeCompetitors(keyword, options = {}) {
        try {
            const response = await this.makeRequest(this.endpoints.COMPETITOR_ANALYSIS, {
                method: 'POST',
                body: JSON.stringify({
                    keyword,
                    location: options.location || 'United States',
                    searchEngine: options.searchEngine || 'google'
                })
            });

            return {
                success: true,
                keyword: response.keyword,
                competitors: response.competitors,
                averages: response.averages,
                analysisId: response.analysisId
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Content analysis methods
    async analyzeContent(content, targetKeyword) {
        try {
            const response = await this.makeRequest(this.endpoints.CONTENT_ANALYSIS, {
                method: 'POST',
                body: JSON.stringify({
                    content,
                    targetKeyword
                })
            });

            return {
                success: true,
                analysis: response.analysis,
                seoScore: response.seoScore,
                recommendations: response.recommendations
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // SEO optimization methods
    async generateMetaTags(content, keyword, options = {}) {
        try {
            const response = await this.makeRequest(this.endpoints.SEO_META_TAGS, {
                method: 'POST',
                body: JSON.stringify({
                    content,
                    keyword,
                    ...options
                })
            });

            return {
                success: true,
                metaTags: response.metaTags,
                generationId: response.generationId
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async generateMetaTagsBulk(requests) {
        try {
            const response = await this.makeRequest(this.endpoints.SEO_META_TAGS_BULK, {
                method: 'POST',
                body: JSON.stringify({ requests })
            });

            return {
                success: true,
                results: response.results,
                generationId: response.generationId
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async optimizeTitle(content, keyword, options = {}) {
        try {
            const response = await this.makeRequest(this.endpoints.SEO_TITLE_OPTIMIZE, {
                method: 'POST',
                body: JSON.stringify({
                    content,
                    keyword,
                    ...options
                })
            });

            return {
                success: true,
                title: response.title
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async optimizeDescription(content, keyword, options = {}) {
        try {
            const response = await this.makeRequest(this.endpoints.SEO_DESCRIPTION_OPTIMIZE, {
                method: 'POST',
                body: JSON.stringify({
                    content,
                    keyword,
                    ...options
                })
            });

            return {
                success: true,
                description: response.description
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async generateSchemaMarkup(content, keyword, options = {}) {
        try {
            const response = await this.makeRequest(this.endpoints.SEO_SCHEMA_MARKUP, {
                method: 'POST',
                body: JSON.stringify({
                    content,
                    keyword,
                    ...options
                })
            });

            return {
                success: true,
                schema: response.schema
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async generateInternalLinkSuggestions(content, existingPages = [], options = {}) {
        try {
            const response = await this.makeRequest(this.endpoints.SEO_INTERNAL_LINKS, {
                method: 'POST',
                body: JSON.stringify({
                    content,
                    existingPages,
                    options
                })
            });

            return {
                success: true,
                suggestions: response.suggestions,
                linkablePhrases: response.linkablePhrases,
                currentLinkAnalysis: response.currentLinkAnalysis,
                recommendations: response.recommendations,
                analysisId: response.analysisId,
                metadata: response.metadata
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async generateExternalLinkRecommendations(content, keyword, options = {}) {
        try {
            const response = await this.makeRequest(this.endpoints.SEO_EXTERNAL_LINKS, {
                method: 'POST',
                body: JSON.stringify({
                    content,
                    keyword,
                    options
                })
            });

            return {
                success: true,
                keyword: response.keyword,
                recommendations: response.recommendations,
                linkOpportunities: response.linkOpportunities,
                existingLinkAnalysis: response.existingLinkAnalysis,
                optimizationTips: response.optimizationTips,
                analysisId: response.analysisId,
                metadata: response.metadata
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async analyzeInternalLinks(content) {
        try {
            const response = await this.makeRequest(this.endpoints.SEO_ANALYZE_LINKS, {
                method: 'POST',
                body: JSON.stringify({ content })
            });

            return {
                success: true,
                analysis: response.analysis
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Project management methods
    async createProject(name, description = '', keywords = [], settings = {}) {
        try {
            const response = await this.makeRequest(this.endpoints.PROJECTS, {
                method: 'POST',
                body: JSON.stringify({
                    name,
                    description,
                    keywords,
                    settings
                })
            });

            return {
                success: true,
                project: response.project
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getProjects(options = {}) {
        try {
            const params = new URLSearchParams({
                page: options.page || 1,
                limit: options.limit || 20,
                status: options.status || 'all',
                search: options.search || '',
                sort: options.sort || 'updated_at',
                order: options.order || 'desc'
            });

            const response = await this.makeRequest(`${this.endpoints.PROJECTS}?${params}`);

            return {
                success: true,
                projects: response.projects,
                pagination: response.pagination
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getProject(projectId) {
        try {
            const response = await this.makeRequest(`${this.endpoints.PROJECTS}/${projectId}`);

            return {
                success: true,
                project: response.project
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async updateProject(projectId, updates) {
        try {
            const response = await this.makeRequest(`${this.endpoints.PROJECTS}/${projectId}`, {
                method: 'PUT',
                body: JSON.stringify(updates)
            });

            return {
                success: true,
                project: response.project
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async deleteProject(projectId) {
        try {
            const response = await this.makeRequest(`${this.endpoints.PROJECTS}/${projectId}`, {
                method: 'DELETE'
            });

            return {
                success: true,
                message: response.message
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async addProjectContent(projectId, contentData) {
        try {
            const response = await this.makeRequest(`${this.endpoints.PROJECTS}/${projectId}/content`, {
                method: 'POST',
                body: JSON.stringify(contentData)
            });

            return {
                success: true,
                content: response.content
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getProjectContent(projectId, options = {}) {
        try {
            const params = new URLSearchParams({
                content_type: options.content_type || 'all',
                page: options.page || 1,
                limit: options.limit || 20
            });

            const response = await this.makeRequest(`${this.endpoints.PROJECTS}/${projectId}/content?${params}`);

            return {
                success: true,
                content: response.content,
                pagination: response.pagination
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async updateProjectContent(projectId, contentId, updates) {
        try {
            const response = await this.makeRequest(`${this.endpoints.PROJECTS}/${projectId}/content/${contentId}`, {
                method: 'PUT',
                body: JSON.stringify(updates)
            });

            return {
                success: true,
                content: response.content
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async deleteProjectContent(projectId, contentId) {
        try {
            const response = await this.makeRequest(`${this.endpoints.PROJECTS}/${projectId}/content/${contentId}`, {
                method: 'DELETE'
            });

            return {
                success: true,
                message: response.message
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async exportProject(projectId, format = 'json') {
        try {
            const response = await fetch(`${this.baseURL}${this.endpoints.PROJECTS}/${projectId}/export?format=${format}`, {
                method: 'GET',
                headers: this.getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error('Export failed');
            }

            // Get filename from Content-Disposition header
            const contentDisposition = response.headers.get('Content-Disposition');
            const filenameMatch = contentDisposition?.match(/filename="(.+)"/);
            const filename = filenameMatch ? filenameMatch[1] : `project-export.${format}`;

            // Download the file
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            return {
                success: true,
                filename
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async duplicateProject(projectId, newName) {
        try {
            const response = await this.makeRequest(`${this.endpoints.PROJECTS}/${projectId}/duplicate`, {
                method: 'POST',
                body: JSON.stringify({ name: newName })
            });

            return {
                success: true,
                project: response.project
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Language management methods
    async getSupportedLanguages() {
        try {
            const response = await this.makeRequest(this.endpoints.LANGUAGES);

            return {
                success: true,
                languages: response.languages
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getLanguageDetails(languageCode) {
        try {
            const response = await this.makeRequest(`${this.endpoints.LANGUAGES}/${languageCode}`);

            return {
                success: true,
                language: response.language
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async generateLanguageContent(content, keyword, language, options = {}) {
        try {
            const response = await this.makeRequest(this.endpoints.LANGUAGE_GENERATE, {
                method: 'POST',
                body: JSON.stringify({
                    content,
                    keyword,
                    language,
                    ...options
                })
            });

            return {
                success: true,
                content: response.content,
                language: response.language,
                generationId: response.generationId,
                metadata: response.metadata
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async translateContent(content, fromLanguage, toLanguage, keyword, maintainSEO = true) {
        try {
            const response = await this.makeRequest(this.endpoints.LANGUAGE_TRANSLATE, {
                method: 'POST',
                body: JSON.stringify({
                    content,
                    fromLanguage,
                    toLanguage,
                    keyword,
                    maintainSEO
                })
            });

            return {
                success: true,
                translatedContent: response.translatedContent,
                fromLanguage: response.fromLanguage,
                toLanguage: response.toLanguage,
                translationId: response.translationId,
                metadata: response.metadata
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getLanguageSEOGuidelines(languageCode) {
        try {
            const url = this.endpoints.LANGUAGE_GUIDELINES.replace('{code}', languageCode);
            const response = await this.makeRequest(url);

            return {
                success: true,
                language: response.language,
                seoGuidelines: response.seoGuidelines,
                contentPatterns: response.contentPatterns
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Bulk processing methods
    async uploadKeywordFile(file) {
        try {
            const formData = new FormData();
            formData.append('keywordFile', file);

            const response = await fetch(`${this.baseURL}${this.endpoints.BULK_KEYWORDS_UPLOAD}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                },
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || 'File upload failed');
            }

            return await response.json();
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async parseKeywordText(keywordText, defaultSettings = {}) {
        try {
            const response = await this.makeRequest(this.endpoints.BULK_KEYWORDS_PARSE, {
                method: 'POST',
                body: JSON.stringify({
                    keywordText,
                    defaultSettings
                })
            });

            return {
                success: true,
                keywords: response.keywords,
                totalKeywords: response.totalKeywords
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async startBulkContentGeneration(keywords, settings = {}, projectId = null) {
        try {
            const response = await this.makeRequest(this.endpoints.BULK_CONTENT_GENERATE, {
                method: 'POST',
                body: JSON.stringify({
                    keywords,
                    settings,
                    projectId
                })
            });

            return {
                success: true,
                operationId: response.operationId,
                totalKeywords: response.totalKeywords,
                estimatedCompletion: response.estimatedCompletion,
                message: response.message
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async startBulkMetaGeneration(keywords, settings = {}, projectId = null) {
        try {
            const response = await this.makeRequest(this.endpoints.BULK_META_GENERATE, {
                method: 'POST',
                body: JSON.stringify({
                    keywords,
                    settings,
                    projectId
                })
            });

            return {
                success: true,
                operationId: response.operationId,
                totalKeywords: response.totalKeywords,
                estimatedCompletion: response.estimatedCompletion,
                message: response.message
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async startBulkCompetitorAnalysis(keywords, settings = {}, projectId = null) {
        try {
            const response = await this.makeRequest(this.endpoints.BULK_COMPETITOR_GENERATE, {
                method: 'POST',
                body: JSON.stringify({
                    keywords,
                    settings,
                    projectId
                })
            });

            return {
                success: true,
                operationId: response.operationId,
                totalKeywords: response.totalKeywords,
                estimatedCompletion: response.estimatedCompletion,
                message: response.message
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getBulkOperationStatus(operationId) {
        try {
            const url = this.endpoints.BULK_OPERATION_STATUS.replace('{id}', operationId);
            const response = await this.makeRequest(url);

            return {
                success: true,
                operation: response.operation
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getBulkOperationResults(operationId, page = 1, limit = 50) {
        try {
            const url = this.endpoints.BULK_OPERATION_RESULTS.replace('{id}', operationId);
            const response = await this.makeRequest(`${url}?page=${page}&limit=${limit}`);

            return {
                success: true,
                results: response.results,
                pagination: response.pagination
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async cancelBulkOperation(operationId) {
        try {
            const url = this.endpoints.BULK_OPERATION_CANCEL.replace('{id}', operationId);
            const response = await this.makeRequest(url, {
                method: 'POST'
            });

            return {
                success: true,
                message: response.message
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getBulkOperations(page = 1, limit = 20) {
        try {
            const response = await this.makeRequest(`${this.endpoints.BULK_OPERATIONS}?page=${page}&limit=${limit}`);

            return {
                success: true,
                operations: response.operations,
                pagination: response.pagination
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async createBulkExport(operationId, format = 'zip') {
        try {
            const url = this.endpoints.BULK_OPERATION_EXPORT.replace('{id}', operationId);
            const response = await this.makeRequest(url, {
                method: 'POST',
                body: JSON.stringify({ format })
            });

            return {
                success: true,
                exportId: response.exportId,
                fileSize: response.fileSize,
                expiresAt: response.expiresAt,
                message: response.message
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async downloadBulkExport(exportId) {
        try {
            const url = this.endpoints.BULK_EXPORT_DOWNLOAD.replace('{id}', exportId);
            const response = await fetch(`${this.baseURL}${url}`, {
                method: 'GET',
                headers: this.getAuthHeaders()
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || 'Download failed');
            }

            // Get filename from Content-Disposition header
            const contentDisposition = response.headers.get('Content-Disposition');
            const filenameMatch = contentDisposition?.match(/filename="(.+)"/);
            const filename = filenameMatch ? filenameMatch[1] : `bulk-export-${exportId}`;

            // Download the file
            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(downloadUrl);
            document.body.removeChild(a);

            return {
                success: true,
                filename
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getBulkExports(page = 1, limit = 20) {
        try {
            const response = await this.makeRequest(`${this.endpoints.BULK_EXPORTS}?page=${page}&limit=${limit}`);

            return {
                success: true,
                exports: response.exports,
                pagination: response.pagination
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getBulkStats() {
        try {
            const response = await this.makeRequest(this.endpoints.BULK_STATS);

            return {
                success: true,
                stats: response.stats
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Export methods
    async exportContent(contentId, format = 'html', options = {}) {
        try {
            let endpoint;
            switch (format.toLowerCase()) {
                case 'html':
                    endpoint = this.endpoints.EXPORT_HTML;
                    break;
                case 'markdown':
                case 'md':
                    endpoint = this.endpoints.EXPORT_MARKDOWN;
                    break;
                case 'json':
                    endpoint = this.endpoints.EXPORT_JSON;
                    break;
                default:
                    throw new Error('Unsupported export format');
            }

            const response = await fetch(`${this.baseURL}${endpoint}`, {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify({
                    contentId,
                    ...options
                })
            });

            if (!response.ok) {
                throw new Error('Export failed');
            }

            // Get filename from Content-Disposition header
            const contentDisposition = response.headers.get('Content-Disposition');
            const filenameMatch = contentDisposition?.match(/filename="(.+)"/);
            const filename = filenameMatch ? filenameMatch[1] : `export.${format}`;

            // Download the file
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            return {
                success: true,
                filename
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Utility methods
    async getCurrentUser() {
        if (!this.authToken) {
            return { success: false, error: 'Not authenticated' };
        }

        return await this.verifyAuth();
    }

    isAuthenticated() {
        return !!this.authToken;
    }

    logout() {
        this.setAuthToken(null);
        window.location.href = '/login.html';
    }

    // Rate limiting and queue management
    async queueRequest(requestFn, priority = 'normal') {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({
                fn: requestFn,
                priority,
                resolve,
                reject
            });
            
            this.processQueue();
        });
    }

    async processQueue() {
        if (this.isProcessing || this.requestQueue.length === 0) return;
        
        this.isProcessing = true;
        
        // Sort by priority
        this.requestQueue.sort((a, b) => {
            const priorities = { high: 3, normal: 2, low: 1 };
            return priorities[b.priority] - priorities[a.priority];
        });
        
        const request = this.requestQueue.shift();
        
        try {
            const result = await request.fn();
            request.resolve(result);
        } catch (error) {
            request.reject(error);
        }
        
        this.isProcessing = false;
        
        // Process next request after a delay
        if (this.requestQueue.length > 0) {
            setTimeout(() => this.processQueue(), 1000);
        }
    }
}

// Create global API client instance
let apiClient;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    apiClient = new APIClient();
    window.apiClient = apiClient;
    
    // Check authentication status on page load
    if (apiClient.isAuthenticated() && 
        !window.location.pathname.includes('login.html') && 
        !window.location.pathname.includes('register.html')) {
        apiClient.verifyAuth().then(result => {
            if (!result.success) {
                console.warn('Authentication verification failed:', result.error);
                // Optionally redirect to login
                // window.location.href = '/login.html';
            }
        });
    }
    
    console.log('API client initialized');
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIClient;
}