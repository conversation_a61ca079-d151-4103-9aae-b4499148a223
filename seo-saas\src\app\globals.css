@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
}

html,
body {
  height: 100%;
}

body {
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

/* Modern Color System */
:root {
  /* Brand Colors - Unified HSL System */
  --primary: 214 100% 50%;
  --primary-foreground: 0 0% 100%;
  --primary-50: 214 100% 97%;
  --primary-100: 214 100% 94%;
  --primary-500: 214 100% 50%;
  --primary-600: 214 100% 45%;
  --primary-700: 214 100% 40%;
  --primary-900: 214 100% 20%;

  /* Semantic Colors */
  --success: 142 76% 36%;
  --success-foreground: 0 0% 100%;
  --warning: 38 92% 50%;
  --warning-foreground: 0 0% 100%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 0 0% 100%;

  /* Neutral System */
  --background: 0 0% 100%;
  --foreground: 222 84% 5%;
  --card: 0 0% 100%;
  --card-foreground: 222 84% 5%;
  --popover: 0 0% 100%;
  --popover-foreground: 222 84% 5%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222 84% 5%;
  --muted: 210 40% 96%;
  --muted-foreground: 215 16% 47%;
  --accent: 210 40% 96%;
  --accent-foreground: 222 84% 5%;
  --border: 214 32% 91%;
  --input: 214 32% 91%;
  --ring: 214 100% 50%;
  --radius: 0.75rem;

  /* Animation Variables */
  --animation-fast: 150ms;
  --animation-normal: 250ms;
  --animation-slow: 350ms;
  
  /* Modern Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --background: 222 84% 5%;
    --foreground: 210 40% 98%;
    --card: 222 84% 5%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 5%;
    --popover-foreground: 210 40% 98%;
    --primary: 214 100% 50%;
    --primary-foreground: 222 84% 5%;
    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;
    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 214 100% 50%;
  }
}

/* Base Styles */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Modern Animations */
@layer utilities {
  /* Smooth transitions */
  .transition-smooth {
    transition: all var(--animation-normal) cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Glass morphism effect */
  .glass {
    backdrop-filter: blur(10px) saturate(180%);
    background-color: rgba(255, 255, 255, 0.72);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  .glass-dark {
    backdrop-filter: blur(10px) saturate(180%);
    background-color: rgba(17, 25, 40, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }

  /* Modern shadows */
  .shadow-modern {
    box-shadow: var(--shadow);
  }

  .shadow-modern-lg {
    box-shadow: var(--shadow-lg);
  }

  .shadow-modern-xl {
    box-shadow: var(--shadow-xl);
  }

  /* 3D transform effects */
  .transform-3d {
    transform-style: preserve-3d;
  }

  .card-hover {
    transition: transform var(--animation-normal) ease, box-shadow var(--animation-normal) ease;
  }

  .card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
  }

  /* Gradient animations */
  .gradient-animation {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }

  @keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Floating animation */
  .floating {
    animation: floating 6s ease-in-out infinite;
  }

  @keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  /* Modern loading spinner */
  .spinner {
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-left: 3px solid hsl(var(--primary));
    border-radius: 50%;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Smooth reveal animations */
  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(60px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Shimmer animation for skeleton loading */
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Enhanced skeleton styles */
  .skeleton-shimmer {
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 20%,
      rgba(255, 255, 255, 0.5) 60%,
      rgba(255, 255, 255, 0)
    );
    background-size: 200% auto;
    animation: shimmer 2s infinite;
  }

  /* Scale on hover */
  .scale-hover {
    transition: transform var(--animation-normal) ease;
  }

  .scale-hover:hover {
    transform: scale(1.05);
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Grid background pattern */
.bg-grid-slate-900\/\[0\.04\] {
  background-image: 
    linear-gradient(to right, rgb(15 23 42 / 0.04) 1px, transparent 1px),
    linear-gradient(to bottom, rgb(15 23 42 / 0.04) 1px, transparent 1px);
}

.bg-\[size\:75px_75px\] {
  background-size: 75px 75px;
}

/* Selection styles */
::selection {
  background-color: hsl(var(--primary) / 0.2);
  color: hsl(var(--primary-foreground));
}

/* Focus styles */
:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Mobile Performance Optimizations */
@media (max-width: 768px) {
  /* Optimize touch interactions */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Improve tap highlighting */
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
  }

  /* Better button touch targets */
  button, 
  [role="button"],
  a {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  /* Optimize scroll performance */
  .scroll-smooth {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Reduce motion for better performance on mobile */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

/* High DPI optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .crisp-edges {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
  .glass {
    backdrop-filter: blur(10px) saturate(180%);
    background-color: rgba(17, 25, 40, 0.75);
    border: 1px solid rgba(255, 255, 255, 0.125);
  }
}

/* Enhanced loading states */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 20%,
    rgba(255, 255, 255, 0.3) 60%,
    transparent 100%
  );
  background-size: 200% auto;
  animation: shimmer 2s infinite;
}

/* Intersection Observer animations */
.fade-in-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-on-scroll.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Performance-optimized shadows */
.shadow-performance {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  will-change: box-shadow;
}

.shadow-performance:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
