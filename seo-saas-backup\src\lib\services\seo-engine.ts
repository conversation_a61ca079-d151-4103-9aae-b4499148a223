// Advanced SEO Content Analysis Engine
// Comprehensive content optimization and competitor analysis system

import { groqService } from './groq';
import { serperService } from './serper';
import { withErrorHandling } from '../error-handler';
import { logApiUsage } from '../supabase';

interface SEOAnalysisRequest {
  keyword: string;
  location: string;
  industry: string;
  contentType: 'service' | 'blog' | 'product' | 'landing' | 'category' | 'faq';
  tone: 'professional' | 'conversational' | 'authoritative' | 'friendly' | 'technical' | 'casual';
  intent: 'informational' | 'commercial' | 'transactional' | 'navigational';
  targetWordCount?: number;
  includeCompetitorAnalysis: boolean;
  includeContentGeneration: boolean;
  customRequirements?: string[];
}

interface SEOAnalysisResult {
  keyword: string;
  location: string;
  competitorAnalysis: CompetitorInsights;
  contentRecommendations: ContentRecommendations;
  generatedContent?: GeneratedContentResult;
  seoScore: SEOScore;
  optimization: OptimizationSuggestions;
  timeline: AnalysisTimeline;
}

interface CompetitorInsights {
  difficulty: 'low' | 'medium' | 'high' | 'very-high';
  averageMetrics: CompetitorMetrics;
  topCompetitors: CompetitorSummary[];
  contentGaps: ContentGap[];
  searchFeatures: SearchFeatureAnalysis[];
  recommendations: string[];
}

interface CompetitorMetrics {
  wordCount: number;
  headingStructure: { [key: string]: number };
  keywordDensity: number;
  domainAuthority: number;
  contentScore: number;
  technicalScore: number;
}

interface CompetitorSummary {
  domain: string;
  position: number;
  title: string;
  strengths: string[];
  weaknesses: string[];
  opportunityScore: number;
}

interface ContentGap {
  type: 'missing_topic' | 'insufficient_depth' | 'outdated_info' | 'poor_structure';
  description: string;
  priority: 'high' | 'medium' | 'low';
  opportunity: string;
}

interface SearchFeatureAnalysis {
  feature: string;
  present: boolean;
  opportunity: boolean;
  strategy: string;
}

interface ContentRecommendations {
  optimalWordCount: number;
  headingStructure: HeadingRecommendation[];
  keywordTargets: KeywordTarget[];
  contentStructure: ContentStructureRecommendation;
  technicalOptimization: TechnicalRecommendation[];
  schemaMarkup: SchemaRecommendation[];
}

interface HeadingRecommendation {
  level: number;
  count: number;
  keywords: string[];
  examples: string[];
}

interface KeywordTarget {
  keyword: string;
  type: 'primary' | 'secondary' | 'lsi' | 'entity';
  targetDensity: number;
  placement: string[];
  priority: number;
}

interface ContentStructureRecommendation {
  introduction: StructureSection;
  mainSections: StructureSection[];
  conclusion: StructureSection;
  additionalElements: AdditionalElement[];
}

interface StructureSection {
  title: string;
  purpose: string;
  keypoints: string[];
  wordCount: number;
  keywords: string[];
}

interface AdditionalElement {
  type: 'faq' | 'table' | 'list' | 'image' | 'video' | 'infographic';
  description: string;
  benefit: string;
  implementation: string;
}

interface TechnicalRecommendation {
  type: 'meta' | 'url' | 'internal_links' | 'external_links' | 'images';
  description: string;
  implementation: string;
  impact: 'high' | 'medium' | 'low';
}

interface SchemaRecommendation {
  type: string;
  description: string;
  code: string;
  benefit: string;
}

interface GeneratedContentResult {
  title: string;
  metaDescription: string;
  content: string;
  outline: ContentOutline;
  seoMetrics: SEOMetrics;
  optimizationApplied: string[];
}

interface ContentOutline {
  structure: OutlineSection[];
  keywordDistribution: KeywordDistribution[];
  estimatedReadingTime: number;
  technicalElements: string[];
}

interface OutlineSection {
  level: number;
  title: string;
  keywords: string[];
  wordCount: number;
  purpose: string;
}

interface KeywordDistribution {
  keyword: string;
  targetDensity: number;
  actualDensity: number;
  placement: string[];
  optimization: 'optimal' | 'low' | 'high' | 'missing';
}

interface SEOMetrics {
  overallScore: number;
  keywordOptimization: number;
  contentQuality: number;
  technicalSEO: number;
  userExperience: number;
  competitiveness: number;
}

interface SEOScore {
  total: number;
  breakdown: ScoreBreakdown;
  comparison: CompetitorComparison;
  improvementPotential: number;
}

interface ScoreBreakdown {
  keywordOptimization: ScoreDetail;
  contentStructure: ScoreDetail;
  technicalSEO: ScoreDetail;
  competitorAlignment: ScoreDetail;
  userIntent: ScoreDetail;
}

interface ScoreDetail {
  score: number;
  maxScore: number;
  status: 'excellent' | 'good' | 'needs_improvement' | 'poor';
  factors: ScoringFactor[];
}

interface ScoringFactor {
  name: string;
  score: number;
  impact: 'high' | 'medium' | 'low';
  description: string;
}

interface CompetitorComparison {
  averageScore: number;
  ranking: number;
  totalCompetitors: number;
  strengths: string[];
  weaknesses: string[];
}

interface OptimizationSuggestions {
  immediate: OptimizationAction[];
  shortTerm: OptimizationAction[];
  longTerm: OptimizationAction[];
  priorityScore: number;
}

interface OptimizationAction {
  action: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'easy' | 'medium' | 'hard';
  timeline: string;
  expectedImprovement: number;
}

interface AnalysisTimeline {
  startTime: number;
  endTime: number;
  steps: TimelineStep[];
  totalDuration: number;
}

interface TimelineStep {
  step: string;
  duration: number;
  status: 'completed' | 'failed' | 'skipped';
  details?: string;
}

export class SEOEngine {
  private static instance: SEOEngine;

  static getInstance(): SEOEngine {
    if (!SEOEngine.instance) {
      SEOEngine.instance = new SEOEngine();
    }
    return SEOEngine.instance;
  }

  // Main analysis method
  async analyzeSEO(
    request: SEOAnalysisRequest,
    userId?: string
  ): Promise<SEOAnalysisResult> {
    return withErrorHandling(
      'seo-engine',
      async () => {
        const startTime = Date.now();
        const timeline: TimelineStep[] = [];

        console.log('Starting comprehensive SEO analysis...', {
          keyword: request.keyword,
          location: request.location,
          contentType: request.contentType,
        });

        let competitorAnalysis: any;
        let generatedContent: any;

        try {
          // Step 1: Competitor Analysis
          if (request.includeCompetitorAnalysis) {
            const stepStart = Date.now();
            console.log('Step 1: Analyzing competitors...');
            
            competitorAnalysis = await serperService.analyzeCompetitors(
              request.keyword,
              request.location,
              {
                analyzeTop: 5,
                includeContent: true,
                includeMetrics: true,
              },
              userId
            );

            timeline.push({
              step: 'Competitor Analysis',
              duration: Date.now() - stepStart,
              status: 'completed',
              details: `Analyzed ${competitorAnalysis.topCompetitors.length} competitors`,
            });
          } else {
            timeline.push({
              step: 'Competitor Analysis',
              duration: 0,
              status: 'skipped',
              details: 'Competitor analysis not requested',
            });
          }

          // Step 2: Content Generation (if requested)
          if (request.includeContentGeneration) {
            const stepStart = Date.now();
            console.log('Step 2: Generating optimized content...');

            const contentRequest = {
              keyword: request.keyword,
              location: request.location,
              industry: request.industry,
              contentType: request.contentType,
              tone: request.tone,
              intent: request.intent,
              targetWordCount: request.targetWordCount || 
                (competitorAnalysis?.averageWordCount || 800),
              includeImages: true,
              includeSchema: true,
              includeFaq: true,
              competitorData: competitorAnalysis,
            };

            generatedContent = await groqService.generateContent(contentRequest, userId);

            timeline.push({
              step: 'Content Generation',
              duration: Date.now() - stepStart,
              status: 'completed',
              details: `Generated ${generatedContent.seoMetrics.wordCount} words`,
            });
          } else {
            timeline.push({
              step: 'Content Generation',
              duration: 0,
              status: 'skipped',
              details: 'Content generation not requested',
            });
          }

          // Step 3: Analyze and create insights
          const stepStart = Date.now();
          console.log('Step 3: Creating insights and recommendations...');

          const competitorInsights = this.createCompetitorInsights(competitorAnalysis);
          const contentRecommendations = this.createContentRecommendations(
            request,
            competitorAnalysis
          );
          const seoScore = this.calculateSEOScore(
            generatedContent,
            competitorAnalysis,
            request
          );
          const optimization = this.generateOptimizationSuggestions(
            seoScore,
            competitorInsights,
            contentRecommendations
          );

          timeline.push({
            step: 'Analysis & Insights',
            duration: Date.now() - stepStart,
            status: 'completed',
            details: 'Generated recommendations and scoring',
          });

          const endTime = Date.now();
          const totalDuration = endTime - startTime;

          const result: SEOAnalysisResult = {
            keyword: request.keyword,
            location: request.location,
            competitorAnalysis: competitorInsights,
            contentRecommendations,
            generatedContent: generatedContent ? {
              title: generatedContent.title,
              metaDescription: generatedContent.metaDescription,
              content: generatedContent.content,
              outline: this.transformOutline(generatedContent.outline),
              seoMetrics: this.transformSEOMetrics(generatedContent.seoMetrics),
              optimizationApplied: this.identifyOptimizationsApplied(generatedContent),
            } : undefined,
            seoScore,
            optimization,
            timeline: {
              startTime,
              endTime,
              steps: timeline,
              totalDuration,
            },
          };

          console.log('SEO analysis completed', {
            totalDuration,
            seoScore: seoScore.total,
            difficulty: competitorInsights.difficulty,
          });

          return result;
        } catch (error) {
          console.error('SEO analysis failed:', error);
          throw error;
        }
      },
      {
        method: 'POST',
        endpoint: 'analyze',
        ...request,
      },
      userId
    );
  }

  // Create competitor insights from raw data
  private createCompetitorInsights(competitorData: any): CompetitorInsights {
    if (!competitorData) {
      return {
        difficulty: 'medium',
        averageMetrics: this.getDefaultMetrics(),
        topCompetitors: [],
        contentGaps: [],
        searchFeatures: [],
        recommendations: ['Enable competitor analysis for detailed insights'],
      };
    }

    const topCompetitors: CompetitorSummary[] = competitorData.topCompetitors.map(
      (comp: any, index: number) => ({
        domain: comp.domain,
        position: comp.position,
        title: comp.title,
        strengths: this.identifyCompetitorStrengths(comp),
        weaknesses: this.identifyCompetitorWeaknesses(comp),
        opportunityScore: this.calculateOpportunityScore(comp, index),
      })
    );

    const contentGaps: ContentGap[] = competitorData.contentGaps.map(
      (gap: string) => ({
        type: 'missing_topic' as const,
        description: gap,
        priority: 'medium' as const,
        opportunity: `Address this content gap to differentiate from competitors`,
      })
    );

    const searchFeatures: SearchFeatureAnalysis[] = competitorData.searchFeatures.map(
      (feature: any) => ({
        feature: feature.type,
        present: feature.present,
        opportunity: !feature.present,
        strategy: this.getFeatureStrategy(feature.type, feature.present),
      })
    );

    return {
      difficulty: competitorData.difficulty,
      averageMetrics: {
        wordCount: competitorData.averageWordCount,
        headingStructure: competitorData.averageHeadings,
        keywordDensity: competitorData.averageKeywordDensity,
        domainAuthority: this.calculateAverageDomainAuthority(competitorData.topCompetitors),
        contentScore: this.calculateAverageContentScore(competitorData.topCompetitors),
        technicalScore: this.calculateAverageTechnicalScore(competitorData.topCompetitors),
      },
      topCompetitors,
      contentGaps,
      searchFeatures,
      recommendations: this.generateCompetitorRecommendations(competitorData),
    };
  }

  // Create content recommendations
  private createContentRecommendations(
    request: SEOAnalysisRequest,
    competitorData: any
  ): ContentRecommendations {
    const targetWordCount = request.targetWordCount || 
      (competitorData?.averageWordCount || 800);
    
    const headingStructure = this.recommendHeadingStructure(
      targetWordCount,
      competitorData?.averageHeadings,
      request.contentType
    );

    const keywordTargets = this.generateKeywordTargets(
      request.keyword,
      competitorData?.commonKeywords || []
    );

    const contentStructure = this.recommendContentStructure(
      request,
      targetWordCount,
      keywordTargets
    );

    const technicalOptimization = this.generateTechnicalRecommendations(request);
    const schemaMarkup = this.generateSchemaRecommendations(request.contentType);

    return {
      optimalWordCount: targetWordCount,
      headingStructure,
      keywordTargets,
      contentStructure,
      technicalOptimization,
      schemaMarkup,
    };
  }

  // Calculate comprehensive SEO score
  private calculateSEOScore(
    generatedContent: any,
    competitorData: any,
    request: SEOAnalysisRequest
  ): SEOScore {
    const keywordOptimization = this.scoreKeywordOptimization(
      generatedContent,
      request.keyword
    );
    
    const contentStructure = this.scoreContentStructure(
      generatedContent,
      competitorData
    );
    
    const technicalSEO = this.scoreTechnicalSEO(generatedContent);
    
    const competitorAlignment = this.scoreCompetitorAlignment(
      generatedContent,
      competitorData
    );
    
    const userIntent = this.scoreUserIntent(generatedContent, request.intent);

    const breakdown: ScoreBreakdown = {
      keywordOptimization,
      contentStructure,
      technicalSEO,
      competitorAlignment,
      userIntent,
    };

    // Calculate total score (weighted average)
    const weights = {
      keywordOptimization: 0.25,
      contentStructure: 0.2,
      technicalSEO: 0.2,
      competitorAlignment: 0.2,
      userIntent: 0.15,
    };

    const total = Math.round(
      Object.entries(breakdown).reduce((sum, [key, detail]) => {
        const weight = weights[key as keyof typeof weights];
        return sum + (detail.score / detail.maxScore) * 100 * weight;
      }, 0)
    );

    const comparison = this.createCompetitorComparison(total, competitorData);
    const improvementPotential = 100 - total;

    return {
      total,
      breakdown,
      comparison,
      improvementPotential,
    };
  }

  // Generate optimization suggestions
  private generateOptimizationSuggestions(
    seoScore: SEOScore,
    competitorInsights: CompetitorInsights,
    contentRecommendations: ContentRecommendations
  ): OptimizationSuggestions {
    const immediate: OptimizationAction[] = [];
    const shortTerm: OptimizationAction[] = [];
    const longTerm: OptimizationAction[] = [];

    // Immediate actions (can be done now)
    if (seoScore.breakdown.keywordOptimization.score < 80) {
      immediate.push({
        action: 'Optimize keyword density',
        description: 'Adjust keyword usage to match competitor averages',
        impact: 'high',
        effort: 'easy',
        timeline: 'Immediate',
        expectedImprovement: 10,
      });
    }

    if (seoScore.breakdown.technicalSEO.score < 70) {
      immediate.push({
        action: 'Fix technical SEO issues',
        description: 'Update meta tags, headings, and technical elements',
        impact: 'high',
        effort: 'easy',
        timeline: 'Immediate',
        expectedImprovement: 15,
      });
    }

    // Short-term actions (within a week)
    if (seoScore.breakdown.contentStructure.score < 75) {
      shortTerm.push({
        action: 'Improve content structure',
        description: 'Reorganize content with better heading hierarchy',
        impact: 'medium',
        effort: 'medium',
        timeline: '1-7 days',
        expectedImprovement: 12,
      });
    }

    if (competitorInsights.contentGaps.length > 0) {
      shortTerm.push({
        action: 'Address content gaps',
        description: 'Add missing topics identified in competitor analysis',
        impact: 'high',
        effort: 'medium',
        timeline: '3-7 days',
        expectedImprovement: 18,
      });
    }

    // Long-term actions (weeks to months)
    if (competitorInsights.difficulty === 'high' || competitorInsights.difficulty === 'very-high') {
      longTerm.push({
        action: 'Build domain authority',
        description: 'Implement link building and authority-building strategies',
        impact: 'high',
        effort: 'hard',
        timeline: '1-6 months',
        expectedImprovement: 25,
      });
    }

    longTerm.push({
      action: 'Monitor and iterate',
      description: 'Track rankings and continuously optimize based on performance',
      impact: 'medium',
      effort: 'medium',
      timeline: 'Ongoing',
      expectedImprovement: 20,
    });

    const priorityScore = this.calculatePriorityScore(immediate, shortTerm, longTerm);

    return {
      immediate,
      shortTerm,
      longTerm,
      priorityScore,
    };
  }

  // Helper methods for scoring and analysis

  private scoreKeywordOptimization(content: any, keyword: string): ScoreDetail {
    if (!content) {
      return {
        score: 0,
        maxScore: 100,
        status: 'poor',
        factors: [{ name: 'No content provided', score: 0, impact: 'high', description: 'Content generation is required for analysis' }],
      };
    }

    const factors: ScoringFactor[] = [];
    let totalScore = 0;

    // Keyword density scoring
    const keywordDensity = content.seoMetrics?.keywordDensity || 0;
    if (keywordDensity >= 1 && keywordDensity <= 3) {
      factors.push({ name: 'Keyword Density', score: 25, impact: 'high', description: 'Optimal keyword density achieved' });
      totalScore += 25;
    } else if (keywordDensity > 0) {
      factors.push({ name: 'Keyword Density', score: 15, impact: 'high', description: 'Keyword density needs adjustment' });
      totalScore += 15;
    }

    // Title optimization
    if (content.title?.toLowerCase().includes(keyword.toLowerCase())) {
      factors.push({ name: 'Keyword in Title', score: 20, impact: 'high', description: 'Primary keyword found in title' });
      totalScore += 20;
    }

    // Meta description optimization
    if (content.metaDescription?.toLowerCase().includes(keyword.toLowerCase())) {
      factors.push({ name: 'Keyword in Meta Description', score: 15, impact: 'medium', description: 'Primary keyword found in meta description' });
      totalScore += 15;
    }

    // LSI keywords
    if (content.seoMetrics?.lsiKeywords?.length > 0) {
      factors.push({ name: 'LSI Keywords', score: 20, impact: 'medium', description: 'Related keywords identified and used' });
      totalScore += 20;
    }

    // Keyword variations
    if (content.seoMetrics?.keywordVariations?.length > 0) {
      factors.push({ name: 'Keyword Variations', score: 20, impact: 'medium', description: 'Keyword variations used naturally' });
      totalScore += 20;
    }

    const status = totalScore >= 80 ? 'excellent' : totalScore >= 60 ? 'good' : totalScore >= 40 ? 'needs_improvement' : 'poor';

    return {
      score: totalScore,
      maxScore: 100,
      status,
      factors,
    };
  }

  private scoreContentStructure(content: any, competitorData: any): ScoreDetail {
    if (!content) {
      return {
        score: 0,
        maxScore: 100,
        status: 'poor',
        factors: [{ name: 'No content provided', score: 0, impact: 'high', description: 'Content generation is required for analysis' }],
      };
    }

    const factors: ScoringFactor[] = [];
    let totalScore = 0;

    // Word count comparison
    const wordCount = content.seoMetrics?.wordCount || 0;
    const averageWordCount = competitorData?.averageWordCount || 800;
    const wordCountRatio = wordCount / averageWordCount;
    
    if (wordCountRatio >= 0.9 && wordCountRatio <= 1.2) {
      factors.push({ name: 'Word Count', score: 25, impact: 'high', description: 'Word count aligns with competitors' });
      totalScore += 25;
    } else if (wordCountRatio >= 0.7 && wordCountRatio <= 1.5) {
      factors.push({ name: 'Word Count', score: 15, impact: 'high', description: 'Word count is acceptable but could be optimized' });
      totalScore += 15;
    }

    // Heading structure
    const headingCount = content.seoMetrics?.headingCount || {};
    if (headingCount.h1 === 1) {
      factors.push({ name: 'H1 Structure', score: 15, impact: 'high', description: 'Proper H1 structure' });
      totalScore += 15;
    }
    if (headingCount.h2 >= 2) {
      factors.push({ name: 'H2 Structure', score: 20, impact: 'medium', description: 'Good H2 heading distribution' });
      totalScore += 20;
    }

    // Readability
    const readabilityScore = content.seoMetrics?.readabilityScore || 0;
    if (readabilityScore >= 60) {
      factors.push({ name: 'Readability', score: 20, impact: 'medium', description: 'Good readability score' });
      totalScore += 20;
    }

    // Content flow and structure
    if (content.outline?.headings?.length > 0) {
      factors.push({ name: 'Content Organization', score: 20, impact: 'medium', description: 'Well-organized content structure' });
      totalScore += 20;
    }

    const status = totalScore >= 80 ? 'excellent' : totalScore >= 60 ? 'good' : totalScore >= 40 ? 'needs_improvement' : 'poor';

    return {
      score: totalScore,
      maxScore: 100,
      status,
      factors,
    };
  }

  private scoreTechnicalSEO(content: any): ScoreDetail {
    const factors: ScoringFactor[] = [];
    let totalScore = 0;

    if (!content) {
      return {
        score: 0,
        maxScore: 100,
        status: 'poor',
        factors: [{ name: 'No content provided', score: 0, impact: 'high', description: 'Content generation is required for analysis' }],
      };
    }

    // Title length
    if (content.title && content.title.length >= 30 && content.title.length <= 60) {
      factors.push({ name: 'Title Length', score: 20, impact: 'high', description: 'Optimal title length' });
      totalScore += 20;
    }

    // Meta description length
    if (content.metaDescription && content.metaDescription.length >= 120 && content.metaDescription.length <= 160) {
      factors.push({ name: 'Meta Description Length', score: 20, impact: 'high', description: 'Optimal meta description length' });
      totalScore += 20;
    }

    // Schema markup
    if (content.schemaMarkup) {
      factors.push({ name: 'Schema Markup', score: 25, impact: 'medium', description: 'Schema markup included' });
      totalScore += 25;
    }

    // Internal links
    if (content.internalLinks && content.internalLinks.length > 0) {
      factors.push({ name: 'Internal Links', score: 15, impact: 'medium', description: 'Internal linking suggestions provided' });
      totalScore += 15;
    }

    // External links
    if (content.externalLinks && content.externalLinks.length > 0) {
      factors.push({ name: 'External Links', score: 10, impact: 'low', description: 'External linking suggestions provided' });
      totalScore += 10;
    }

    // Image optimization
    if (content.imagePrompts && content.imagePrompts.length > 0) {
      factors.push({ name: 'Image Optimization', score: 10, impact: 'low', description: 'Image suggestions with alt text' });
      totalScore += 10;
    }

    const status = totalScore >= 80 ? 'excellent' : totalScore >= 60 ? 'good' : totalScore >= 40 ? 'needs_improvement' : 'poor';

    return {
      score: totalScore,
      maxScore: 100,
      status,
      factors,
    };
  }

  private scoreCompetitorAlignment(content: any, competitorData: any): ScoreDetail {
    if (!competitorData) {
      return {
        score: 50,
        maxScore: 100,
        status: 'needs_improvement',
        factors: [{ name: 'No competitor data', score: 50, impact: 'medium', description: 'Enable competitor analysis for better scoring' }],
      };
    }

    const factors: ScoringFactor[] = [];
    let totalScore = 0;

    // Keyword density alignment
    const keywordDensity = content?.seoMetrics?.keywordDensity || 0;
    const avgKeywordDensity = competitorData.averageKeywordDensity || 2;
    const densityDiff = Math.abs(keywordDensity - avgKeywordDensity);
    
    if (densityDiff <= 0.5) {
      factors.push({ name: 'Keyword Density Alignment', score: 30, impact: 'high', description: 'Keyword density matches competitors' });
      totalScore += 30;
    } else if (densityDiff <= 1) {
      factors.push({ name: 'Keyword Density Alignment', score: 20, impact: 'high', description: 'Keyword density is close to competitors' });
      totalScore += 20;
    }

    // Word count alignment
    const wordCount = content?.seoMetrics?.wordCount || 0;
    const avgWordCount = competitorData.averageWordCount || 800;
    const wordCountRatio = wordCount / avgWordCount;
    
    if (wordCountRatio >= 0.9 && wordCountRatio <= 1.1) {
      factors.push({ name: 'Word Count Alignment', score: 25, impact: 'high', description: 'Word count aligns with top competitors' });
      totalScore += 25;
    }

    // Topic coverage
    const commonKeywords = competitorData.commonKeywords || [];
    const contentKeywords = content?.seoMetrics?.lsiKeywords || [];
    const keywordOverlap = commonKeywords.filter((kw: string) => 
      contentKeywords.some((ckw: string) => ckw.includes(kw))
    ).length;
    
    if (keywordOverlap >= commonKeywords.length * 0.7) {
      factors.push({ name: 'Topic Coverage', score: 25, impact: 'medium', description: 'Good coverage of competitor topics' });
      totalScore += 25;
    }

    // Content gaps addressed
    const contentGaps = competitorData.contentGaps || [];
    if (contentGaps.length > 0) {
      factors.push({ name: 'Content Gaps', score: 20, impact: 'medium', description: 'Opportunity to address competitor content gaps' });
      totalScore += 20;
    }

    const status = totalScore >= 80 ? 'excellent' : totalScore >= 60 ? 'good' : totalScore >= 40 ? 'needs_improvement' : 'poor';

    return {
      score: totalScore,
      maxScore: 100,
      status,
      factors,
    };
  }

  private scoreUserIntent(content: any, intent: string): ScoreDetail {
    const factors: ScoringFactor[] = [];
    let totalScore = 0;

    if (!content) {
      return {
        score: 0,
        maxScore: 100,
        status: 'poor',
        factors: [{ name: 'No content provided', score: 0, impact: 'high', description: 'Content generation is required for analysis' }],
      };
    }

    // Intent-specific scoring
    switch (intent) {
      case 'informational':
        if (content.content?.includes('how to') || content.content?.includes('what is')) {
          factors.push({ name: 'Informational Intent', score: 30, impact: 'high', description: 'Content addresses informational queries' });
          totalScore += 30;
        }
        break;
      case 'commercial':
        if (content.content?.includes('comparison') || content.content?.includes('review')) {
          factors.push({ name: 'Commercial Intent', score: 30, impact: 'high', description: 'Content supports commercial research' });
          totalScore += 30;
        }
        break;
      case 'transactional':
        if (content.content?.includes('buy') || content.content?.includes('service')) {
          factors.push({ name: 'Transactional Intent', score: 30, impact: 'high', description: 'Content supports transactional queries' });
          totalScore += 30;
        }
        break;
    }

    // FAQ section for informational intent
    if (intent === 'informational' && content.faqSection) {
      factors.push({ name: 'FAQ Section', score: 25, impact: 'medium', description: 'FAQ section addresses common questions' });
      totalScore += 25;
    }

    // Call to action alignment
    if (content.content?.includes('contact') || content.content?.includes('learn more')) {
      factors.push({ name: 'Call to Action', score: 25, impact: 'medium', description: 'Appropriate calls to action included' });
      totalScore += 25;
    }

    // Content depth
    const wordCount = content.seoMetrics?.wordCount || 0;
    if (wordCount >= 500) {
      factors.push({ name: 'Content Depth', score: 20, impact: 'medium', description: 'Sufficient content depth for user intent' });
      totalScore += 20;
    }

    const status = totalScore >= 80 ? 'excellent' : totalScore >= 60 ? 'good' : totalScore >= 40 ? 'needs_improvement' : 'poor';

    return {
      score: totalScore,
      maxScore: 100,
      status,
      factors,
    };
  }

  // Additional helper methods
  private getDefaultMetrics(): CompetitorMetrics {
    return {
      wordCount: 800,
      headingStructure: { h1: 1, h2: 3, h3: 2, h4: 1 },
      keywordDensity: 2.0,
      domainAuthority: 50,
      contentScore: 65,
      technicalScore: 70,
    };
  }

  private identifyCompetitorStrengths(competitor: any): string[] {
    const strengths: string[] = [];
    
    if (competitor.contentScore > 80) strengths.push('High-quality content');
    if (competitor.technicalScore > 80) strengths.push('Strong technical SEO');
    if (competitor.domainAuthority > 70) strengths.push('High domain authority');
    if (competitor.wordCount > 1500) strengths.push('Comprehensive content');
    
    return strengths;
  }

  private identifyCompetitorWeaknesses(competitor: any): string[] {
    const weaknesses: string[] = [];
    
    if (competitor.contentScore < 60) weaknesses.push('Poor content quality');
    if (competitor.technicalScore < 60) weaknesses.push('Technical SEO issues');
    if (competitor.keywordDensity < 1) weaknesses.push('Low keyword optimization');
    if (competitor.wordCount < 500) weaknesses.push('Thin content');
    
    return weaknesses;
  }

  private calculateOpportunityScore(competitor: any, position: number): number {
    let score = 100 - (position * 15); // Base score decreases with position
    
    // Adjust based on weaknesses
    const weaknesses = this.identifyCompetitorWeaknesses(competitor);
    score += weaknesses.length * 10;
    
    return Math.max(0, Math.min(100, score));
  }

  private getFeatureStrategy(featureType: string, present: boolean): string {
    if (present) {
      return `Compete for ${featureType} position`;
    } else {
      return `Opportunity to capture ${featureType} feature`;
    }
  }

  private generateCompetitorRecommendations(competitorData: any): string[] {
    const recommendations: string[] = [];
    
    if (competitorData.difficulty === 'low') {
      recommendations.push('Target this keyword - low competition detected');
    } else if (competitorData.difficulty === 'high') {
      recommendations.push('Consider long-tail variations due to high competition');
    }
    
    if (competitorData.averageWordCount < 800) {
      recommendations.push('Opportunity to create more comprehensive content');
    }
    
    if (competitorData.contentGaps.length > 0) {
      recommendations.push('Address identified content gaps for competitive advantage');
    }
    
    return recommendations;
  }

  private calculateAverageDomainAuthority(competitors: any[]): number {
    if (!competitors.length) return 50;
    return competitors.reduce((sum, comp) => sum + (comp.domainAuthority || 50), 0) / competitors.length;
  }

  private calculateAverageContentScore(competitors: any[]): number {
    if (!competitors.length) return 65;
    return competitors.reduce((sum, comp) => sum + (comp.contentScore || 65), 0) / competitors.length;
  }

  private calculateAverageTechnicalScore(competitors: any[]): number {
    if (!competitors.length) return 70;
    return competitors.reduce((sum, comp) => sum + (comp.technicalScore || 70), 0) / competitors.length;
  }

  private recommendHeadingStructure(
    wordCount: number,
    avgHeadings: any,
    contentType: string
  ): HeadingRecommendation[] {
    const recommendations: HeadingRecommendation[] = [];
    
    // H1 - Always one
    recommendations.push({
      level: 1,
      count: 1,
      keywords: ['primary'],
      examples: ['Main title with primary keyword'],
    });
    
    // H2 - Based on word count
    const h2Count = Math.max(2, Math.floor(wordCount / 300));
    recommendations.push({
      level: 2,
      count: h2Count,
      keywords: ['primary', 'secondary'],
      examples: ['Major section headings'],
    });
    
    // H3 - Based on content depth
    const h3Count = Math.max(0, Math.floor(wordCount / 500));
    if (h3Count > 0) {
      recommendations.push({
        level: 3,
        count: h3Count,
        keywords: ['secondary', 'lsi'],
        examples: ['Subsection headings'],
      });
    }
    
    return recommendations;
  }

  private generateKeywordTargets(primaryKeyword: string, commonKeywords: string[]): KeywordTarget[] {
    const targets: KeywordTarget[] = [];
    
    // Primary keyword
    targets.push({
      keyword: primaryKeyword,
      type: 'primary',
      targetDensity: 2.0,
      placement: ['title', 'h1', 'first-paragraph', 'conclusion'],
      priority: 10,
    });
    
    // Add common keywords as secondary targets
    commonKeywords.slice(0, 5).forEach((keyword, index) => {
      targets.push({
        keyword,
        type: 'secondary',
        targetDensity: 1.0,
        placement: ['h2', 'body'],
        priority: 9 - index,
      });
    });
    
    return targets;
  }

  private recommendContentStructure(
    request: SEOAnalysisRequest,
    wordCount: number,
    keywordTargets: KeywordTarget[]
  ): ContentStructureRecommendation {
    const sectionWordCount = Math.floor(wordCount / 6); // Distribute across 6 main sections
    
    return {
      introduction: {
        title: 'Introduction',
        purpose: 'Hook readers and introduce main topic',
        keypoints: ['Problem statement', 'Overview of solution', 'What readers will learn'],
        wordCount: sectionWordCount,
        keywords: [request.keyword],
      },
      mainSections: [
        {
          title: `What is ${request.keyword}?`,
          purpose: 'Define and explain the main concept',
          keypoints: ['Clear definition', 'Key characteristics', 'Why it matters'],
          wordCount: sectionWordCount,
          keywords: keywordTargets.slice(0, 2).map(k => k.keyword),
        },
        {
          title: `Benefits of ${request.keyword}`,
          purpose: 'Highlight value proposition',
          keypoints: ['Primary benefits', 'Real-world examples', 'Supporting evidence'],
          wordCount: sectionWordCount,
          keywords: keywordTargets.slice(1, 3).map(k => k.keyword),
        },
        {
          title: `How to Choose ${request.keyword}`,
          purpose: 'Guide decision-making process',
          keypoints: ['Selection criteria', 'Comparison factors', 'Best practices'],
          wordCount: sectionWordCount,
          keywords: keywordTargets.slice(2, 4).map(k => k.keyword),
        },
      ],
      conclusion: {
        title: 'Conclusion',
        purpose: 'Summarize and provide next steps',
        keypoints: ['Key takeaways', 'Action items', 'Call to action'],
        wordCount: sectionWordCount,
        keywords: [request.keyword],
      },
      additionalElements: [
        {
          type: 'faq',
          description: 'Frequently Asked Questions section',
          benefit: 'Addresses common queries and improves featured snippet chances',
          implementation: 'Add 5-8 relevant questions with detailed answers',
        },
        {
          type: 'table',
          description: 'Comparison or feature table',
          benefit: 'Improves content scannability and user experience',
          implementation: 'Create tables for comparisons or feature lists',
        },
      ],
    };
  }

  private generateTechnicalRecommendations(request: SEOAnalysisRequest): TechnicalRecommendation[] {
    return [
      {
        type: 'meta',
        description: 'Optimize title tag and meta description',
        implementation: 'Include primary keyword in title (30-60 chars) and meta description (120-160 chars)',
        impact: 'high',
      },
      {
        type: 'url',
        description: 'Create SEO-friendly URL structure',
        implementation: `Use URL like /${request.keyword.replace(/\s+/g, '-').toLowerCase()}`,
        impact: 'medium',
      },
      {
        type: 'internal_links',
        description: 'Add strategic internal links',
        implementation: 'Link to 3-5 related pages with keyword-rich anchor text',
        impact: 'medium',
      },
      {
        type: 'images',
        description: 'Optimize images for SEO',
        implementation: 'Add descriptive alt text and file names with keywords',
        impact: 'low',
      },
    ];
  }

  private generateSchemaRecommendations(contentType: string): SchemaRecommendation[] {
    const recommendations: SchemaRecommendation[] = [];
    
    switch (contentType) {
      case 'service':
        recommendations.push({
          type: 'Service',
          description: 'Service schema markup',
          code: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Service',
            'name': 'Service Name',
            'description': 'Service Description',
          }, null, 2),
          benefit: 'Helps search engines understand your service offering',
        });
        break;
      case 'blog':
        recommendations.push({
          type: 'Article',
          description: 'Article schema markup',
          code: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Article',
            'headline': 'Article Title',
            'author': 'Author Name',
          }, null, 2),
          benefit: 'Improves article visibility in search results',
        });
        break;
    }
    
    return recommendations;
  }

  private transformOutline(outline: any): ContentOutline {
    return {
      structure: outline?.headings?.map((heading: any, index: number) => ({
        level: heading.level,
        title: heading.text,
        keywords: heading.keywords || [],
        wordCount: heading.wordCount || 100,
        purpose: this.getHeadingPurpose(heading.level, index),
      })) || [],
      keywordDistribution: [],
      estimatedReadingTime: outline?.estimatedReadingTime || 4,
      technicalElements: ['meta tags', 'headings', 'internal links'],
    };
  }

  private transformSEOMetrics(metrics: any): SEOMetrics {
    return {
      overallScore: metrics?.seoScore || 75,
      keywordOptimization: this.calculateKeywordScore(metrics),
      contentQuality: this.calculateContentScore(metrics),
      technicalSEO: this.calculateTechnicalScore(metrics),
      userExperience: this.calculateUXScore(metrics),
      competitiveness: 70, // Default competitive score
    };
  }

  private identifyOptimizationsApplied(content: any): string[] {
    const optimizations: string[] = [];
    
    if (content.title) optimizations.push('Title optimization');
    if (content.metaDescription) optimizations.push('Meta description optimization');
    if (content.schemaMarkup) optimizations.push('Schema markup');
    if (content.internalLinks) optimizations.push('Internal linking');
    if (content.imagePrompts) optimizations.push('Image optimization');
    if (content.faqSection) optimizations.push('FAQ section');
    
    return optimizations;
  }

  private getHeadingPurpose(level: number, index: number): string {
    switch (level) {
      case 1: return 'Main page title';
      case 2: return 'Major section heading';
      case 3: return 'Subsection heading';
      default: return 'Supporting heading';
    }
  }

  private calculateKeywordScore(metrics: any): number {
    let score = 0;
    if (metrics?.keywordDensity >= 1 && metrics?.keywordDensity <= 3) score += 30;
    if (metrics?.keywordVariations?.length > 0) score += 20;
    if (metrics?.lsiKeywords?.length > 0) score += 25;
    if (metrics?.entities?.length > 0) score += 25;
    return score;
  }

  private calculateContentScore(metrics: any): number {
    let score = 0;
    if (metrics?.wordCount >= 500) score += 25;
    if (metrics?.readabilityScore >= 60) score += 25;
    if (metrics?.headingCount?.h1 === 1) score += 15;
    if (metrics?.headingCount?.h2 >= 2) score += 20;
    if (metrics?.headingCount?.h3 >= 1) score += 15;
    return score;
  }

  private calculateTechnicalScore(metrics: any): number {
    return 75; // Default technical score
  }

  private calculateUXScore(metrics: any): number {
    let score = 50; // Base score
    if (metrics?.readabilityScore >= 70) score += 25;
    if (metrics?.wordCount >= 800) score += 25;
    return score;
  }

  private createCompetitorComparison(score: number, competitorData: any): CompetitorComparison {
    const avgScore = competitorData ? 
      this.calculateAverageContentScore(competitorData.topCompetitors) : 65;
    
    const totalCompetitors = competitorData?.topCompetitors?.length || 5;
    const ranking = score > avgScore ? 
      Math.max(1, Math.floor(totalCompetitors * 0.3)) :
      Math.ceil(totalCompetitors * 0.7);
    
    return {
      averageScore: avgScore,
      ranking,
      totalCompetitors,
      strengths: score > avgScore ? ['Above average optimization'] : [],
      weaknesses: score < avgScore ? ['Below competitor average'] : [],
    };
  }

  private calculatePriorityScore(
    immediate: OptimizationAction[],
    shortTerm: OptimizationAction[],
    longTerm: OptimizationAction[]
  ): number {
    const immediateScore = immediate.reduce((sum, action) => sum + action.expectedImprovement, 0);
    const shortTermScore = shortTerm.reduce((sum, action) => sum + action.expectedImprovement, 0) * 0.8;
    const longTermScore = longTerm.reduce((sum, action) => sum + action.expectedImprovement, 0) * 0.6;
    
    return Math.round(immediateScore + shortTermScore + longTermScore);
  }
}

// Export singleton instance
export const seoEngine = SEOEngine.getInstance();
export default SEOEngine;