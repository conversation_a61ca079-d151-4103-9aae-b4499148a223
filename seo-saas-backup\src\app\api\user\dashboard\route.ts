// User Dashboard API Route
// Provides user statistics, usage data, and account information

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { z } from 'zod';

interface DashboardStats {
  user: UserInfo;
  subscription: SubscriptionInfo;
  usage: UsageStats;
  recentAnalyses: RecentAnalysis[];
  quotas: QuotaInfo;
  billing: BillingInfo;
}

interface UserInfo {
  id: string;
  email: string;
  fullName?: string;
  avatarUrl?: string;
  createdAt: string;
  lastLoginAt?: string;
}

interface SubscriptionInfo {
  tier: 'free' | 'pro' | 'enterprise';
  status: 'active' | 'cancelled' | 'past_due' | 'trialing';
  currentPeriodStart?: string;
  currentPeriodEnd?: string;
  cancelAtPeriodEnd?: boolean;
  trialEnd?: string;
}

interface UsageStats {
  currentMonth: MonthlyUsage;
  previousMonth: MonthlyUsage;
  totalAllTime: AllTimeUsage;
  dailyUsage: DailyUsage[];
}

interface MonthlyUsage {
  month: string;
  analysesCount: number;
  creditsUsed: number;
  costUSD: number;
  groqCalls: number;
  serperCalls: number;
  avgSeoScore: number;
}

interface AllTimeUsage {
  totalAnalyses: number;
  totalCreditsUsed: number;
  totalCostUSD: number;
  averageSeoScore: number;
  joinedDaysAgo: number;
}

interface DailyUsage {
  date: string;
  analyses: number;
  credits: number;
  cost: number;
}

interface RecentAnalysis {
  id: string;
  keyword: string;
  location: string;
  industry: string;
  contentType: string;
  seoScore: number;
  difficulty: string;
  costUSD: number;
  createdAt: string;
}

interface QuotaInfo {
  credits: {
    remaining: number;
    total: number;
    resetDate: string;
  };
  cost: {
    spent: number;
    limit: number;
    resetDate: string;
  };
  apiCalls: {
    used: number;
    limit: number;
    resetDate: string;
  };
}

interface BillingInfo {
  currentMonthCost: number;
  projectedMonthCost: number;
  lastPaymentAmount?: number;
  lastPaymentDate?: string;
  nextBillingDate?: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '30d';
    const includeDetailed = searchParams.get('detailed') === 'true';

    // Get user session
    const supabase = createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user profile data
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select(`
        id,
        email,
        full_name,
        avatar_url,
        subscription_tier,
        subscription_status,
        credits_remaining,
        monthly_spend,
        total_analyses,
        created_at,
        last_login_at,
        subscription_period_start,
        subscription_period_end,
        trial_end
      `)
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Failed to fetch user profile:', profileError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch user data' },
        { status: 500 }
      );
    }

    // Build dashboard stats
    const dashboardStats: DashboardStats = {
      user: {
        id: profile.id,
        email: profile.email,
        fullName: profile.full_name,
        avatarUrl: profile.avatar_url,
        createdAt: profile.created_at,
        lastLoginAt: profile.last_login_at,
      },
      subscription: {
        tier: profile.subscription_tier || 'free',
        status: profile.subscription_status || 'active',
        currentPeriodStart: profile.subscription_period_start,
        currentPeriodEnd: profile.subscription_period_end,
        trialEnd: profile.trial_end,
      },
      usage: await getUsageStats(supabase, user.id, timeframe),
      recentAnalyses: await getRecentAnalyses(supabase, user.id, 10),
      quotas: getQuotaInfo(profile),
      billing: await getBillingInfo(supabase, user.id),
    };

    return NextResponse.json({
      success: true,
      data: dashboardStats,
      metadata: {
        timeframe,
        generatedAt: new Date().toISOString(),
        includeDetailed,
      },
    });

  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST endpoint for updating user preferences
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validation schema
    const updateSchema = z.object({
      fullName: z.string().max(100).optional(),
      avatarUrl: z.string().url().optional(),
      preferences: z.object({
        emailNotifications: z.boolean().optional(),
        marketingEmails: z.boolean().optional(),
        defaultLocation: z.string().max(100).optional(),
        defaultIndustry: z.string().max(50).optional(),
      }).optional(),
    });

    const validatedData = updateSchema.parse(body);

    // Get user session
    const supabase = createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Update user profile
    const updateData: any = {};
    if (validatedData.fullName !== undefined) updateData.full_name = validatedData.fullName;
    if (validatedData.avatarUrl !== undefined) updateData.avatar_url = validatedData.avatarUrl;
    if (validatedData.preferences !== undefined) updateData.preferences = validatedData.preferences;

    updateData.updated_at = new Date().toISOString();

    const { data, error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', user.id)
      .select()
      .single();

    if (error) {
      console.error('Failed to update user profile:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update profile' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        id: data.id,
        fullName: data.full_name,
        avatarUrl: data.avatar_url,
        preferences: data.preferences,
        updatedAt: data.updated_at,
      },
    });

  } catch (error) {
    console.error('Profile update error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper functions

async function getUsageStats(supabase: any, userId: string, timeframe: string): Promise<UsageStats> {
  const now = new Date();
  const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const previousMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  const previousMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

  // Get current month usage
  const { data: currentMonthData } = await supabase
    .from('seo_analyses')
    .select('seo_score, cost_usd, api_calls')
    .eq('user_id', userId)
    .gte('created_at', currentMonthStart.toISOString());

  // Get previous month usage
  const { data: previousMonthData } = await supabase
    .from('seo_analyses')
    .select('seo_score, cost_usd, api_calls')
    .eq('user_id', userId)
    .gte('created_at', previousMonthStart.toISOString())
    .lt('created_at', currentMonthStart.toISOString());

  // Get all-time stats
  const { data: allTimeData } = await supabase
    .from('seo_analyses')
    .select('seo_score, cost_usd, created_at')
    .eq('user_id', userId);

  // Get daily usage for the last 30 days
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const { data: dailyData } = await supabase
    .from('seo_analyses')
    .select('created_at, cost_usd')
    .eq('user_id', userId)
    .gte('created_at', thirtyDaysAgo.toISOString())
    .order('created_at', { ascending: true });

  // Get API usage
  const { data: apiUsage } = await supabase
    .from('api_usage')
    .select('api_name, created_at')
    .eq('user_id', userId)
    .gte('created_at', currentMonthStart.toISOString());

  // Process current month data
  const currentMonth: MonthlyUsage = {
    month: currentMonthStart.toISOString().substring(0, 7),
    analysesCount: currentMonthData?.length || 0,
    creditsUsed: (currentMonthData?.length || 0) * 2, // Estimated credits
    costUSD: currentMonthData?.reduce((sum: number, item: any) => sum + (item.cost_usd || 0), 0) || 0,
    groqCalls: (apiUsage || []).filter((item: any) => item.api_name === 'groq').length || 0,
    serperCalls: (apiUsage || []).filter((item: any) => item.api_name === 'serper').length || 0,
    avgSeoScore: currentMonthData?.length 
      ? (currentMonthData || []).reduce((sum: number, item: any) => sum + (item.seo_score || 0), 0) / currentMonthData.length
      : 0,
  };

  // Process previous month data
  const previousMonth: MonthlyUsage = {
    month: previousMonthStart.toISOString().substring(0, 7),
    analysesCount: previousMonthData?.length || 0,
    creditsUsed: (previousMonthData?.length || 0) * 2,
    costUSD: (previousMonthData || []).reduce((sum: number, item: any) => sum + (item.cost_usd || 0), 0) || 0,
    groqCalls: 0, // Would need additional query for this
    serperCalls: 0, // Would need additional query for this
    avgSeoScore: previousMonthData?.length 
      ? (previousMonthData || []).reduce((sum: number, item: any) => sum + (item.seo_score || 0), 0) / previousMonthData.length
      : 0,
  };

  // Process all-time data
  const totalAllTime: AllTimeUsage = {
    totalAnalyses: allTimeData?.length || 0,
    totalCreditsUsed: (allTimeData?.length || 0) * 2,
    totalCostUSD: (allTimeData || []).reduce((sum: number, item: any) => sum + (item.cost_usd || 0), 0) || 0,
    averageSeoScore: allTimeData?.length 
      ? (allTimeData || []).reduce((sum: number, item: any) => sum + (item.seo_score || 0), 0) / allTimeData.length
      : 0,
    joinedDaysAgo: allTimeData?.length 
      ? Math.floor((now.getTime() - new Date(allTimeData[0].created_at).getTime()) / (24 * 60 * 60 * 1000))
      : 0,
  };

  // Process daily usage
  const dailyUsageMap = new Map<string, DailyUsage>();
  
  // Initialize all days with zero values
  for (let i = 29; i >= 0; i--) {
    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
    const dateStr = date.toISOString().substring(0, 10);
    dailyUsageMap.set(dateStr, {
      date: dateStr,
      analyses: 0,
      credits: 0,
      cost: 0,
    });
  }

  // Fill in actual usage data
  (dailyData || []).forEach((item: any) => {
    const dateStr = item.created_at.substring(0, 10);
    const existing = dailyUsageMap.get(dateStr);
    if (existing) {
      existing.analyses += 1;
      existing.credits += 2; // Estimated credits per analysis
      existing.cost += item.cost_usd || 0;
    }
  });

  const dailyUsage = Array.from(dailyUsageMap.values()).sort((a, b) => a.date.localeCompare(b.date));

  return {
    currentMonth,
    previousMonth,
    totalAllTime,
    dailyUsage,
  };
}

async function getRecentAnalyses(supabase: any, userId: string, limit: number): Promise<RecentAnalysis[]> {
  const { data, error } = await supabase
    .from('seo_analyses')
    .select(`
      id,
      keyword,
      location,
      industry,
      content_type,
      seo_score,
      difficulty,
      cost_usd,
      created_at
    `)
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('Failed to fetch recent analyses:', error);
    return [];
  }

  return (data || []).map((item: any) => ({
    id: item.id,
    keyword: item.keyword,
    location: item.location || '',
    industry: item.industry,
    contentType: item.content_type,
    seoScore: item.seo_score || 0,
    difficulty: item.difficulty || 'medium',
    costUSD: item.cost_usd || 0,
    createdAt: item.created_at,
  }));
}

function getQuotaInfo(profile: any): QuotaInfo {
  const tier = profile.subscription_tier || 'free';
  const now = new Date();
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);

  // Define limits based on subscription tier
  const limits = {
    free: { credits: 50, cost: 5, apiCalls: 100 },
    pro: { credits: 1000, cost: 50, apiCalls: 2000 },
    enterprise: { credits: 10000, cost: 500, apiCalls: 20000 },
  };

  const tierLimits = limits[tier as keyof typeof limits] || limits.free;

  return {
    credits: {
      remaining: profile.credits_remaining || 0,
      total: tierLimits.credits,
      resetDate: nextMonth.toISOString(),
    },
    cost: {
      spent: profile.monthly_spend || 0,
      limit: tierLimits.cost,
      resetDate: nextMonth.toISOString(),
    },
    apiCalls: {
      used: 0, // Would need to calculate from API usage table
      limit: tierLimits.apiCalls,
      resetDate: nextMonth.toISOString(),
    },
  };
}

async function getBillingInfo(supabase: any, userId: string): Promise<BillingInfo> {
  const now = new Date();
  const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);

  // Get current month spending
  const { data: currentMonthSpending } = await supabase
    .from('seo_analyses')
    .select('cost_usd')
    .eq('user_id', userId)
    .gte('created_at', currentMonthStart.toISOString());

  const currentMonthCost = (currentMonthSpending || []).reduce((sum: number, item: any) => sum + (item.cost_usd || 0), 0) || 0;

  // Calculate daily average and project for the month
  const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
  const daysPassed = now.getDate();
  const dailyAverage = daysPassed > 0 ? currentMonthCost / daysPassed : 0;
  const projectedMonthCost = dailyAverage * daysInMonth;

  return {
    currentMonthCost,
    projectedMonthCost,
    // In a real app, these would come from payment/billing tables
    lastPaymentAmount: undefined,
    lastPaymentDate: undefined,
    nextBillingDate: new Date(now.getFullYear(), now.getMonth() + 1, 1).toISOString(),
  };
}