const natural = require('natural');

class ContentQualityValidator {
  constructor() {
    this.tokenizer = new natural.WordTokenizer();
    this.sentenceTokenizer = new natural.SentenceTokenizer();
    this.stemmer = natural.PorterStemmer;
    
    // Initialize validation rules and thresholds
    this.validationRules = this.initializeValidationRules();
    this.qualityThresholds = this.initializeQualityThresholds();
    this.precisionMetrics = this.initializePrecisionMetrics();
  }

  // Main method for comprehensive content quality validation
  async validateContentQuality(content, keyword, competitorBenchmarks, options = {}) {
    try {
      console.log(`Starting comprehensive content quality validation for keyword: ${keyword}`);

      // Perform precision validation
      const precisionValidation = this.validatePrecisionCompliance(content, keyword, competitorBenchmarks);
      
      // Validate keyword optimization
      const keywordValidation = this.validateKeywordOptimization(content, keyword, competitorBenchmarks);
      
      // Validate content structure
      const structureValidation = this.validateContentStructure(content, keyword, competitorBenchmarks);
      
      // Validate readability and grammar
      const readabilityValidation = this.validateReadabilityStandards(content);
      
      // Validate SEO compliance
      const seoValidation = this.validateSEOCompliance(content, keyword);
      
      // Validate E-E-A-T signals
      const eeatValidation = this.validateEEATSignals(content, keyword);
      
      // Validate semantic integration
      const semanticValidation = this.validateSemanticIntegration(content, keyword, options.semanticData);
      
      // Perform competitive analysis validation
      const competitiveValidation = this.validateCompetitiveCompliance(content, keyword, competitorBenchmarks);
      
      // Calculate overall quality score
      const overallQualityScore = this.calculateOverallQualityScore({
        precisionValidation,
        keywordValidation,
        structureValidation,
        readabilityValidation,
        seoValidation,
        eeatValidation,
        semanticValidation,
        competitiveValidation
      });
      
      // Generate comprehensive validation report
      const validationReport = this.generateValidationReport({
        precisionValidation,
        keywordValidation,
        structureValidation,
        readabilityValidation,
        seoValidation,
        eeatValidation,
        semanticValidation,
        competitiveValidation,
        overallQualityScore
      });

      return {
        success: true,
        validation: {
          overallQualityScore,
          passed: overallQualityScore >= this.qualityThresholds.minimumQualityScore,
          precisionValidation,
          keywordValidation,
          structureValidation,
          readabilityValidation,
          seoValidation,
          eeatValidation,
          semanticValidation,
          competitiveValidation,
          validationReport
        },
        metadata: {
          validatedAt: new Date().toISOString(),
          validationVersion: '1.0.0',
          qualityGrade: this.getQualityGrade(overallQualityScore),
          improvementAreas: validationReport.improvementAreas,
          criticalIssues: validationReport.criticalIssues
        }
      };

    } catch (error) {
      console.error('Content quality validation error:', error);
      return {
        success: false,
        error: error.message,
        validation: null
      };
    }
  }

  // Initialize validation rules
  initializeValidationRules() {
    return {
      wordCount: {
        tolerance: 0, // Zero tolerance for word count deviation
        minAccuracy: 100, // Must be exactly accurate
        maxDeviation: 5 // Maximum 5 words deviation allowed
      },
      keywordDensity: {
        tolerance: 0.1, // 0.1% tolerance for keyword density
        minAccuracy: 95, // 95% accuracy required
        componentTolerance: 0.2 // Higher tolerance for component keywords
      },
      headingStructure: {
        exactMatch: true, // Must match competitor heading counts exactly
        keywordOptimization: 0.5, // At least 50% of headings should contain keyword
        hierarchyCompliance: true // Proper H1->H2->H3 hierarchy required
      },
      readability: {
        minFleschScore: 60, // Minimum Flesch Reading Ease score
        maxGradeLevel: 12, // Maximum grade level
        sentenceComplexity: 'moderate' // Sentence complexity level
      },
      seo: {
        titleOptimization: true, // Title must be optimized
        metaDescription: true, // Meta description required
        internalLinking: true, // Internal links required
        technicalFactors: true // Technical SEO factors must be present
      },
      eeat: {
        minScore: 70, // Minimum E-E-A-T score
        authoritySignals: true, // Authority signals required
        expertiseIndicators: true, // Expertise indicators required
        trustSignals: true // Trust signals required
      },
      semantic: {
        lsiIntegration: true, // LSI keywords must be integrated
        entityOptimization: true, // Entities must be optimized
        topicalAuthority: 0.8, // Topical authority score threshold
        semanticCoherence: 0.85 // Semantic coherence threshold
      }
    };
  }

  // Initialize quality thresholds
  initializeQualityThresholds() {
    return {
      minimumQualityScore: 90, // 90% overall quality required
      excellentScore: 95, // 95% for excellent rating
      goodScore: 85, // 85% for good rating
      acceptableScore: 80, // 80% for acceptable rating
      criticalIssueThreshold: 5, // Maximum 5 critical issues
      majorIssueThreshold: 10, // Maximum 10 major issues
      minorIssueThreshold: 20 // Maximum 20 minor issues
    };
  }

  // Initialize precision metrics
  initializePrecisionMetrics() {
    return {
      wordCountWeight: 25, // 25% weight for word count accuracy
      keywordDensityWeight: 20, // 20% weight for keyword density
      structureWeight: 15, // 15% weight for content structure
      readabilityWeight: 10, // 10% weight for readability
      seoWeight: 15, // 15% weight for SEO compliance
      eeatWeight: 10, // 10% weight for E-E-A-T signals
      semanticWeight: 5 // 5% weight for semantic integration
    };
  }

  // Validate precision compliance
  validatePrecisionCompliance(content, keyword, benchmarks) {
    const words = this.tokenizer.tokenize(content) || [];
    const actualWordCount = words.length;
    const targetWordCount = benchmarks.wordCount?.target || 1000;
    
    // Calculate word count precision
    const wordCountDeviation = Math.abs(actualWordCount - targetWordCount);
    const wordCountAccuracy = Math.max(0, 100 - (wordCountDeviation / targetWordCount) * 100);
    
    // Calculate keyword density precision
    const keywordDensity = this.calculateKeywordDensity(content, keyword);
    const targetDensity = benchmarks.keywordDensity?.main || 2.0;
    const densityDeviation = Math.abs(keywordDensity - targetDensity);
    const densityAccuracy = Math.max(0, 100 - (densityDeviation / targetDensity) * 100);

    return {
      wordCount: {
        actual: actualWordCount,
        target: targetWordCount,
        deviation: wordCountDeviation,
        accuracy: wordCountAccuracy,
        passed: wordCountDeviation <= this.validationRules.wordCount.maxDeviation
      },
      keywordDensity: {
        actual: keywordDensity,
        target: targetDensity,
        deviation: densityDeviation,
        accuracy: densityAccuracy,
        passed: densityDeviation <= this.validationRules.keywordDensity.tolerance
      },
      overallPrecision: (wordCountAccuracy + densityAccuracy) / 2,
      passed: wordCountDeviation <= this.validationRules.wordCount.maxDeviation && 
              densityDeviation <= this.validationRules.keywordDensity.tolerance
    };
  }

  // Validate keyword optimization
  validateKeywordOptimization(content, keyword, benchmarks) {
    const contentLower = content.toLowerCase();
    const keywordLower = keyword.toLowerCase();
    
    // Analyze keyword placement
    const titleContainsKeyword = this.checkTitleKeywordPresence(content, keyword);
    const firstParagraphContainsKeyword = this.checkFirstParagraphKeyword(content, keyword);
    const lastParagraphContainsKeyword = this.checkLastParagraphKeyword(content, keyword);
    
    // Analyze keyword distribution
    const keywordDistribution = this.analyzeKeywordDistribution(content, keyword);
    
    // Analyze component keywords
    const componentKeywords = keyword.split(' ');
    const componentAnalysis = componentKeywords.map(component => ({
      keyword: component,
      density: this.calculateKeywordDensity(content, component),
      distribution: this.analyzeKeywordDistribution(content, component)
    }));

    return {
      mainKeyword: {
        density: this.calculateKeywordDensity(content, keyword),
        placement: {
          title: titleContainsKeyword,
          firstParagraph: firstParagraphContainsKeyword,
          lastParagraph: lastParagraphContainsKeyword
        },
        distribution: keywordDistribution
      },
      componentKeywords: componentAnalysis,
      overallOptimization: this.calculateKeywordOptimizationScore({
        titleContainsKeyword,
        firstParagraphContainsKeyword,
        lastParagraphContainsKeyword,
        keywordDistribution,
        componentAnalysis
      }),
      passed: titleContainsKeyword && firstParagraphContainsKeyword && keywordDistribution.evenDistribution
    };
  }

  // Validate content structure
  validateContentStructure(content, keyword, benchmarks) {
    const headingAnalysis = this.analyzeHeadingStructure(content, keyword);
    const paragraphAnalysis = this.analyzeParagraphStructure(content);
    const listAnalysis = this.analyzeListStructure(content);
    
    // Compare with benchmarks
    const targetH2Count = benchmarks.headingStructure?.h2Count || 5;
    const targetH3Count = benchmarks.headingStructure?.h3Count || 8;
    
    const h2Compliance = Math.abs(headingAnalysis.h2Count - targetH2Count) <= 1;
    const h3Compliance = Math.abs(headingAnalysis.h3Count - targetH3Count) <= 2;

    return {
      headings: {
        structure: headingAnalysis,
        compliance: {
          h2Count: h2Compliance,
          h3Count: h3Compliance,
          keywordOptimization: headingAnalysis.keywordOptimizationRatio >= 50
        }
      },
      paragraphs: paragraphAnalysis,
      lists: listAnalysis,
      overallStructure: this.calculateStructureScore({
        headingAnalysis,
        paragraphAnalysis,
        listAnalysis,
        h2Compliance,
        h3Compliance
      }),
      passed: h2Compliance && h3Compliance && headingAnalysis.h1Count === 1
    };
  }

  // Validate readability standards
  validateReadabilityStandards(content) {
    const readabilityMetrics = this.calculateReadabilityMetrics(content);
    const sentenceAnalysis = this.analyzeSentenceComplexity(content);
    const vocabularyAnalysis = this.analyzeVocabularyComplexity(content);

    return {
      metrics: readabilityMetrics,
      sentences: sentenceAnalysis,
      vocabulary: vocabularyAnalysis,
      overallReadability: this.calculateReadabilityScore(readabilityMetrics, sentenceAnalysis, vocabularyAnalysis),
      passed: readabilityMetrics.fleschReadingEase >= this.validationRules.readability.minFleschScore &&
              readabilityMetrics.fleschKincaidGrade <= this.validationRules.readability.maxGradeLevel
    };
  }

  // Validate SEO compliance
  validateSEOCompliance(content, keyword) {
    const titleOptimization = this.validateTitleOptimization(content, keyword);
    const metaDescriptionOptimization = this.validateMetaDescription(content, keyword);
    const internalLinkingOptimization = this.validateInternalLinking(content);
    const technicalSEOFactors = this.validateTechnicalSEOFactors(content, keyword);

    return {
      title: titleOptimization,
      metaDescription: metaDescriptionOptimization,
      internalLinking: internalLinkingOptimization,
      technicalFactors: technicalSEOFactors,
      overallSEO: this.calculateSEOScore({
        titleOptimization,
        metaDescriptionOptimization,
        internalLinkingOptimization,
        technicalSEOFactors
      }),
      passed: titleOptimization.passed && metaDescriptionOptimization.passed && 
              internalLinkingOptimization.passed && technicalSEOFactors.passed
    };
  }

  // Validate E-E-A-T signals
  validateEEATSignals(content, keyword) {
    const experienceSignals = this.validateExperienceSignals(content);
    const expertiseSignals = this.validateExpertiseSignals(content, keyword);
    const authoritySignals = this.validateAuthoritySignals(content);
    const trustSignals = this.validateTrustSignals(content);

    return {
      experience: experienceSignals,
      expertise: expertiseSignals,
      authority: authoritySignals,
      trust: trustSignals,
      overallEEAT: this.calculateEEATScore({
        experienceSignals,
        expertiseSignals,
        authoritySignals,
        trustSignals
      }),
      passed: experienceSignals.score >= 70 && expertiseSignals.score >= 70 && 
              authoritySignals.score >= 70 && trustSignals.score >= 70
    };
  }

  // Validate semantic integration
  validateSemanticIntegration(content, keyword, semanticData) {
    if (!semanticData) {
      return {
        lsiIntegration: { score: 0, passed: false },
        entityOptimization: { score: 0, passed: false },
        topicalAuthority: { score: 0, passed: false },
        semanticCoherence: { score: 0, passed: false },
        overallSemantic: 0,
        passed: false
      };
    }

    const lsiIntegration = this.validateLSIIntegration(content, semanticData);
    const entityOptimization = this.validateEntityOptimization(content, semanticData);
    const topicalAuthority = this.validateTopicalAuthority(content, keyword, semanticData);
    const semanticCoherence = this.validateSemanticCoherence(content, keyword);

    return {
      lsiIntegration,
      entityOptimization,
      topicalAuthority,
      semanticCoherence,
      overallSemantic: this.calculateSemanticScore({
        lsiIntegration,
        entityOptimization,
        topicalAuthority,
        semanticCoherence
      }),
      passed: lsiIntegration.passed && entityOptimization.passed && 
              topicalAuthority.passed && semanticCoherence.passed
    };
  }

  // Validate competitive compliance
  validateCompetitiveCompliance(content, keyword, benchmarks) {
    const wordCountCompliance = this.validateWordCountCompliance(content, benchmarks);
    const keywordDensityCompliance = this.validateKeywordDensityCompliance(content, keyword, benchmarks);
    const structureCompliance = this.validateStructureCompliance(content, benchmarks);

    return {
      wordCount: wordCountCompliance,
      keywordDensity: keywordDensityCompliance,
      structure: structureCompliance,
      overallCompliance: this.calculateCompetitiveComplianceScore({
        wordCountCompliance,
        keywordDensityCompliance,
        structureCompliance
      }),
      passed: wordCountCompliance.passed && keywordDensityCompliance.passed && structureCompliance.passed
    };
  }

  // Calculate overall quality score
  calculateOverallQualityScore(validations) {
    const weights = this.precisionMetrics;
    
    let totalScore = 0;
    totalScore += (validations.precisionValidation.overallPrecision * weights.wordCountWeight) / 100;
    totalScore += (validations.keywordValidation.overallOptimization * weights.keywordDensityWeight) / 100;
    totalScore += (validations.structureValidation.overallStructure * weights.structureWeight) / 100;
    totalScore += (validations.readabilityValidation.overallReadability * weights.readabilityWeight) / 100;
    totalScore += (validations.seoValidation.overallSEO * weights.seoWeight) / 100;
    totalScore += (validations.eeatValidation.overallEEAT * weights.eeatWeight) / 100;
    totalScore += (validations.semanticValidation.overallSemantic * weights.semanticWeight) / 100;

    return Math.round(totalScore * 100) / 100;
  }

  // Generate comprehensive validation report
  generateValidationReport(validations) {
    const criticalIssues = [];
    const majorIssues = [];
    const minorIssues = [];
    const improvementAreas = [];

    // Analyze each validation area
    if (!validations.precisionValidation.passed) {
      criticalIssues.push('Word count or keyword density precision requirements not met');
    }

    if (!validations.keywordValidation.passed) {
      majorIssues.push('Keyword optimization requirements not satisfied');
    }

    if (!validations.structureValidation.passed) {
      majorIssues.push('Content structure does not match competitor benchmarks');
    }

    if (!validations.readabilityValidation.passed) {
      minorIssues.push('Readability standards not met');
    }

    if (!validations.seoValidation.passed) {
      majorIssues.push('SEO compliance requirements not satisfied');
    }

    if (!validations.eeatValidation.passed) {
      minorIssues.push('E-E-A-T signals need improvement');
    }

    if (!validations.semanticValidation.passed) {
      minorIssues.push('Semantic integration could be enhanced');
    }

    // Generate improvement recommendations
    if (validations.overallQualityScore < this.qualityThresholds.excellentScore) {
      improvementAreas.push('Overall content quality optimization needed');
    }

    return {
      criticalIssues,
      majorIssues,
      minorIssues,
      improvementAreas,
      totalIssues: criticalIssues.length + majorIssues.length + minorIssues.length,
      qualityGrade: this.getQualityGrade(validations.overallQualityScore),
      recommendations: this.generateRecommendations(validations),
      nextSteps: this.generateNextSteps(criticalIssues, majorIssues, minorIssues)
    };
  }

  // Helper methods for calculations
  calculateKeywordDensity(content, keyword) {
    const words = this.tokenizer.tokenize(content.toLowerCase()) || [];
    const keywordWords = this.tokenizer.tokenize(keyword.toLowerCase()) || [];
    const keywordMatches = content.toLowerCase().split(keyword.toLowerCase()).length - 1;
    
    return words.length > 0 ? (keywordMatches / words.length) * 100 : 0;
  }

  analyzeHeadingStructure(content, keyword) {
    const h1Matches = content.match(/<h1[^>]*>(.*?)<\/h1>/gi) || content.match(/^# (.+)$/gm) || [];
    const h2Matches = content.match(/<h2[^>]*>(.*?)<\/h2>/gi) || content.match(/^## (.+)$/gm) || [];
    const h3Matches = content.match(/<h3[^>]*>(.*?)<\/h3>/gi) || content.match(/^### (.+)$/gm) || [];
    
    const keywordLower = keyword.toLowerCase();
    const allHeadings = [...h1Matches, ...h2Matches, ...h3Matches];
    const keywordOptimizedHeadings = allHeadings.filter(heading => 
      heading.toLowerCase().includes(keywordLower)
    ).length;

    return {
      h1Count: h1Matches.length,
      h2Count: h2Matches.length,
      h3Count: h3Matches.length,
      totalHeadings: allHeadings.length,
      keywordOptimizedHeadings,
      keywordOptimizationRatio: allHeadings.length > 0 ? (keywordOptimizedHeadings / allHeadings.length) * 100 : 0
    };
  }

  getQualityGrade(score) {
    if (score >= this.qualityThresholds.excellentScore) return 'A+';
    if (score >= this.qualityThresholds.goodScore) return 'A';
    if (score >= this.qualityThresholds.acceptableScore) return 'B';
    if (score >= 70) return 'C';
    return 'F';
  }

  // Additional helper methods would be implemented here...
  // (Simplified for brevity, but would include all validation logic)

  checkTitleKeywordPresence(content, keyword) {
    const titleMatch = content.match(/<h1[^>]*>(.*?)<\/h1>/i) || content.match(/^# (.+)$/m);
    if (!titleMatch) return false;
    return titleMatch[0].toLowerCase().includes(keyword.toLowerCase());
  }

  checkFirstParagraphKeyword(content, keyword) {
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    if (paragraphs.length === 0) return false;
    return paragraphs[0].toLowerCase().includes(keyword.toLowerCase());
  }

  checkLastParagraphKeyword(content, keyword) {
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    if (paragraphs.length === 0) return false;
    return paragraphs[paragraphs.length - 1].toLowerCase().includes(keyword.toLowerCase());
  }

  analyzeKeywordDistribution(content, keyword) {
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const keywordParagraphs = paragraphs.filter(p => p.toLowerCase().includes(keyword.toLowerCase()));
    
    return {
      totalParagraphs: paragraphs.length,
      keywordParagraphs: keywordParagraphs.length,
      distributionRatio: paragraphs.length > 0 ? (keywordParagraphs.length / paragraphs.length) * 100 : 0,
      evenDistribution: keywordParagraphs.length >= Math.floor(paragraphs.length * 0.3)
    };
  }

  calculateKeywordOptimizationScore(analysis) {
    let score = 0;
    if (analysis.titleContainsKeyword) score += 30;
    if (analysis.firstParagraphContainsKeyword) score += 25;
    if (analysis.lastParagraphContainsKeyword) score += 20;
    if (analysis.keywordDistribution.evenDistribution) score += 25;
    return score;
  }

  analyzeParagraphStructure(content) {
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const avgLength = paragraphs.reduce((sum, p) => sum + p.length, 0) / paragraphs.length;
    
    return {
      count: paragraphs.length,
      averageLength: Math.round(avgLength),
      shortParagraphs: paragraphs.filter(p => p.length < 100).length,
      longParagraphs: paragraphs.filter(p => p.length > 500).length
    };
  }

  analyzeListStructure(content) {
    const bulletLists = content.match(/^\s*[-*+]\s+/gm) || [];
    const numberedLists = content.match(/^\s*\d+\.\s+/gm) || [];
    
    return {
      bulletLists: bulletLists.length,
      numberedLists: numberedLists.length,
      totalLists: bulletLists.length + numberedLists.length
    };
  }

  calculateStructureScore(analysis) {
    let score = 0;
    if (analysis.h2Compliance) score += 30;
    if (analysis.h3Compliance) score += 30;
    if (analysis.headingAnalysis.h1Count === 1) score += 20;
    if (analysis.paragraphAnalysis.count >= 5) score += 10;
    if (analysis.listAnalysis.totalLists >= 2) score += 10;
    return score;
  }

  calculateReadabilityMetrics(content) {
    const sentences = this.sentenceTokenizer.tokenize(content) || [];
    const words = this.tokenizer.tokenize(content) || [];
    const syllables = this.countSyllables(content);
    
    // Flesch Reading Ease
    const fleschReadingEase = 206.835 - (1.015 * (words.length / sentences.length)) - (84.6 * (syllables / words.length));
    
    // Flesch-Kincaid Grade Level
    const fleschKincaidGrade = 0.39 * (words.length / sentences.length) + 11.8 * (syllables / words.length) - 15.59;
    
    return {
      fleschReadingEase: Math.round(fleschReadingEase * 100) / 100,
      fleschKincaidGrade: Math.round(fleschKincaidGrade * 100) / 100,
      sentences: sentences.length,
      words: words.length,
      syllables: syllables
    };
  }

  countSyllables(text) {
    return text.toLowerCase().replace(/[^a-z]/g, '').replace(/[aeiouy]+/g, 'a').length;
  }

  analyzeSentenceComplexity(content) {
    const sentences = this.sentenceTokenizer.tokenize(content) || [];
    const lengths = sentences.map(s => (this.tokenizer.tokenize(s) || []).length);
    
    return {
      count: sentences.length,
      averageLength: lengths.reduce((sum, len) => sum + len, 0) / lengths.length,
      shortSentences: lengths.filter(len => len < 10).length,
      longSentences: lengths.filter(len => len > 25).length
    };
  }

  analyzeVocabularyComplexity(content) {
    const words = this.tokenizer.tokenize(content.toLowerCase()) || [];
    const uniqueWords = [...new Set(words)];
    
    return {
      totalWords: words.length,
      uniqueWords: uniqueWords.length,
      lexicalDiversity: uniqueWords.length / words.length,
      averageWordLength: words.reduce((sum, word) => sum + word.length, 0) / words.length
    };
  }

  calculateReadabilityScore(metrics, sentences, vocabulary) {
    let score = 0;
    if (metrics.fleschReadingEase >= 60) score += 40;
    if (metrics.fleschKincaidGrade <= 12) score += 30;
    if (sentences.averageLength <= 20) score += 15;
    if (vocabulary.lexicalDiversity >= 0.5) score += 15;
    return score;
  }

  validateTitleOptimization(content, keyword) {
    const titleMatch = content.match(/<h1[^>]*>(.*?)<\/h1>/i) || content.match(/^# (.+)$/m);
    if (!titleMatch) return { passed: false, score: 0, issues: ['No title found'] };
    
    const title = titleMatch[1] || titleMatch[0];
    const containsKeyword = title.toLowerCase().includes(keyword.toLowerCase());
    const length = title.length;
    const optimalLength = length >= 30 && length <= 60;
    
    return {
      passed: containsKeyword && optimalLength,
      score: (containsKeyword ? 50 : 0) + (optimalLength ? 50 : 0),
      title,
      length,
      containsKeyword,
      optimalLength,
      issues: [
        ...(containsKeyword ? [] : ['Title does not contain target keyword']),
        ...(optimalLength ? [] : ['Title length not optimal (30-60 characters)'])
      ]
    };
  }

  validateMetaDescription(content, keyword) {
    // This would analyze meta description if present in content
    // Simplified implementation
    return {
      passed: true,
      score: 80,
      issues: []
    };
  }

  validateInternalLinking(content) {
    const internalLinks = content.match(/\[([^\]]+)\]\(([^)]+)\)/g) || [];
    const passed = internalLinks.length >= 2;
    
    return {
      passed,
      score: passed ? 100 : Math.min(internalLinks.length * 50, 100),
      linkCount: internalLinks.length,
      issues: passed ? [] : ['Insufficient internal linking (minimum 2 links required)']
    };
  }

  validateTechnicalSEOFactors(content, keyword) {
    // Simplified technical SEO validation
    const hasStructuredData = content.includes('schema') || content.includes('json-ld');
    const hasImageAltText = content.includes('alt=') || content.includes('![');
    
    return {
      passed: true,
      score: 90,
      structuredData: hasStructuredData,
      imageOptimization: hasImageAltText,
      issues: []
    };
  }

  calculateSEOScore(factors) {
    return (factors.titleOptimization.score + factors.metaDescriptionOptimization.score + 
            factors.internalLinkingOptimization.score + factors.technicalSEOFactors.score) / 4;
  }

  validateExperienceSignals(content) {
    const experienceIndicators = [
      'experience', 'used', 'tested', 'tried', 'personally', 'hands-on', 'real-world'
    ];
    
    const signals = experienceIndicators.filter(indicator => 
      content.toLowerCase().includes(indicator)
    );
    
    return {
      score: Math.min(signals.length * 15, 100),
      signals: signals.length,
      passed: signals.length >= 3
    };
  }

  validateExpertiseSignals(content, keyword) {
    const expertiseIndicators = [
      'expert', 'professional', 'specialist', 'certified', 'qualified', 'advanced', 'technical'
    ];
    
    const signals = expertiseIndicators.filter(indicator => 
      content.toLowerCase().includes(indicator)
    );
    
    return {
      score: Math.min(signals.length * 15, 100),
      signals: signals.length,
      passed: signals.length >= 3
    };
  }

  validateAuthoritySignals(content) {
    const authorityIndicators = [
      'research', 'study', 'data', 'statistics', 'source', 'reference', 'according to'
    ];
    
    const signals = authorityIndicators.filter(indicator => 
      content.toLowerCase().includes(indicator)
    );
    
    return {
      score: Math.min(signals.length * 15, 100),
      signals: signals.length,
      passed: signals.length >= 3
    };
  }

  validateTrustSignals(content) {
    const trustIndicators = [
      'guarantee', 'secure', 'privacy', 'contact', 'about', 'testimonial', 'review'
    ];
    
    const signals = trustIndicators.filter(indicator => 
      content.toLowerCase().includes(indicator)
    );
    
    return {
      score: Math.min(signals.length * 15, 100),
      signals: signals.length,
      passed: signals.length >= 2
    };
  }

  calculateEEATScore(signals) {
    return (signals.experienceSignals.score + signals.expertiseSignals.score + 
            signals.authoritySignals.score + signals.trustSignals.score) / 4;
  }

  validateLSIIntegration(content, semanticData) {
    if (!semanticData.headingOptimizedTerms) return { passed: false, score: 0 };
    
    const integratedTerms = semanticData.headingOptimizedTerms.filter(term => 
      content.toLowerCase().includes(term.toLowerCase())
    );
    
    const score = (integratedTerms.length / semanticData.headingOptimizedTerms.length) * 100;
    
    return {
      passed: score >= 70,
      score,
      integratedTerms: integratedTerms.length,
      totalTerms: semanticData.headingOptimizedTerms.length
    };
  }

  validateEntityOptimization(content, semanticData) {
    if (!semanticData.prioritizedEntities) return { passed: false, score: 0 };
    
    const integratedEntities = semanticData.prioritizedEntities.filter(entity => 
      content.toLowerCase().includes(entity.term.toLowerCase())
    );
    
    const score = (integratedEntities.length / semanticData.prioritizedEntities.length) * 100;
    
    return {
      passed: score >= 60,
      score,
      integratedEntities: integratedEntities.length,
      totalEntities: semanticData.prioritizedEntities.length
    };
  }

  validateTopicalAuthority(content, keyword, semanticData) {
    // Simplified topical authority validation
    return {
      passed: true,
      score: 85,
      authorityScore: 85
    };
  }

  validateSemanticCoherence(content, keyword) {
    // Simplified semantic coherence validation
    return {
      passed: true,
      score: 90,
      coherenceScore: 90
    };
  }

  calculateSemanticScore(factors) {
    return (factors.lsiIntegration.score + factors.entityOptimization.score + 
            factors.topicalAuthority.score + factors.semanticCoherence.score) / 4;
  }

  validateWordCountCompliance(content, benchmarks) {
    const words = this.tokenizer.tokenize(content) || [];
    const actualCount = words.length;
    const targetCount = benchmarks.wordCount?.target || 1000;
    const deviation = Math.abs(actualCount - targetCount);
    
    return {
      passed: deviation <= 5,
      score: Math.max(0, 100 - (deviation / targetCount) * 100),
      actual: actualCount,
      target: targetCount,
      deviation
    };
  }

  validateKeywordDensityCompliance(content, keyword, benchmarks) {
    const actualDensity = this.calculateKeywordDensity(content, keyword);
    const targetDensity = benchmarks.keywordDensity?.main || 2.0;
    const deviation = Math.abs(actualDensity - targetDensity);
    
    return {
      passed: deviation <= 0.1,
      score: Math.max(0, 100 - (deviation / targetDensity) * 100),
      actual: actualDensity,
      target: targetDensity,
      deviation
    };
  }

  validateStructureCompliance(content, benchmarks) {
    const headingAnalysis = this.analyzeHeadingStructure(content, '');
    const targetH2 = benchmarks.headingStructure?.h2Count || 5;
    const targetH3 = benchmarks.headingStructure?.h3Count || 8;
    
    const h2Compliance = Math.abs(headingAnalysis.h2Count - targetH2) <= 1;
    const h3Compliance = Math.abs(headingAnalysis.h3Count - targetH3) <= 2;
    
    return {
      passed: h2Compliance && h3Compliance,
      score: (h2Compliance ? 50 : 0) + (h3Compliance ? 50 : 0),
      h2Compliance,
      h3Compliance
    };
  }

  calculateCompetitiveComplianceScore(factors) {
    return (factors.wordCountCompliance.score + factors.keywordDensityCompliance.score + 
            factors.structureCompliance.score) / 3;
  }

  generateRecommendations(validations) {
    const recommendations = [];
    
    if (!validations.precisionValidation.passed) {
      recommendations.push('Adjust word count and keyword density to match competitor benchmarks exactly');
    }
    
    if (!validations.keywordValidation.passed) {
      recommendations.push('Improve keyword placement in title, first paragraph, and throughout content');
    }
    
    if (!validations.structureValidation.passed) {
      recommendations.push('Restructure headings to match competitor heading distribution');
    }
    
    if (!validations.readabilityValidation.passed) {
      recommendations.push('Improve readability by simplifying sentence structure and vocabulary');
    }
    
    if (!validations.seoValidation.passed) {
      recommendations.push('Enhance SEO elements including title optimization and internal linking');
    }
    
    if (!validations.eeatValidation.passed) {
      recommendations.push('Add more experience, expertise, authority, and trust signals');
    }
    
    if (!validations.semanticValidation.passed) {
      recommendations.push('Better integrate LSI keywords and semantic entities');
    }
    
    return recommendations;
  }

  generateNextSteps(criticalIssues, majorIssues, minorIssues) {
    const nextSteps = [];
    
    if (criticalIssues.length > 0) {
      nextSteps.push('URGENT: Address critical precision issues immediately');
    }
    
    if (majorIssues.length > 0) {
      nextSteps.push('HIGH PRIORITY: Fix major optimization issues');
    }
    
    if (minorIssues.length > 0) {
      nextSteps.push('MEDIUM PRIORITY: Improve minor quality aspects');
    }
    
    if (criticalIssues.length === 0 && majorIssues.length === 0 && minorIssues.length === 0) {
      nextSteps.push('EXCELLENT: Content meets all quality standards');
    }
    
    return nextSteps;
  }
}

module.exports = ContentQualityValidator;