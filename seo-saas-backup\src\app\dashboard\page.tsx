// Professional Dashboard Page
// Enterprise-grade dashboard with analytics and quick actions

'use client'

import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge, ScoreBadge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { cn, formatNumber, formatDate } from "@/lib/utils"
import {
  PlusIcon,
  DocumentTextIcon,
  ChartBarIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
  EyeIcon
} from "@heroicons/react/24/outline"

interface DashboardStats {
  totalProjects: number
  contentGenerated: number
  avgSeoScore: number
  totalAnalyses: number
  monthlyUsage: {
    current: number
    limit: number
  }
}

interface RecentActivity {
  id: string
  type: 'content' | 'analysis' | 'project'
  title: string
  score?: number
  createdAt: string
  status: 'completed' | 'processing' | 'failed'
}

interface QuickAction {
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  href: string
  color: string
}

export default function DashboardPage() {
  const [stats, setStats] = React.useState<DashboardStats>({
    totalProjects: 12,
    contentGenerated: 48,
    avgSeoScore: 78,
    totalAnalyses: 156,
    monthlyUsage: {
      current: 23,
      limit: 100
    }
  })

  const [recentActivity, setRecentActivity] = React.useState<RecentActivity[]>([
    {
      id: '1',
      type: 'content',
      title: 'SEO Services Landing Page',
      score: 85,
      createdAt: '2024-01-15T10:30:00Z',
      status: 'completed'
    },
    {
      id: '2',
      type: 'analysis',
      title: 'Competitor Analysis - Digital Marketing',
      score: 72,
      createdAt: '2024-01-15T09:15:00Z',
      status: 'completed'
    },
    {
      id: '3',
      type: 'project',
      title: 'Healthcare Website Project',
      createdAt: '2024-01-14T16:45:00Z',
      status: 'processing'
    },
    {
      id: '4',
      type: 'content',
      title: 'Blog Post - AI in Marketing',
      score: 91,
      createdAt: '2024-01-14T14:20:00Z',
      status: 'completed'
    },
    {
      id: '5',
      type: 'analysis',
      title: 'Technical SEO Audit',
      score: 67,
      createdAt: '2024-01-14T11:30:00Z',
      status: 'completed'
    }
  ])

  const quickActions: QuickAction[] = [
    {
      title: 'Generate Content',
      description: 'Create SEO-optimized content with AI',
      icon: DocumentTextIcon,
      href: '/dashboard/content',
      color: 'bg-blue-500'
    },
    {
      title: 'SEO Analysis',
      description: 'Analyze existing content performance',
      icon: ChartBarIcon,
      href: '/dashboard/analysis',
      color: 'bg-green-500'
    },
    {
      title: 'New Project',
      description: 'Start a new SEO project',
      icon: PlusIcon,
      href: '/dashboard/projects/new',
      color: 'bg-purple-500'
    },
    {
      title: 'Competitor Research',
      description: 'Research competitor strategies',
      icon: UserGroupIcon,
      href: '/dashboard/competitors',
      color: 'bg-orange-500'
    }
  ]

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'content':
        return DocumentTextIcon
      case 'analysis':
        return ChartBarIcon
      case 'project':
        return PlusIcon
      default:
        return DocumentTextIcon
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100'
      case 'processing':
        return 'text-yellow-600 bg-yellow-100'
      case 'failed':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">
          Welcome back! Here's an overview of your SEO performance.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            <PlusIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProjects}</div>
            <p className="text-xs text-muted-foreground">
              +2 from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Content Generated</CardTitle>
            <DocumentTextIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.contentGenerated}</div>
            <p className="text-xs text-muted-foreground">
              +12 from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg SEO Score</CardTitle>
            <ArrowTrendingUpIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.avgSeoScore}/100</div>
            <p className="text-xs text-muted-foreground">
              +5 points improvement
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Analyses</CardTitle>
            <ChartBarIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stats.totalAnalyses)}</div>
            <p className="text-xs text-muted-foreground">
              +23 this month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Usage and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Monthly Usage */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Usage</CardTitle>
            <CardDescription>
              Content generations this month
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {stats.monthlyUsage.current} / {stats.monthlyUsage.limit}
              </span>
              <Badge variant="secondary">
                {Math.round((stats.monthlyUsage.current / stats.monthlyUsage.limit) * 100)}%
              </Badge>
            </div>
            <Progress 
              value={stats.monthlyUsage.current} 
              max={stats.monthlyUsage.limit} 
              variant={stats.monthlyUsage.current > stats.monthlyUsage.limit * 0.8 ? 'warning' : 'default'}
            />
            <p className="text-xs text-muted-foreground">
              {stats.monthlyUsage.limit - stats.monthlyUsage.current} generations remaining
            </p>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Get started with common tasks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-4 justify-start"
                  asChild
                >
                  <a href={action.href}>
                    <div className="flex items-start space-x-3">
                      <div className={cn("p-2 rounded-md", action.color)}>
                        <action.icon className="h-5 w-5 text-white" />
                      </div>
                      <div className="text-left">
                        <div className="font-medium">{action.title}</div>
                        <div className="text-sm text-muted-foreground">
                          {action.description}
                        </div>
                      </div>
                    </div>
                  </a>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Your latest content generations and analyses
              </CardDescription>
            </div>
            <Button variant="outline" size="sm">
              <EyeIcon className="h-4 w-4 mr-2" />
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.map((activity) => {
              const Icon = getActivityIcon(activity.type)
              return (
                <div key={activity.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex-shrink-0">
                    <div className="p-2 bg-gray-100 rounded-md">
                      <Icon className="h-5 w-5 text-gray-600" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {activity.title}
                      </p>
                      <div className="flex items-center space-x-2">
                        {activity.score && (
                          <ScoreBadge score={activity.score} />
                        )}
                        <Badge 
                          variant="outline" 
                          className={getStatusColor(activity.status)}
                        >
                          {activity.status}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center mt-1 space-x-2">
                      <ClockIcon className="h-3 w-3 text-gray-400" />
                      <p className="text-xs text-gray-500">
                        {formatDate(activity.createdAt)}
                      </p>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
