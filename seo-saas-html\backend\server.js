const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const contentRoutes = require('./routes/content');
const analysisRoutes = require('./routes/analysis');
const exportRoutes = require('./routes/export');
const templatesRoutes = require('./routes/templates');
const seoRoutes = require('./routes/seo');
const projectsRoutes = require('./routes/projects');
const languagesRoutes = require('./routes/languages');
const bulkRoutes = require('./routes/bulk');
const precisionRoutes = require('./routes/precision');

const app = express();

// Security middleware
app.use(helmet());

// CORS configuration
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:5555'],
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Compression middleware
app.use(compression());

// Logging middleware
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/content', contentRoutes);
app.use('/api/analysis', analysisRoutes);
app.use('/api/export', exportRoutes);
app.use('/api/templates', templatesRoutes);
app.use('/api/seo', seoRoutes);
app.use('/api/projects', projectsRoutes);
app.use('/api/languages', languagesRoutes);
app.use('/api/bulk', bulkRoutes);
app.use('/api/precision', precisionRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  
  const status = err.status || 500;
  const message = err.message || 'Internal Server Error';
  
  res.status(status).json({
    error: {
      status,
      message,
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    }
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: { status: 404, message: 'Not Found' } });
});

const PORT = process.env.PORT || 3001;

app.listen(PORT, () => {
  console.log(`
    🚀 SEO SAAS Backend Server Running
    📡 Port: ${PORT}
    🌍 Environment: ${process.env.NODE_ENV}
    🔗 Health Check: http://localhost:${PORT}/api/health
  `);
});