// Professional Badge Component
// Enterprise-grade badge for scores, priorities, and status indicators

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        success:
          "border-transparent bg-green-100 text-green-800 hover:bg-green-200",
        warning:
          "border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
        error:
          "border-transparent bg-red-100 text-red-800 hover:bg-red-200",
        info:
          "border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

// Score Badge Component
interface ScoreBadgeProps {
  score: number
  className?: string
}

function ScoreBadge({ score, className }: ScoreBadgeProps) {
  const getVariant = (score: number) => {
    if (score >= 80) return "success"
    if (score >= 60) return "warning" 
    if (score >= 40) return "error"
    return "destructive"
  }

  return (
    <Badge variant={getVariant(score)} className={className}>
      {score}/100
    </Badge>
  )
}

// Priority Badge Component
interface PriorityBadgeProps {
  priority: 'high' | 'medium' | 'low'
  className?: string
}

function PriorityBadge({ priority, className }: PriorityBadgeProps) {
  const getVariant = (priority: string) => {
    switch (priority) {
      case 'high': return "error"
      case 'medium': return "warning"
      case 'low': return "success"
      default: return "secondary"
    }
  }

  return (
    <Badge variant={getVariant(priority)} className={className}>
      {priority.toUpperCase()}
    </Badge>
  )
}

// Status Badge Component
interface StatusBadgeProps {
  status: 'active' | 'inactive' | 'pending' | 'completed' | 'failed'
  className?: string
}

function StatusBadge({ status, className }: StatusBadgeProps) {
  const getVariant = (status: string) => {
    switch (status) {
      case 'active': return "success"
      case 'completed': return "success"
      case 'pending': return "warning"
      case 'inactive': return "secondary"
      case 'failed': return "error"
      default: return "secondary"
    }
  }

  return (
    <Badge variant={getVariant(status)} className={className}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  )
}

export { Badge, badgeVariants, ScoreBadge, PriorityBadge, StatusBadge }
