const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { verifyToken, checkUsageLimit } = require('../middleware/auth');
const BulkProcessor = require('../services/bulkProcessor');
const BulkExporter = require('../services/bulkExporter');

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedExtensions = ['.csv', '.xlsx', '.xls'];
    const fileExtension = path.extname(file.originalname).toLowerCase();
    
    if (allowedExtensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error('Only CSV and Excel files are allowed'));
    }
  }
});

const bulkProcessor = new BulkProcessor();
const bulkExporter = new BulkExporter();

// Upload keywords file endpoint
router.post('/keywords/upload', verifyToken, upload.single('keywordFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }
    
    const fileExtension = path.extname(req.file.originalname).toLowerCase();
    const fileType = fileExtension === '.csv' ? 'csv' : 'xlsx';
    
    // Parse keywords from file
    const keywords = await bulkProcessor.parseKeywordFile(req.file.path, fileType);
    
    // Clean up uploaded file
    await fs.unlink(req.file.path);
    
    if (keywords.length === 0) {
      return res.status(400).json({ error: 'No valid keywords found in file' });
    }
    
    if (keywords.length > 500) {
      return res.status(400).json({ error: 'Maximum 500 keywords allowed per upload' });
    }
    
    res.json({
      success: true,
      keywords,
      totalKeywords: keywords.length,
      preview: keywords.slice(0, 10) // Show first 10 keywords as preview
    });
    
  } catch (error) {
    // Clean up uploaded file on error
    if (req.file?.path) {
      try {
        await fs.unlink(req.file.path);
      } catch (unlinkError) {
        console.error('Error cleaning up uploaded file:', unlinkError);
      }
    }
    
    console.error('File upload error:', error);
    res.status(500).json({ error: error.message || 'Failed to process uploaded file' });
  }
});

// Parse textarea keywords endpoint
router.post('/keywords/parse-text', verifyToken, async (req, res) => {
  try {
    const { keywordText, defaultSettings = {} } = req.body;
    
    if (!keywordText || typeof keywordText !== 'string') {
      return res.status(400).json({ error: 'Keyword text is required' });
    }
    
    const keywords = bulkProcessor.parseTextareaInput(keywordText.trim(), defaultSettings);
    
    if (keywords.length === 0) {
      return res.status(400).json({ error: 'No valid keywords found' });
    }
    
    if (keywords.length > 500) {
      return res.status(400).json({ error: 'Maximum 500 keywords allowed per batch' });
    }
    
    res.json({
      success: true,
      keywords,
      totalKeywords: keywords.length
    });
    
  } catch (error) {
    console.error('Text parsing error:', error);
    res.status(500).json({ error: 'Failed to parse keyword text' });
  }
});

// Start bulk content generation
router.post('/content/generate', verifyToken, checkUsageLimit, async (req, res) => {
  try {
    const {
      keywords,
      settings = {},
      projectId = null
    } = req.body;
    
    if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
      return res.status(400).json({ error: 'Keywords array is required' });
    }
    
    // Validate user's subscription limits
    const keywordCount = keywords.length;
    if (keywordCount > 500) {
      return res.status(400).json({ error: 'Maximum 500 keywords allowed per bulk operation' });
    }
    
    // Check if user has enough credits/quota
    // This would integrate with your subscription/billing system
    
    const result = await bulkProcessor.createBulkOperation(
      req.user.id,
      'content_generation',
      keywords,
      settings,
      projectId
    );
    
    res.json({
      success: true,
      operationId: result.operationId,
      totalKeywords: result.totalKeywords,
      estimatedCompletion: result.estimatedCompletion,
      message: 'Bulk content generation started'
    });
    
  } catch (error) {
    console.error('Bulk content generation error:', error);
    res.status(500).json({ error: error.message || 'Failed to start bulk content generation' });
  }
});

// Start bulk meta tag generation
router.post('/meta-tags/generate', verifyToken, checkUsageLimit, async (req, res) => {
  try {
    const {
      keywords,
      settings = {},
      projectId = null
    } = req.body;
    
    if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
      return res.status(400).json({ error: 'Keywords array is required' });
    }
    
    const result = await bulkProcessor.createBulkOperation(
      req.user.id,
      'meta_tags',
      keywords,
      settings,
      projectId
    );
    
    res.json({
      success: true,
      operationId: result.operationId,
      totalKeywords: result.totalKeywords,
      estimatedCompletion: result.estimatedCompletion,
      message: 'Bulk meta tag generation started'
    });
    
  } catch (error) {
    console.error('Bulk meta tag generation error:', error);
    res.status(500).json({ error: error.message || 'Failed to start bulk meta tag generation' });
  }
});

// Start bulk competitor analysis
router.post('/competitor-analysis/generate', verifyToken, checkUsageLimit, async (req, res) => {
  try {
    const {
      keywords,
      settings = {},
      projectId = null
    } = req.body;
    
    if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
      return res.status(400).json({ error: 'Keywords array is required' });
    }
    
    const result = await bulkProcessor.createBulkOperation(
      req.user.id,
      'competitor_analysis',
      keywords,
      settings,
      projectId
    );
    
    res.json({
      success: true,
      operationId: result.operationId,
      totalKeywords: result.totalKeywords,
      estimatedCompletion: result.estimatedCompletion,
      message: 'Bulk competitor analysis started'
    });
    
  } catch (error) {
    console.error('Bulk competitor analysis error:', error);
    res.status(500).json({ error: error.message || 'Failed to start bulk competitor analysis' });
  }
});

// Get operation status
router.get('/operations/:operationId/status', verifyToken, async (req, res) => {
  try {
    const { operationId } = req.params;
    
    const status = await bulkProcessor.getOperationStatus(operationId, req.user.id);
    
    res.json({
      success: true,
      operation: status
    });
    
  } catch (error) {
    console.error('Get operation status error:', error);
    res.status(404).json({ error: error.message || 'Operation not found' });
  }
});

// Get operation results
router.get('/operations/:operationId/results', verifyToken, async (req, res) => {
  try {
    const { operationId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 50, 100);
    
    const results = await bulkProcessor.getOperationResults(operationId, req.user.id, page, limit);
    
    res.json({
      success: true,
      ...results
    });
    
  } catch (error) {
    console.error('Get operation results error:', error);
    res.status(404).json({ error: error.message || 'Results not found' });
  }
});

// Cancel operation
router.post('/operations/:operationId/cancel', verifyToken, async (req, res) => {
  try {
    const { operationId } = req.params;
    
    const result = await bulkProcessor.cancelOperation(operationId, req.user.id);
    
    res.json({
      success: true,
      message: 'Operation cancelled successfully'
    });
    
  } catch (error) {
    console.error('Cancel operation error:', error);
    res.status(400).json({ error: error.message || 'Failed to cancel operation' });
  }
});

// Get user's bulk operations
router.get('/operations', verifyToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 20, 50);
    
    const operations = await bulkProcessor.getUserOperations(req.user.id, page, limit);
    
    res.json({
      success: true,
      ...operations
    });
    
  } catch (error) {
    console.error('Get user operations error:', error);
    res.status(500).json({ error: 'Failed to fetch operations' });
  }
});

// Create export
router.post('/operations/:operationId/export', verifyToken, async (req, res) => {
  try {
    const { operationId } = req.params;
    const { format = 'zip' } = req.body;
    
    const allowedFormats = ['zip', 'csv', 'excel', 'json'];
    if (!allowedFormats.includes(format.toLowerCase())) {
      return res.status(400).json({ error: 'Invalid export format' });
    }
    
    const exportResult = await bulkExporter.createBulkExport(operationId, req.user.id, format);
    
    res.json({
      success: true,
      exportId: exportResult.exportId,
      fileSize: exportResult.fileSize,
      expiresAt: exportResult.expiresAt,
      message: 'Export created successfully'
    });
    
  } catch (error) {
    console.error('Create export error:', error);
    res.status(500).json({ error: error.message || 'Failed to create export' });
  }
});

// Download export
router.get('/exports/:exportId/download', verifyToken, async (req, res) => {
  try {
    const { exportId } = req.params;
    
    const downloadInfo = await bulkExporter.getExportDownload(exportId, req.user.id);
    
    res.setHeader('Content-Type', downloadInfo.contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${downloadInfo.fileName}"`);
    res.setHeader('Content-Length', downloadInfo.fileSize);
    
    // Stream the file
    const fileStream = require('fs').createReadStream(downloadInfo.filePath);
    fileStream.pipe(res);
    
    fileStream.on('error', (error) => {
      console.error('File stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: 'Failed to download file' });
      }
    });
    
  } catch (error) {
    console.error('Download export error:', error);
    res.status(404).json({ error: error.message || 'Export not found' });
  }
});

// Get user's exports
router.get('/exports', verifyToken, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 20, 50);
    
    const exports = await bulkExporter.getUserExports(req.user.id, page, limit);
    
    res.json({
      success: true,
      ...exports
    });
    
  } catch (error) {
    console.error('Get user exports error:', error);
    res.status(500).json({ error: 'Failed to fetch exports' });
  }
});

// Cleanup expired exports (admin endpoint or scheduled job)
router.post('/cleanup-expired-exports', verifyToken, async (req, res) => {
  try {
    // You might want to add admin authentication here
    await bulkExporter.cleanupExpiredExports();
    
    res.json({
      success: true,
      message: 'Expired exports cleaned up successfully'
    });
    
  } catch (error) {
    console.error('Cleanup error:', error);
    res.status(500).json({ error: 'Failed to cleanup expired exports' });
  }
});

// Get bulk processing statistics (for user dashboard)
router.get('/stats', verifyToken, async (req, res) => {
  try {
    const { createClient } = require('@supabase/supabase-js');
    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );
    
    // Get user's bulk operation statistics
    const { data: operations } = await supabase
      .from('bulk_operations')
      .select('operation_type, status, total_keywords, successful_keywords, created_at')
      .eq('user_id', req.user.id)
      .order('created_at', { ascending: false })
      .limit(30);
    
    const stats = {
      totalOperations: operations?.length || 0,
      completedOperations: operations?.filter(op => op.status === 'completed').length || 0,
      totalKeywordsProcessed: operations?.reduce((sum, op) => sum + (op.successful_keywords || 0), 0) || 0,
      operationsByType: {},
      recentActivity: operations?.slice(0, 10) || []
    };
    
    // Group by operation type
    operations?.forEach(op => {
      if (!stats.operationsByType[op.operation_type]) {
        stats.operationsByType[op.operation_type] = {
          total: 0,
          completed: 0,
          keywordsProcessed: 0
        };
      }
      
      stats.operationsByType[op.operation_type].total++;
      if (op.status === 'completed') {
        stats.operationsByType[op.operation_type].completed++;
      }
      stats.operationsByType[op.operation_type].keywordsProcessed += op.successful_keywords || 0;
    });
    
    res.json({
      success: true,
      stats
    });
    
  } catch (error) {
    console.error('Get bulk stats error:', error);
    res.status(500).json({ error: 'Failed to fetch statistics' });
  }
});

module.exports = router;