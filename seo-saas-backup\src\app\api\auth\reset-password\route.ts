import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { z } from 'zod';
import { logUserActivity } from '@/lib/supabase';

const resetPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const { email } = resetPasswordSchema.parse(body);
    
    const supabase = createSupabaseServerClient();
    
    // Send password reset email
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${request.nextUrl.origin}/auth/reset-password`,
    });
    
    if (error) {
      // Log failed password reset attempt
      await logUserActivity({
        action_type: 'password_reset_failed',
        action_details: {
          email,
          error: error.message,
        },
        ip_address: request.ip,
        user_agent: request.headers.get('user-agent') || undefined,
      });
      
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    // Log password reset request
    await logUserActivity({
      action_type: 'password_reset_requested',
      action_details: {
        email,
      },
      ip_address: request.ip,
      user_agent: request.headers.get('user-agent') || undefined,
    });
    
    return NextResponse.json({
      success: true,
      message: 'Password reset email sent. Please check your inbox.',
    });
    
  } catch (error) {
    console.error('Password reset error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}