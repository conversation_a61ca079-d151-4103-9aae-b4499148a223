const natural = require('natural');
const axios = require('axios');

class GrammarReadabilityOptimizer {
  constructor() {
    this.tokenizer = new natural.WordTokenizer();
    this.sentenceTokenizer = new natural.SentenceTokenizer();
    this.tagger = new natural.BrillPOSTagger('EN', natural.BrillPOSTagger.lexicon, natural.BrillPOSTagger.rules);
    this.stemmer = natural.PorterStemmer;
    this.spellcheck = new natural.Spellcheck(natural.Spellcheck.en_US);
    
    // Grammar rules database
    this.grammarRules = this.initializeGrammarRules();
    this.readabilityTargets = this.initializeReadabilityTargets();
    this.commonErrors = this.initializeCommonErrors();
  }

  // Main method to optimize grammar and readability
  async optimizeGrammarAndReadability(content, keyword, options = {}) {
    try {
      console.log(`Starting grammar and readability optimization for keyword: ${keyword}`);

      // Perform comprehensive grammar analysis
      const grammarAnalysis = await this.analyzeGrammar(content);
      
      // Analyze readability metrics
      const readabilityAnalysis = this.analyzeReadability(content);
      
      // Detect and fix grammar errors
      const grammarCorrected = await this.correctGrammarErrors(content, grammarAnalysis);
      
      // Optimize sentence structure
      const sentenceOptimized = this.optimizeSentenceStructure(grammarCorrected.content, readabilityAnalysis);
      
      // Enhance vocabulary and word choice
      const vocabularyEnhanced = this.enhanceVocabulary(sentenceOptimized.content, keyword, options);
      
      // Improve flow and transitions
      const flowOptimized = this.improveContentFlow(vocabularyEnhanced.content);
      
      // Fine-tune readability
      const readabilityOptimized = this.finetuneReadability(flowOptimized.content, options.targetAudience);
      
      // Perform final grammar check
      const finalValidation = await this.performFinalValidation(readabilityOptimized.content);

      return {
        success: true,
        content: readabilityOptimized.content,
        optimization: {
          grammarAnalysis,
          readabilityAnalysis,
          corrections: {
            grammar: grammarCorrected.corrections,
            sentences: sentenceOptimized.improvements,
            vocabulary: vocabularyEnhanced.enhancements,
            flow: flowOptimized.improvements
          },
          finalValidation,
          metrics: {
            before: {
              grammarScore: grammarAnalysis.overallScore,
              readabilityScore: readabilityAnalysis.overallScore
            },
            after: {
              grammarScore: finalValidation.grammarScore,
              readabilityScore: finalValidation.readabilityScore
            }
          }
        },
        metadata: {
          grammarScore: finalValidation.grammarScore,
          readabilityScore: finalValidation.readabilityScore,
          fleschReadingEase: finalValidation.metrics.fleschReadingEase,
          gradeLevel: finalValidation.metrics.gradeLevel,
          processedAt: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('Grammar and readability optimization error:', error);
      return {
        success: false,
        error: error.message,
        content: content
      };
    }
  }

  // Initialize grammar rules
  initializeGrammarRules() {
    return {
      subjectVerbAgreement: {
        singular: ['is', 'was', 'has', 'does'],
        plural: ['are', 'were', 'have', 'do']
      },
      articleUsage: {
        vowelSounds: ['a', 'e', 'i', 'o', 'u'],
        consonantSounds: ['b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'q', 'r', 's', 't', 'v', 'w', 'x', 'y', 'z']
      },
      verbTenses: {
        present: ['am', 'is', 'are', 'have', 'has', 'do', 'does'],
        past: ['was', 'were', 'had', 'did'],
        future: ['will', 'shall', 'going to']
      },
      punctuationRules: {
        sentenceEnders: ['.', '!', '?'],
        clauseSeparators: [',', ';', ':'],
        quotationMarks: ['"', "'"]
      },
      commonPatterns: {
        passiveVoice: /\b(is|are|was|were|been|being)\s+\w+ed\b/g,
        wordyPhrases: {
          'in order to': 'to',
          'due to the fact that': 'because',
          'at this point in time': 'now',
          'in the event that': 'if'
        }
      }
    };
  }

  // Initialize readability targets
  initializeReadabilityTargets() {
    return {
      general: {
        fleschReadingEase: { min: 60, max: 70 },
        gradeLevel: { min: 8, max: 10 },
        avgSentenceLength: { min: 15, max: 20 },
        avgWordLength: { min: 4, max: 6 }
      },
      professional: {
        fleschReadingEase: { min: 30, max: 50 },
        gradeLevel: { min: 12, max: 16 },
        avgSentenceLength: { min: 20, max: 25 },
        avgWordLength: { min: 5, max: 7 }
      },
      casual: {
        fleschReadingEase: { min: 70, max: 90 },
        gradeLevel: { min: 6, max: 8 },
        avgSentenceLength: { min: 10, max: 15 },
        avgWordLength: { min: 3, max: 5 }
      }
    };
  }

  // Initialize common errors database
  initializeCommonErrors() {
    return {
      spelling: {
        'recieve': 'receive',
        'beleive': 'believe',
        'occured': 'occurred',
        'seperate': 'separate',
        'definately': 'definitely',
        'accomodate': 'accommodate',
        'embarass': 'embarrass',
        'occurence': 'occurrence',
        'concensus': 'consensus',
        'experiance': 'experience'
      },
      grammar: {
        'could of': 'could have',
        'would of': 'would have',
        'should of': 'should have',
        'your welcome': "you're welcome",
        'its been': "it's been",
        'whos': "who's",
        'theyre': "they're",
        'their going': "they're going",
        'your going': "you're going",
        'alot': 'a lot'
      },
      redundancy: {
        'absolutely essential': 'essential',
        'advance planning': 'planning',
        'basic fundamentals': 'fundamentals',
        'close proximity': 'proximity',
        'completely finished': 'finished',
        'end result': 'result',
        'final outcome': 'outcome',
        'free gift': 'gift',
        'future plans': 'plans',
        'past history': 'history'
      },
      wordiness: {
        'at the present time': 'now',
        'in spite of the fact that': 'although',
        'in the near future': 'soon',
        'has the ability to': 'can',
        'is able to': 'can',
        'in order to': 'to',
        'due to the fact that': 'because',
        'for the purpose of': 'to',
        'with regard to': 'about',
        'in reference to': 'about'
      }
    };
  }

  // Analyze grammar
  async analyzeGrammar(content) {
    const analysis = {
      errors: [],
      warnings: [],
      suggestions: [],
      sentenceStructure: [],
      overallScore: 100
    };

    const sentences = this.sentenceTokenizer.tokenize(content);
    
    // Analyze each sentence
    for (const sentence of sentences) {
      const sentenceAnalysis = this.analyzeSentenceGrammar(sentence);
      
      analysis.errors.push(...sentenceAnalysis.errors);
      analysis.warnings.push(...sentenceAnalysis.warnings);
      analysis.suggestions.push(...sentenceAnalysis.suggestions);
      analysis.sentenceStructure.push(sentenceAnalysis.structure);
    }

    // Check for common errors
    const commonErrorsFound = this.checkCommonErrors(content);
    analysis.errors.push(...commonErrorsFound);

    // Check spelling
    const spellingErrors = await this.checkSpelling(content);
    analysis.errors.push(...spellingErrors);

    // Check punctuation
    const punctuationIssues = this.checkPunctuation(content);
    analysis.warnings.push(...punctuationIssues);

    // Calculate overall score
    analysis.overallScore = this.calculateGrammarScore(analysis);

    return analysis;
  }

  // Analyze individual sentence grammar
  analyzeSentenceGrammar(sentence) {
    const analysis = {
      errors: [],
      warnings: [],
      suggestions: [],
      structure: {}
    };

    const words = this.tokenizer.tokenize(sentence);
    const taggedWords = this.tagger.tag(words);

    // Check subject-verb agreement
    const agreementIssues = this.checkSubjectVerbAgreement(taggedWords);
    analysis.errors.push(...agreementIssues);

    // Check article usage
    const articleIssues = this.checkArticleUsage(sentence);
    analysis.errors.push(...articleIssues);

    // Check verb tense consistency
    const tenseIssues = this.checkVerbTenseConsistency(taggedWords);
    analysis.warnings.push(...tenseIssues);

    // Analyze sentence structure
    analysis.structure = {
      length: words.length,
      complexity: this.calculateSentenceComplexity(taggedWords),
      hasSubject: this.hasSubject(taggedWords),
      hasVerb: this.hasVerb(taggedWords),
      isFragment: words.length < 3 || !analysis.structure.hasSubject || !analysis.structure.hasVerb,
      isRunOn: words.length > 40
    };

    // Generate suggestions
    if (analysis.structure.isFragment) {
      analysis.suggestions.push({
        type: 'sentence_fragment',
        message: 'This appears to be a sentence fragment. Consider adding a subject or verb.',
        sentence
      });
    }

    if (analysis.structure.isRunOn) {
      analysis.suggestions.push({
        type: 'run_on_sentence',
        message: 'This sentence is too long. Consider breaking it into smaller sentences.',
        sentence
      });
    }

    return analysis;
  }

  // Check subject-verb agreement
  checkSubjectVerbAgreement(taggedWords) {
    const errors = [];
    
    for (let i = 0; i < taggedWords.length - 1; i++) {
      const current = taggedWords[i];
      const next = taggedWords[i + 1];
      
      // Simple subject-verb agreement check
      if (current.pos === 'NN' && next.pos === 'VBZ') {
        // Singular noun with singular verb - correct
      } else if (current.pos === 'NNS' && next.pos === 'VBP') {
        // Plural noun with plural verb - correct
      } else if (current.pos === 'NN' && next.pos === 'VBP' && this.grammarRules.subjectVerbAgreement.plural.includes(next.token.toLowerCase())) {
        errors.push({
          type: 'subject_verb_disagreement',
          message: `Subject-verb disagreement: "${current.token}" (singular) with "${next.token}" (plural)`,
          position: i,
          suggestion: this.getSingularVerb(next.token)
        });
      } else if (current.pos === 'NNS' && next.pos === 'VBZ' && this.grammarRules.subjectVerbAgreement.singular.includes(next.token.toLowerCase())) {
        errors.push({
          type: 'subject_verb_disagreement',
          message: `Subject-verb disagreement: "${current.token}" (plural) with "${next.token}" (singular)`,
          position: i,
          suggestion: this.getPluralVerb(next.token)
        });
      }
    }
    
    return errors;
  }

  // Check article usage
  checkArticleUsage(sentence) {
    const errors = [];
    const words = sentence.split(/\s+/);
    
    for (let i = 0; i < words.length - 1; i++) {
      const current = words[i].toLowerCase();
      const next = words[i + 1].toLowerCase();
      
      if (current === 'a' && this.grammarRules.articleUsage.vowelSounds.includes(next[0])) {
        errors.push({
          type: 'article_usage',
          message: `Use "an" before vowel sound: "a ${next}" should be "an ${next}"`,
          position: i,
          suggestion: 'an'
        });
      } else if (current === 'an' && this.grammarRules.articleUsage.consonantSounds.includes(next[0])) {
        errors.push({
          type: 'article_usage',
          message: `Use "a" before consonant sound: "an ${next}" should be "a ${next}"`,
          position: i,
          suggestion: 'a'
        });
      }
    }
    
    return errors;
  }

  // Check verb tense consistency
  checkVerbTenseConsistency(taggedWords) {
    const warnings = [];
    const verbTenses = [];
    
    // Collect all verb tenses in the sentence
    taggedWords.forEach((word, index) => {
      if (word.pos.startsWith('VB')) {
        let tense = 'present';
        if (word.pos === 'VBD' || word.pos === 'VBN') {
          tense = 'past';
        } else if (this.grammarRules.verbTenses.future.some(marker => 
          index > 0 && taggedWords[index - 1].token.toLowerCase() === marker)) {
          tense = 'future';
        }
        verbTenses.push({ word: word.token, tense, position: index });
      }
    });
    
    // Check for tense shifts
    if (verbTenses.length > 1) {
      const primaryTense = verbTenses[0].tense;
      verbTenses.slice(1).forEach(verb => {
        if (verb.tense !== primaryTense) {
          warnings.push({
            type: 'tense_inconsistency',
            message: `Possible tense shift: "${verbTenses[0].word}" (${primaryTense}) to "${verb.word}" (${verb.tense})`,
            position: verb.position
          });
        }
      });
    }
    
    return warnings;
  }

  // Check common errors
  checkCommonErrors(content) {
    const errors = [];
    const contentLower = content.toLowerCase();
    
    // Check spelling errors
    Object.entries(this.commonErrors.spelling).forEach(([error, correction]) => {
      if (contentLower.includes(error)) {
        errors.push({
          type: 'spelling_error',
          message: `Spelling error: "${error}" should be "${correction}"`,
          error,
          correction
        });
      }
    });
    
    // Check grammar errors
    Object.entries(this.commonErrors.grammar).forEach(([error, correction]) => {
      if (contentLower.includes(error)) {
        errors.push({
          type: 'grammar_error',
          message: `Grammar error: "${error}" should be "${correction}"`,
          error,
          correction
        });
      }
    });
    
    // Check redundancy
    Object.entries(this.commonErrors.redundancy).forEach(([error, correction]) => {
      if (contentLower.includes(error)) {
        errors.push({
          type: 'redundancy',
          message: `Redundant phrase: "${error}" can be simplified to "${correction}"`,
          error,
          correction
        });
      }
    });
    
    // Check wordiness
    Object.entries(this.commonErrors.wordiness).forEach(([error, correction]) => {
      if (contentLower.includes(error)) {
        errors.push({
          type: 'wordiness',
          message: `Wordy phrase: "${error}" can be simplified to "${correction}"`,
          error,
          correction
        });
      }
    });
    
    return errors;
  }

  // Check spelling
  async checkSpelling(content) {
    const errors = [];
    const words = this.tokenizer.tokenize(content);
    
    words.forEach((word, index) => {
      // Skip if word is all caps (likely an acronym)
      if (word === word.toUpperCase()) return;
      
      // Simple spell check (in production, use a proper spell checker API)
      if (!this.isValidWord(word)) {
        const suggestions = this.getSuggestedSpellings(word);
        errors.push({
          type: 'spelling_error',
          message: `Possible spelling error: "${word}"`,
          word,
          position: index,
          suggestions
        });
      }
    });
    
    return errors;
  }

  // Check if word is valid (simplified)
  isValidWord(word) {
    // This is a simplified check - in production, use a proper dictionary
    const commonWords = new Set([
      'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i',
      'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at'
    ]);
    
    return commonWords.has(word.toLowerCase()) || word.length < 3;
  }

  // Get suggested spellings (simplified)
  getSuggestedSpellings(word) {
    // In production, use a proper spell checker API
    return [];
  }

  // Check punctuation
  checkPunctuation(content) {
    const issues = [];
    
    // Check for double spaces
    if (content.includes('  ')) {
      issues.push({
        type: 'punctuation_spacing',
        message: 'Remove double spaces',
        pattern: '  '
      });
    }
    
    // Check for missing space after punctuation
    const punctuationPattern = /[,.!?;:][a-zA-Z]/g;
    const matches = content.match(punctuationPattern);
    if (matches) {
      matches.forEach(match => {
        issues.push({
          type: 'punctuation_spacing',
          message: `Add space after punctuation: "${match}"`,
          pattern: match
        });
      });
    }
    
    // Check for multiple punctuation marks
    const multiplePunctuation = /[.!?]{2,}/g;
    if (multiplePunctuation.test(content)) {
      issues.push({
        type: 'punctuation_multiple',
        message: 'Avoid multiple punctuation marks',
        pattern: multiplePunctuation
      });
    }
    
    return issues;
  }

  // Calculate grammar score
  calculateGrammarScore(analysis) {
    let score = 100;
    
    // Deduct points for errors
    score -= analysis.errors.length * 5;
    score -= analysis.warnings.length * 2;
    
    // Ensure score doesn't go below 0
    return Math.max(0, score);
  }

  // Analyze readability
  analyzeReadability(content) {
    const sentences = this.sentenceTokenizer.tokenize(content);
    const words = this.tokenizer.tokenize(content);
    
    // Calculate basic metrics
    const totalSentences = sentences.length;
    const totalWords = words.length;
    const totalSyllables = words.reduce((sum, word) => sum + this.countSyllables(word), 0);
    
    // Calculate average metrics
    const avgSentenceLength = totalWords / totalSentences;
    const avgSyllablesPerWord = totalSyllables / totalWords;
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / totalWords;
    
    // Calculate readability scores
    const fleschReadingEase = this.calculateFleschReadingEase(avgSentenceLength, avgSyllablesPerWord);
    const fleschKincaidGrade = this.calculateFleschKincaidGrade(avgSentenceLength, avgSyllablesPerWord);
    const gunningFog = this.calculateGunningFog(avgSentenceLength, words);
    const colemanLiau = this.calculateColemanLiau(content);
    const automatedReadability = this.calculateAutomatedReadability(avgSentenceLength, avgWordLength);
    
    // Analyze sentence variety
    const sentenceVariety = this.analyzeSentenceVariety(sentences);
    
    // Analyze paragraph structure
    const paragraphAnalysis = this.analyzeParagraphStructure(content);
    
    // Calculate overall readability score
    const overallScore = this.calculateOverallReadabilityScore({
      fleschReadingEase,
      fleschKincaidGrade,
      gunningFog,
      colemanLiau,
      automatedReadability
    });
    
    return {
      metrics: {
        totalSentences,
        totalWords,
        totalSyllables,
        avgSentenceLength,
        avgSyllablesPerWord,
        avgWordLength
      },
      scores: {
        fleschReadingEase,
        fleschKincaidGrade,
        gunningFog,
        colemanLiau,
        automatedReadability
      },
      analysis: {
        sentenceVariety,
        paragraphAnalysis
      },
      overallScore,
      recommendations: this.generateReadabilityRecommendations({
        avgSentenceLength,
        avgWordLength,
        fleschReadingEase,
        sentenceVariety
      })
    };
  }

  // Count syllables in a word
  countSyllables(word) {
    const vowels = 'aeiouy';
    let count = 0;
    let previousWasVowel = false;
    
    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i].toLowerCase());
      if (isVowel && !previousWasVowel) {
        count++;
      }
      previousWasVowel = isVowel;
    }
    
    // Adjust for silent e
    if (word.endsWith('e') && count > 1) {
      count--;
    }
    
    // Ensure at least one syllable
    return Math.max(1, count);
  }

  // Calculate Flesch Reading Ease
  calculateFleschReadingEase(avgSentenceLength, avgSyllablesPerWord) {
    return 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
  }

  // Calculate Flesch-Kincaid Grade Level
  calculateFleschKincaidGrade(avgSentenceLength, avgSyllablesPerWord) {
    return (0.39 * avgSentenceLength) + (11.8 * avgSyllablesPerWord) - 15.59;
  }

  // Calculate Gunning Fog Index
  calculateGunningFog(avgSentenceLength, words) {
    const complexWords = words.filter(word => this.countSyllables(word) >= 3).length;
    const percentComplexWords = (complexWords / words.length) * 100;
    return 0.4 * (avgSentenceLength + percentComplexWords);
  }

  // Calculate Coleman-Liau Index
  calculateColemanLiau(content) {
    const letters = content.replace(/[^a-zA-Z]/g, '').length;
    const words = this.tokenizer.tokenize(content).length;
    const sentences = this.sentenceTokenizer.tokenize(content).length;
    
    const L = (letters / words) * 100;
    const S = (sentences / words) * 100;
    
    return 0.0588 * L - 0.296 * S - 15.8;
  }

  // Calculate Automated Readability Index
  calculateAutomatedReadability(avgSentenceLength, avgWordLength) {
    return 4.71 * avgWordLength + 0.5 * avgSentenceLength - 21.43;
  }

  // Analyze sentence variety
  analyzeSentenceVariety(sentences) {
    const lengths = sentences.map(s => this.tokenizer.tokenize(s).length);
    const avgLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
    const variance = lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Analyze sentence types
    const sentenceTypes = {
      simple: 0,
      compound: 0,
      complex: 0,
      compoundComplex: 0
    };
    
    sentences.forEach(sentence => {
      const type = this.classifySentenceType(sentence);
      sentenceTypes[type]++;
    });
    
    return {
      lengthVariation: {
        min: Math.min(...lengths),
        max: Math.max(...lengths),
        average: avgLength,
        standardDeviation
      },
      sentenceTypes,
      varietyScore: this.calculateVarietyScore(standardDeviation, sentenceTypes)
    };
  }

  // Classify sentence type
  classifySentenceType(sentence) {
    const hasConjunction = /\b(and|but|or|nor|for|yet|so)\b/.test(sentence);
    const hasSubordinateClause = /\b(because|although|while|when|if|since|unless)\b/.test(sentence);
    
    if (hasConjunction && hasSubordinateClause) return 'compoundComplex';
    if (hasSubordinateClause) return 'complex';
    if (hasConjunction) return 'compound';
    return 'simple';
  }

  // Calculate variety score
  calculateVarietyScore(standardDeviation, sentenceTypes) {
    // Higher standard deviation = more variety in length
    const lengthVarietyScore = Math.min(100, standardDeviation * 10);
    
    // More diverse sentence types = better variety
    const typeCount = Object.values(sentenceTypes).filter(count => count > 0).length;
    const typeVarietyScore = (typeCount / 4) * 100;
    
    return (lengthVarietyScore + typeVarietyScore) / 2;
  }

  // Analyze paragraph structure
  analyzeParagraphStructure(content) {
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const paragraphLengths = paragraphs.map(p => this.tokenizer.tokenize(p).length);
    
    return {
      count: paragraphs.length,
      avgLength: paragraphLengths.reduce((sum, len) => sum + len, 0) / paragraphs.length,
      minLength: Math.min(...paragraphLengths),
      maxLength: Math.max(...paragraphLengths),
      balance: this.calculateParagraphBalance(paragraphLengths)
    };
  }

  // Calculate paragraph balance
  calculateParagraphBalance(lengths) {
    if (lengths.length < 2) return 100;
    
    const avg = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
    const variance = lengths.reduce((sum, len) => sum + Math.pow(len - avg, 2), 0) / lengths.length;
    const coefficientOfVariation = Math.sqrt(variance) / avg;
    
    // Lower coefficient of variation = better balance
    return Math.max(0, 100 - (coefficientOfVariation * 100));
  }

  // Calculate overall readability score
  calculateOverallReadabilityScore(scores) {
    // Normalize scores to 0-100 scale
    const normalizedFlesch = Math.max(0, Math.min(100, scores.fleschReadingEase));
    const normalizedGrade = Math.max(0, 100 - (scores.fleschKincaidGrade * 5));
    const normalizedFog = Math.max(0, 100 - (scores.gunningFog * 5));
    const normalizedColeman = Math.max(0, 100 - (scores.colemanLiau * 5));
    const normalizedARI = Math.max(0, 100 - (scores.automatedReadability * 5));
    
    // Average all normalized scores
    return (normalizedFlesch + normalizedGrade + normalizedFog + normalizedColeman + normalizedARI) / 5;
  }

  // Generate readability recommendations
  generateReadabilityRecommendations(metrics) {
    const recommendations = [];
    
    if (metrics.avgSentenceLength > 25) {
      recommendations.push({
        type: 'sentence_length',
        message: 'Sentences are too long. Aim for 15-20 words per sentence.',
        priority: 'high'
      });
    }
    
    if (metrics.avgWordLength > 6) {
      recommendations.push({
        type: 'word_complexity',
        message: 'Words are too complex. Use simpler vocabulary where possible.',
        priority: 'medium'
      });
    }
    
    if (metrics.fleschReadingEase < 30) {
      recommendations.push({
        type: 'readability',
        message: 'Content is very difficult to read. Simplify sentence structure and vocabulary.',
        priority: 'high'
      });
    }
    
    if (metrics.sentenceVariety && metrics.sentenceVariety.varietyScore < 50) {
      recommendations.push({
        type: 'variety',
        message: 'Sentence structure lacks variety. Mix simple, compound, and complex sentences.',
        priority: 'medium'
      });
    }
    
    return recommendations;
  }

  // Correct grammar errors
  async correctGrammarErrors(content, grammarAnalysis) {
    let correctedContent = content;
    const corrections = [];
    
    // Fix common errors
    grammarAnalysis.errors.forEach(error => {
      if (error.correction) {
        correctedContent = correctedContent.replace(
          new RegExp(error.error, 'gi'),
          error.correction
        );
        corrections.push({
          type: error.type,
          original: error.error,
          correction: error.correction
        });
      }
    });
    
    // Fix article usage
    const articleErrors = grammarAnalysis.errors.filter(e => e.type === 'article_usage');
    articleErrors.forEach(error => {
      if (error.suggestion) {
        const pattern = new RegExp(`\\b${error.type === 'a' ? 'a' : 'an'}\\s+`, 'gi');
        correctedContent = correctedContent.replace(pattern, `${error.suggestion} `);
        corrections.push({
          type: 'article',
          original: error.type,
          correction: error.suggestion
        });
      }
    });
    
    // Fix subject-verb agreement
    const agreementErrors = grammarAnalysis.errors.filter(e => e.type === 'subject_verb_disagreement');
    agreementErrors.forEach(error => {
      if (error.suggestion) {
        corrections.push({
          type: 'subject_verb_agreement',
          message: error.message,
          suggestion: error.suggestion
        });
      }
    });
    
    return {
      content: correctedContent,
      corrections
    };
  }

  // Get singular verb form
  getSingularVerb(pluralVerb) {
    const conversions = {
      'are': 'is',
      'were': 'was',
      'have': 'has',
      'do': 'does'
    };
    return conversions[pluralVerb.toLowerCase()] || pluralVerb;
  }

  // Get plural verb form
  getPluralVerb(singularVerb) {
    const conversions = {
      'is': 'are',
      'was': 'were',
      'has': 'have',
      'does': 'do'
    };
    return conversions[singularVerb.toLowerCase()] || singularVerb;
  }

  // Optimize sentence structure
  optimizeSentenceStructure(content, readabilityAnalysis) {
    let optimizedContent = content;
    const improvements = [];
    
    const sentences = this.sentenceTokenizer.tokenize(content);
    const optimizedSentences = [];
    
    sentences.forEach(sentence => {
      const words = this.tokenizer.tokenize(sentence);
      
      // Break up long sentences
      if (words.length > 30) {
        const splitSentences = this.splitLongSentence(sentence);
        optimizedSentences.push(...splitSentences);
        improvements.push({
          type: 'split_sentence',
          original: sentence,
          result: splitSentences
        });
      }
      // Combine very short sentences
      else if (words.length < 5 && optimizedSentences.length > 0) {
        const lastSentence = optimizedSentences[optimizedSentences.length - 1];
        const combined = this.combineSentences(lastSentence, sentence);
        optimizedSentences[optimizedSentences.length - 1] = combined;
        improvements.push({
          type: 'combine_sentences',
          original: [lastSentence, sentence],
          result: combined
        });
      }
      // Fix passive voice
      else if (this.isPassiveVoice(sentence)) {
        const activeVoice = this.convertToActiveVoice(sentence);
        optimizedSentences.push(activeVoice);
        improvements.push({
          type: 'active_voice',
          original: sentence,
          result: activeVoice
        });
      }
      else {
        optimizedSentences.push(sentence);
      }
    });
    
    optimizedContent = optimizedSentences.join(' ');
    
    return {
      content: optimizedContent,
      improvements
    };
  }

  // Split long sentence
  splitLongSentence(sentence) {
    // Look for natural breaking points
    const conjunctions = ['and', 'but', 'however', 'therefore', 'moreover'];
    
    for (const conjunction of conjunctions) {
      const pattern = new RegExp(`\\s+${conjunction}\\s+`, 'i');
      if (pattern.test(sentence)) {
        const parts = sentence.split(pattern);
        if (parts.length === 2 && parts[0].length > 20 && parts[1].length > 20) {
          return [
            parts[0].trim() + '.',
            conjunction.charAt(0).toUpperCase() + conjunction.slice(1) + ' ' + parts[1].trim()
          ];
        }
      }
    }
    
    // If no natural break, return original
    return [sentence];
  }

  // Combine sentences
  combineSentences(sentence1, sentence2) {
    // Remove period from first sentence
    const s1 = sentence1.replace(/\.$/, '');
    const s2 = sentence2.charAt(0).toLowerCase() + sentence2.slice(1);
    
    // Choose appropriate connector
    const connectors = [', and', ', which', ' that'];
    const connector = connectors[Math.floor(Math.random() * connectors.length)];
    
    return s1 + connector + ' ' + s2;
  }

  // Check if sentence is passive voice
  isPassiveVoice(sentence) {
    return this.grammarRules.commonPatterns.passiveVoice.test(sentence);
  }

  // Convert to active voice
  convertToActiveVoice(sentence) {
    // This is a simplified conversion - in production, use more sophisticated NLP
    return sentence.replace(/\b(was|were|is|are|been|being)\s+(\w+ed)\b/g, (match, verb, participle) => {
      return participle.replace(/ed$/, 's');
    });
  }

  // Enhance vocabulary
  enhanceVocabulary(content, keyword, options) {
    let enhancedContent = content;
    const enhancements = [];
    
    // Replace overused words
    const overusedWords = this.findOverusedWords(content);
    overusedWords.forEach(word => {
      const replacements = this.getSynonyms(word.word);
      if (replacements.length > 0) {
        const replacement = replacements[0];
        // Replace some instances, not all
        let count = 0;
        enhancedContent = enhancedContent.replace(new RegExp(`\\b${word.word}\\b`, 'gi'), (match) => {
          count++;
          if (count % 2 === 0) {
            enhancements.push({
              type: 'synonym',
              original: match,
              replacement
            });
            return replacement;
          }
          return match;
        });
      }
    });
    
    // Enhance weak verbs
    const weakVerbs = ['is', 'are', 'was', 'were', 'has', 'have', 'had', 'get', 'got', 'make', 'made'];
    weakVerbs.forEach(verb => {
      const strongerVerb = this.getStrongerVerb(verb);
      if (strongerVerb) {
        enhancedContent = enhancedContent.replace(
          new RegExp(`\\b${verb}\\b`, 'gi'),
          strongerVerb
        );
        enhancements.push({
          type: 'strong_verb',
          original: verb,
          replacement: strongerVerb
        });
      }
    });
    
    // Add transition words
    enhancedContent = this.addTransitionWords(enhancedContent);
    
    return {
      content: enhancedContent,
      enhancements
    };
  }

  // Find overused words
  findOverusedWords(content) {
    const words = this.tokenizer.tokenize(content.toLowerCase());
    const wordFrequency = {};
    
    words.forEach(word => {
      if (word.length > 4) { // Only count substantial words
        wordFrequency[word] = (wordFrequency[word] || 0) + 1;
      }
    });
    
    // Find words used more than 3 times
    return Object.entries(wordFrequency)
      .filter(([word, count]) => count > 3)
      .map(([word, count]) => ({ word, count }))
      .sort((a, b) => b.count - a.count);
  }

  // Get synonyms (simplified)
  getSynonyms(word) {
    const synonyms = {
      'important': ['significant', 'crucial', 'essential', 'vital'],
      'good': ['excellent', 'beneficial', 'valuable', 'effective'],
      'bad': ['detrimental', 'harmful', 'negative', 'adverse'],
      'big': ['substantial', 'considerable', 'extensive', 'major'],
      'small': ['minor', 'limited', 'modest', 'minimal'],
      'many': ['numerous', 'various', 'multiple', 'several'],
      'help': ['assist', 'support', 'facilitate', 'enable']
    };
    
    return synonyms[word.toLowerCase()] || [];
  }

  // Get stronger verb
  getStrongerVerb(weakVerb) {
    const strongerVerbs = {
      'is': 'represents',
      'are': 'constitute',
      'was': 'became',
      'were': 'remained',
      'has': 'possesses',
      'have': 'maintain',
      'get': 'obtain',
      'got': 'acquired',
      'make': 'create',
      'made': 'produced'
    };
    
    return strongerVerbs[weakVerb.toLowerCase()] || null;
  }

  // Add transition words
  addTransitionWords(content) {
    const paragraphs = content.split(/\n\s*\n/);
    const transitions = [
      'Furthermore,', 'Additionally,', 'Moreover,', 'In addition,',
      'However,', 'Nevertheless,', 'On the other hand,',
      'Therefore,', 'Consequently,', 'As a result,',
      'For instance,', 'For example,', 'Specifically,'
    ];
    
    // Add transitions to some paragraphs
    for (let i = 1; i < paragraphs.length; i += 2) {
      if (!paragraphs[i].match(/^(Furthermore|Additionally|Moreover|However|Therefore)/)) {
        const transition = transitions[i % transitions.length];
        paragraphs[i] = transition + ' ' + paragraphs[i].charAt(0).toLowerCase() + paragraphs[i].slice(1);
      }
    }
    
    return paragraphs.join('\n\n');
  }

  // Improve content flow
  improveContentFlow(content) {
    let improvedContent = content;
    const improvements = [];
    
    // Ensure logical progression
    improvedContent = this.ensureLogicalProgression(improvedContent);
    
    // Add connecting phrases
    improvedContent = this.addConnectingPhrases(improvedContent);
    
    // Balance paragraph lengths
    improvedContent = this.balanceParagraphs(improvedContent);
    
    // Improve rhythm and cadence
    improvedContent = this.improveRhythmAndCadence(improvedContent);
    
    return {
      content: improvedContent,
      improvements
    };
  }

  // Ensure logical progression
  ensureLogicalProgression(content) {
    const paragraphs = content.split(/\n\s*\n/);
    const orderedParagraphs = [];
    
    // Identify paragraph types
    paragraphs.forEach(paragraph => {
      const type = this.identifyParagraphType(paragraph);
      orderedParagraphs.push({ content: paragraph, type });
    });
    
    // Reorder if necessary (simplified logic)
    orderedParagraphs.sort((a, b) => {
      const typeOrder = {
        'introduction': 1,
        'definition': 2,
        'explanation': 3,
        'example': 4,
        'conclusion': 5
      };
      return (typeOrder[a.type] || 3) - (typeOrder[b.type] || 3);
    });
    
    return orderedParagraphs.map(p => p.content).join('\n\n');
  }

  // Identify paragraph type
  identifyParagraphType(paragraph) {
    const lowerParagraph = paragraph.toLowerCase();
    
    if (lowerParagraph.includes('in conclusion') || lowerParagraph.includes('to summarize')) {
      return 'conclusion';
    }
    if (lowerParagraph.includes('for example') || lowerParagraph.includes('for instance')) {
      return 'example';
    }
    if (lowerParagraph.includes('is defined as') || lowerParagraph.includes('refers to')) {
      return 'definition';
    }
    if (lowerParagraph.match(/^(this|these|it|they)/)) {
      return 'explanation';
    }
    return 'introduction';
  }

  // Add connecting phrases
  addConnectingPhrases(content) {
    const sentences = this.sentenceTokenizer.tokenize(content);
    const connectedSentences = [];
    
    sentences.forEach((sentence, index) => {
      connectedSentences.push(sentence);
      
      // Add connecting phrase between related sentences
      if (index < sentences.length - 1 && Math.random() > 0.7) {
        const connector = this.getAppropriateConnector(sentence, sentences[index + 1]);
        if (connector) {
          sentences[index + 1] = connector + ' ' + sentences[index + 1].charAt(0).toLowerCase() + sentences[index + 1].slice(1);
        }
      }
    });
    
    return connectedSentences.join(' ');
  }

  // Get appropriate connector
  getAppropriateConnector(sentence1, sentence2) {
    // Analyze relationship between sentences
    if (sentence2.toLowerCase().includes('however') || sentence2.toLowerCase().includes('but')) {
      return null; // Already has connector
    }
    
    if (sentence1.includes('?')) {
      return 'To answer this,';
    }
    
    if (sentence2.toLowerCase().includes('example')) {
      return 'Specifically,';
    }
    
    // Random appropriate connector
    const connectors = ['Indeed,', 'In fact,', 'Notably,', 'Importantly,'];
    return connectors[Math.floor(Math.random() * connectors.length)];
  }

  // Balance paragraphs
  balanceParagraphs(content) {
    const paragraphs = content.split(/\n\s*\n/);
    const targetLength = 100; // Target words per paragraph
    const balancedParagraphs = [];
    
    paragraphs.forEach(paragraph => {
      const words = this.tokenizer.tokenize(paragraph);
      
      if (words.length > targetLength * 1.5) {
        // Split long paragraph
        const sentences = this.sentenceTokenizer.tokenize(paragraph);
        const midPoint = Math.floor(sentences.length / 2);
        balancedParagraphs.push(sentences.slice(0, midPoint).join(' '));
        balancedParagraphs.push(sentences.slice(midPoint).join(' '));
      } else if (words.length < targetLength * 0.5 && balancedParagraphs.length > 0) {
        // Merge with previous paragraph
        balancedParagraphs[balancedParagraphs.length - 1] += ' ' + paragraph;
      } else {
        balancedParagraphs.push(paragraph);
      }
    });
    
    return balancedParagraphs.join('\n\n');
  }

  // Improve rhythm and cadence
  improveRhythmAndCadence(content) {
    const sentences = this.sentenceTokenizer.tokenize(content);
    const improvedSentences = [];
    
    sentences.forEach((sentence, index) => {
      const words = this.tokenizer.tokenize(sentence);
      const prevLength = index > 0 ? this.tokenizer.tokenize(sentences[index - 1]).length : 0;
      
      // Vary sentence length for better rhythm
      if (prevLength > 20 && words.length > 20) {
        // Insert a short sentence for variety
        improvedSentences.push(sentence);
        if (index < sentences.length - 1) {
          improvedSentences.push('This is important.');
        }
      } else {
        improvedSentences.push(sentence);
      }
    });
    
    return improvedSentences.join(' ');
  }

  // Fine-tune readability
  finetuneReadability(content, targetAudience = 'general') {
    let tunedContent = content;
    const targets = this.readabilityTargets[targetAudience] || this.readabilityTargets.general;
    
    // Analyze current readability
    const currentAnalysis = this.analyzeReadability(tunedContent);
    
    // Adjust sentence length
    if (currentAnalysis.metrics.avgSentenceLength > targets.avgSentenceLength.max) {
      tunedContent = this.shortenSentences(tunedContent, targets.avgSentenceLength.max);
    } else if (currentAnalysis.metrics.avgSentenceLength < targets.avgSentenceLength.min) {
      tunedContent = this.lengthenSentences(tunedContent, targets.avgSentenceLength.min);
    }
    
    // Adjust vocabulary complexity
    if (currentAnalysis.metrics.avgWordLength > targets.avgWordLength.max) {
      tunedContent = this.simplifyVocabulary(tunedContent);
    }
    
    // Ensure proper formatting
    tunedContent = this.ensureProperFormatting(tunedContent);
    
    return {
      content: tunedContent,
      targetAudience,
      adjustments: {
        sentenceLength: currentAnalysis.metrics.avgSentenceLength !== targets.avgSentenceLength.max,
        vocabulary: currentAnalysis.metrics.avgWordLength !== targets.avgWordLength.max
      }
    };
  }

  // Shorten sentences
  shortenSentences(content, targetLength) {
    const sentences = this.sentenceTokenizer.tokenize(content);
    const shortened = [];
    
    sentences.forEach(sentence => {
      const words = this.tokenizer.tokenize(sentence);
      if (words.length > targetLength) {
        const split = this.splitLongSentence(sentence);
        shortened.push(...split);
      } else {
        shortened.push(sentence);
      }
    });
    
    return shortened.join(' ');
  }

  // Lengthen sentences
  lengthenSentences(content, targetLength) {
    const sentences = this.sentenceTokenizer.tokenize(content);
    const lengthened = [];
    
    for (let i = 0; i < sentences.length; i++) {
      const words = this.tokenizer.tokenize(sentences[i]);
      if (words.length < targetLength && i < sentences.length - 1) {
        const nextWords = this.tokenizer.tokenize(sentences[i + 1]);
        if (nextWords.length < targetLength) {
          // Combine sentences
          lengthened.push(this.combineSentences(sentences[i], sentences[i + 1]));
          i++; // Skip next sentence
        } else {
          lengthened.push(sentences[i]);
        }
      } else {
        lengthened.push(sentences[i]);
      }
    }
    
    return lengthened.join(' ');
  }

  // Simplify vocabulary
  simplifyVocabulary(content) {
    const complexWords = {
      'utilize': 'use',
      'implement': 'apply',
      'demonstrate': 'show',
      'facilitate': 'help',
      'terminate': 'end',
      'commence': 'start',
      'endeavor': 'try',
      'ascertain': 'find out',
      'substantiate': 'prove',
      'elucidate': 'explain'
    };
    
    let simplified = content;
    Object.entries(complexWords).forEach(([complex, simple]) => {
      simplified = simplified.replace(new RegExp(`\\b${complex}\\b`, 'gi'), simple);
    });
    
    return simplified;
  }

  // Ensure proper formatting
  ensureProperFormatting(content) {
    let formatted = content;
    
    // Fix spacing issues
    formatted = formatted.replace(/\s+/g, ' '); // Multiple spaces to single
    formatted = formatted.replace(/\s+([.,!?;:])/g, '$1'); // Remove space before punctuation
    formatted = formatted.replace(/([.,!?;:])\s*/g, '$1 '); // Ensure space after punctuation
    formatted = formatted.replace(/\s+$/gm, ''); // Remove trailing spaces
    
    // Fix paragraph spacing
    formatted = formatted.replace(/\n{3,}/g, '\n\n'); // Multiple newlines to double
    
    // Ensure sentences end with proper punctuation
    const sentences = formatted.split(/(?<=[.!?])\s+/);
    formatted = sentences.map(sentence => {
      if (sentence.trim() && !sentence.trim().match(/[.!?]$/)) {
        return sentence.trim() + '.';
      }
      return sentence;
    }).join(' ');
    
    // Capitalize first letter of sentences
    formatted = formatted.replace(/(^|[.!?]\s+)([a-z])/g, (match, p1, p2) => p1 + p2.toUpperCase());
    
    return formatted.trim();
  }

  // Perform final validation
  async performFinalValidation(content) {
    // Re-analyze grammar
    const grammarAnalysis = await this.analyzeGrammar(content);
    
    // Re-analyze readability
    const readabilityAnalysis = this.analyzeReadability(content);
    
    // Check for remaining issues
    const remainingIssues = [];
    
    if (grammarAnalysis.errors.length > 0) {
      remainingIssues.push({
        type: 'grammar',
        count: grammarAnalysis.errors.length,
        severity: 'medium'
      });
    }
    
    if (readabilityAnalysis.overallScore < 60) {
      remainingIssues.push({
        type: 'readability',
        score: readabilityAnalysis.overallScore,
        severity: 'low'
      });
    }
    
    return {
      grammarScore: grammarAnalysis.overallScore,
      readabilityScore: readabilityAnalysis.overallScore,
      remainingIssues,
      metrics: {
        fleschReadingEase: readabilityAnalysis.scores.fleschReadingEase,
        gradeLevel: readabilityAnalysis.scores.fleschKincaidGrade,
        avgSentenceLength: readabilityAnalysis.metrics.avgSentenceLength,
        avgWordLength: readabilityAnalysis.metrics.avgWordLength
      },
      passed: grammarAnalysis.overallScore >= 90 && readabilityAnalysis.overallScore >= 70
    };
  }

  // Check if sentence has subject
  hasSubject(taggedWords) {
    return taggedWords.some(word => 
      word.pos === 'NN' || word.pos === 'NNS' || 
      word.pos === 'NNP' || word.pos === 'NNPS' ||
      word.pos === 'PRP'
    );
  }

  // Check if sentence has verb
  hasVerb(taggedWords) {
    return taggedWords.some(word => word.pos.startsWith('VB'));
  }

  // Calculate sentence complexity
  calculateSentenceComplexity(taggedWords) {
    const factors = {
      length: taggedWords.length,
      clauseCount: taggedWords.filter(w => w.pos === 'CC' || w.pos === 'IN').length,
      nounPhrases: taggedWords.filter(w => w.pos.startsWith('NN')).length,
      verbPhrases: taggedWords.filter(w => w.pos.startsWith('VB')).length
    };
    
    return (factors.length * 0.3) + 
           (factors.clauseCount * 2) + 
           (factors.nounPhrases * 0.5) + 
           (factors.verbPhrases * 0.5);
  }
}

module.exports = GrammarReadabilityOptimizer;