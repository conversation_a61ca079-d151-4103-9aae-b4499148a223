const natural = require('natural');
const axios = require('axios');

class CompetitorBenchmarkMatcher {
  constructor() {
    this.tokenizer = new natural.WordTokenizer();
    this.stemmer = natural.PorterStemmer;
    this.serperApiKey = process.env.SERPER_API_KEY;
    
    // Initialize matching algorithms and tolerances
    this.matchingAlgorithms = this.initializeMatchingAlgorithms();
    this.precisionTolerances = this.initializePrecisionTolerances();
    this.benchmarkWeights = this.initializeBenchmarkWeights();
  }

  // Main method for comprehensive competitor benchmark matching
  async matchCompetitorBenchmarks(content, keyword, competitorBenchmarks, options = {}) {
    try {
      console.log(`Starting competitor benchmark matching for keyword: ${keyword}`);

      // Analyze current content metrics
      const currentMetrics = await this.analyzeCurrentContentMetrics(content, keyword);
      
      // Calculate required adjustments to match benchmarks
      const requiredAdjustments = this.calculateRequiredAdjustments(currentMetrics, competitorBenchmarks);
      
      // Apply surgical adjustments to match benchmarks precisely
      const adjustedContent = await this.applySurgicalAdjustments(content, keyword, requiredAdjustments, options);
      
      // Validate benchmark compliance
      const complianceValidation = this.validateBenchmarkCompliance(adjustedContent.content, keyword, competitorBenchmarks);
      
      // Fine-tune for perfect precision if needed
      const finalContent = await this.fineTuneForPerfectPrecision(
        adjustedContent.content, 
        keyword, 
        competitorBenchmarks, 
        complianceValidation
      );
      
      // Generate comprehensive matching report
      const matchingReport = this.generateMatchingReport({
        originalMetrics: currentMetrics,
        targetBenchmarks: competitorBenchmarks,
        finalMetrics: await this.analyzeCurrentContentMetrics(finalContent.content, keyword),
        adjustments: requiredAdjustments,
        compliance: complianceValidation
      });

      return {
        success: true,
        content: finalContent.content,
        matching: {
          originalMetrics: currentMetrics,
          targetBenchmarks: competitorBenchmarks,
          finalMetrics: await this.analyzeCurrentContentMetrics(finalContent.content, keyword),
          adjustmentsMade: adjustedContent.adjustments,
          complianceValidation,
          matchingReport
        },
        metadata: {
          matchingAccuracy: complianceValidation.overallAccuracy,
          benchmarksMatched: complianceValidation.benchmarksMatched,
          totalBenchmarks: complianceValidation.totalBenchmarks,
          perfectMatch: complianceValidation.perfectMatch,
          adjustmentsApplied: adjustedContent.adjustments.length,
          processedAt: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('Competitor benchmark matching error:', error);
      return {
        success: false,
        error: error.message,
        content: content
      };
    }
  }

  // Initialize matching algorithms
  initializeMatchingAlgorithms() {
    return {
      wordCount: {
        algorithm: 'surgical_adjustment',
        precision: 'exact', // Must match exactly
        tolerance: 0, // Zero tolerance
        method: 'iterative_refinement'
      },
      keywordDensity: {
        algorithm: 'density_optimization',
        precision: 'surgical', // 0.1% precision
        tolerance: 0.1,
        method: 'smart_insertion_removal'
      },
      headingStructure: {
        algorithm: 'structure_matching',
        precision: 'exact', // Exact heading count match
        tolerance: 0,
        method: 'intelligent_restructuring'
      },
      contentLength: {
        algorithm: 'length_optimization',
        precision: 'character_level',
        tolerance: 50, // 50 character tolerance
        method: 'semantic_expansion_contraction'
      },
      readabilityMetrics: {
        algorithm: 'readability_matching',
        precision: 'score_based',
        tolerance: 2.0, // 2 point tolerance
        method: 'sentence_complexity_adjustment'
      }
    };
  }

  // Initialize precision tolerances
  initializePrecisionTolerances() {
    return {
      wordCount: {
        exact: 0, // Zero deviation allowed
        near: 5, // 5 words maximum
        acceptable: 10 // 10 words maximum
      },
      keywordDensity: {
        exact: 0.05, // 0.05% maximum deviation
        near: 0.1, // 0.1% maximum deviation
        acceptable: 0.2 // 0.2% maximum deviation
      },
      headingCounts: {
        h1: 0, // Must be exact
        h2: 1, // 1 heading tolerance
        h3: 2, // 2 headings tolerance
        total: 2 // Total heading tolerance
      },
      readabilityScores: {
        fleschReadingEase: 5.0, // 5 point tolerance
        gradeLevel: 1.0, // 1 grade level tolerance
        sentenceLength: 2.0 // 2 word average tolerance
      }
    };
  }

  // Initialize benchmark weights
  initializeBenchmarkWeights() {
    return {
      wordCount: 35, // 35% weight - most critical
      keywordDensity: 30, // 30% weight - very critical
      headingStructure: 20, // 20% weight - important
      readabilityMetrics: 10, // 10% weight - secondary
      contentQuality: 5 // 5% weight - tertiary
    };
  }

  // Analyze current content metrics
  async analyzeCurrentContentMetrics(content, keyword) {
    const words = this.tokenizer.tokenize(content) || [];
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    // Calculate keyword metrics
    const keywordDensity = this.calculatePreciseKeywordDensity(content, keyword);
    const componentKeywords = keyword.split(' ');
    const componentDensities = componentKeywords.map(component => ({
      keyword: component,
      density: this.calculatePreciseKeywordDensity(content, component)
    }));

    // Analyze heading structure
    const headingStructure = this.analyzeDetailedHeadingStructure(content, keyword);
    
    // Calculate readability metrics
    const readabilityMetrics = this.calculatePreciseReadabilityMetrics(content);
    
    // Analyze content quality indicators
    const qualityIndicators = this.analyzeContentQualityIndicators(content, keyword);

    return {
      wordCount: words.length,
      characterCount: content.length,
      sentenceCount: sentences.length,
      paragraphCount: content.split(/\n\s*\n/).filter(p => p.trim().length > 0).length,
      keywordMetrics: {
        mainKeyword: {
          keyword,
          density: keywordDensity,
          occurrences: this.countKeywordOccurrences(content, keyword)
        },
        componentKeywords: componentDensities
      },
      headingStructure,
      readabilityMetrics,
      qualityIndicators
    };
  }

  // Calculate required adjustments to match benchmarks
  calculateRequiredAdjustments(currentMetrics, benchmarks) {
    const adjustments = [];

    // Word count adjustments
    if (benchmarks.wordCount && benchmarks.wordCount.target) {
      const wordCountDifference = benchmarks.wordCount.target - currentMetrics.wordCount;
      if (Math.abs(wordCountDifference) > this.precisionTolerances.wordCount.exact) {
        adjustments.push({
          type: 'wordCount',
          current: currentMetrics.wordCount,
          target: benchmarks.wordCount.target,
          difference: wordCountDifference,
          action: wordCountDifference > 0 ? 'expand' : 'contract',
          priority: 'critical',
          method: wordCountDifference > 0 ? 'semantic_expansion' : 'intelligent_reduction'
        });
      }
    }

    // Keyword density adjustments
    if (benchmarks.keywordDensity && benchmarks.keywordDensity.main) {
      const densityDifference = benchmarks.keywordDensity.main - currentMetrics.keywordMetrics.mainKeyword.density;
      if (Math.abs(densityDifference) > this.precisionTolerances.keywordDensity.exact) {
        adjustments.push({
          type: 'keywordDensity',
          current: currentMetrics.keywordMetrics.mainKeyword.density,
          target: benchmarks.keywordDensity.main,
          difference: densityDifference,
          action: densityDifference > 0 ? 'increase' : 'decrease',
          priority: 'critical',
          method: densityDifference > 0 ? 'strategic_insertion' : 'smart_removal'
        });
      }
    }

    // Heading structure adjustments
    if (benchmarks.headingStructure) {
      const h2Difference = (benchmarks.headingStructure.h2Count || 0) - currentMetrics.headingStructure.h2Count;
      const h3Difference = (benchmarks.headingStructure.h3Count || 0) - currentMetrics.headingStructure.h3Count;
      
      if (Math.abs(h2Difference) > this.precisionTolerances.headingCounts.h2) {
        adjustments.push({
          type: 'headingStructure',
          subtype: 'h2',
          current: currentMetrics.headingStructure.h2Count,
          target: benchmarks.headingStructure.h2Count,
          difference: h2Difference,
          action: h2Difference > 0 ? 'add' : 'remove',
          priority: 'high',
          method: h2Difference > 0 ? 'intelligent_heading_creation' : 'smart_heading_consolidation'
        });
      }
      
      if (Math.abs(h3Difference) > this.precisionTolerances.headingCounts.h3) {
        adjustments.push({
          type: 'headingStructure',
          subtype: 'h3',
          current: currentMetrics.headingStructure.h3Count,
          target: benchmarks.headingStructure.h3Count,
          difference: h3Difference,
          action: h3Difference > 0 ? 'add' : 'remove',
          priority: 'high',
          method: h3Difference > 0 ? 'intelligent_subheading_creation' : 'smart_subheading_consolidation'
        });
      }
    }

    // Component keyword density adjustments
    if (benchmarks.keywordDensity && benchmarks.keywordDensity.components) {
      currentMetrics.keywordMetrics.componentKeywords.forEach((component, index) => {
        const targetDensity = benchmarks.keywordDensity.components[component.keyword];
        if (targetDensity) {
          const componentDifference = targetDensity - component.density;
          if (Math.abs(componentDifference) > this.precisionTolerances.keywordDensity.near) {
            adjustments.push({
              type: 'componentKeywordDensity',
              keyword: component.keyword,
              current: component.density,
              target: targetDensity,
              difference: componentDifference,
              action: componentDifference > 0 ? 'increase' : 'decrease',
              priority: 'medium',
              method: componentDifference > 0 ? 'contextual_insertion' : 'selective_removal'
            });
          }
        }
      });
    }

    // Sort adjustments by priority
    return adjustments.sort((a, b) => {
      const priorityOrder = { critical: 3, high: 2, medium: 1, low: 0 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  // Apply surgical adjustments to match benchmarks precisely
  async applySurgicalAdjustments(content, keyword, adjustments, options) {
    let adjustedContent = content;
    const appliedAdjustments = [];

    for (const adjustment of adjustments) {
      try {
        console.log(`Applying ${adjustment.type} adjustment: ${adjustment.action} by ${Math.abs(adjustment.difference)}`);
        
        switch (adjustment.type) {
          case 'wordCount':
            const wordCountResult = await this.adjustWordCount(adjustedContent, keyword, adjustment, options);
            adjustedContent = wordCountResult.content;
            appliedAdjustments.push({
              ...adjustment,
              applied: true,
              result: wordCountResult.result
            });
            break;
            
          case 'keywordDensity':
            const densityResult = await this.adjustKeywordDensity(adjustedContent, keyword, adjustment, options);
            adjustedContent = densityResult.content;
            appliedAdjustments.push({
              ...adjustment,
              applied: true,
              result: densityResult.result
            });
            break;
            
          case 'headingStructure':
            const headingResult = await this.adjustHeadingStructure(adjustedContent, keyword, adjustment, options);
            adjustedContent = headingResult.content;
            appliedAdjustments.push({
              ...adjustment,
              applied: true,
              result: headingResult.result
            });
            break;
            
          case 'componentKeywordDensity':
            const componentResult = await this.adjustComponentKeywordDensity(adjustedContent, adjustment.keyword, adjustment, options);
            adjustedContent = componentResult.content;
            appliedAdjustments.push({
              ...adjustment,
              applied: true,
              result: componentResult.result
            });
            break;
            
          default:
            console.warn(`Unknown adjustment type: ${adjustment.type}`);
            appliedAdjustments.push({
              ...adjustment,
              applied: false,
              error: 'Unknown adjustment type'
            });
        }
      } catch (error) {
        console.error(`Failed to apply ${adjustment.type} adjustment:`, error);
        appliedAdjustments.push({
          ...adjustment,
          applied: false,
          error: error.message
        });
      }
    }

    return {
      content: adjustedContent,
      adjustments: appliedAdjustments
    };
  }

  // Adjust word count to match benchmark
  async adjustWordCount(content, keyword, adjustment, options) {
    const currentWords = this.tokenizer.tokenize(content) || [];
    const targetWordCount = adjustment.target;
    const difference = adjustment.difference;

    if (difference > 0) {
      // Expand content
      return this.expandContentIntelligently(content, keyword, difference, options);
    } else {
      // Contract content
      return this.contractContentIntelligently(content, keyword, Math.abs(difference), options);
    }
  }

  // Intelligently expand content
  async expandContentIntelligently(content, keyword, wordsToAdd, options) {
    // Strategy: Add semantically relevant content that enhances value
    const expansionStrategies = [
      { method: 'detailed_examples', weight: 30 },
      { method: 'expert_insights', weight: 25 },
      { method: 'additional_context', weight: 20 },
      { method: 'supporting_evidence', weight: 15 },
      { method: 'practical_tips', weight: 10 }
    ];

    let expandedContent = content;
    let wordsAdded = 0;
    const targetWords = wordsToAdd;

    // Add content strategically to reach target word count
    for (const strategy of expansionStrategies) {
      if (wordsAdded >= targetWords) break;
      
      const wordsForThisStrategy = Math.ceil((targetWords - wordsAdded) * (strategy.weight / 100));
      const expansionResult = await this.applyExpansionStrategy(
        expandedContent, 
        keyword, 
        strategy.method, 
        wordsForThisStrategy
      );
      
      expandedContent = expansionResult.content;
      wordsAdded += expansionResult.wordsAdded;
    }

    return {
      content: expandedContent,
      result: {
        wordsAdded,
        targetWords,
        accuracy: (wordsAdded / targetWords) * 100,
        method: 'intelligent_expansion'
      }
    };
  }

  // Apply specific expansion strategy
  async applyExpansionStrategy(content, keyword, method, targetWords) {
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    let expandedContent = content;
    let wordsAdded = 0;

    switch (method) {
      case 'detailed_examples':
        // Add detailed examples throughout the content
        for (let i = 0; i < paragraphs.length && wordsAdded < targetWords; i++) {
          if (paragraphs[i].toLowerCase().includes(keyword.toLowerCase())) {
            const exampleText = this.generateRelevantExample(keyword, targetWords - wordsAdded);
            expandedContent = this.insertTextAfterParagraph(expandedContent, i, exampleText);
            wordsAdded += (this.tokenizer.tokenize(exampleText) || []).length;
          }
        }
        break;
        
      case 'expert_insights':
        // Add expert perspectives and insights
        const insightText = this.generateExpertInsight(keyword, targetWords);
        expandedContent = this.insertTextInMiddle(expandedContent, insightText);
        wordsAdded += (this.tokenizer.tokenize(insightText) || []).length;
        break;
        
      case 'additional_context':
        // Add contextual background information
        const contextText = this.generateAdditionalContext(keyword, targetWords);
        expandedContent = this.insertTextAtBeginning(expandedContent, contextText);
        wordsAdded += (this.tokenizer.tokenize(contextText) || []).length;
        break;
        
      case 'supporting_evidence':
        // Add supporting evidence and data
        const evidenceText = this.generateSupportingEvidence(keyword, targetWords);
        expandedContent = this.insertTextBeforeConclusion(expandedContent, evidenceText);
        wordsAdded += (this.tokenizer.tokenize(evidenceText) || []).length;
        break;
        
      case 'practical_tips':
        // Add practical tips and actionable advice
        const tipsText = this.generatePracticalTips(keyword, targetWords);
        expandedContent = this.insertTextAtEnd(expandedContent, tipsText);
        wordsAdded += (this.tokenizer.tokenize(tipsText) || []).length;
        break;
    }

    return {
      content: expandedContent,
      wordsAdded
    };
  }

  // Intelligently contract content
  async contractContentIntelligently(content, keyword, wordsToRemove, options) {
    // Strategy: Remove redundant content while preserving value and keyword optimization
    let contractedContent = content;
    let wordsRemoved = 0;
    const targetWords = wordsToRemove;

    // Removal strategies in order of preference
    const removalStrategies = [
      { method: 'remove_redundancy', weight: 40 },
      { method: 'condense_explanations', weight: 30 },
      { method: 'eliminate_filler', weight: 20 },
      { method: 'merge_similar_sections', weight: 10 }
    ];

    for (const strategy of removalStrategies) {
      if (wordsRemoved >= targetWords) break;
      
      const wordsForThisStrategy = Math.ceil((targetWords - wordsRemoved) * (strategy.weight / 100));
      const contractionResult = await this.applyContractionStrategy(
        contractedContent, 
        keyword, 
        strategy.method, 
        wordsForThisStrategy
      );
      
      contractedContent = contractionResult.content;
      wordsRemoved += contractionResult.wordsRemoved;
    }

    return {
      content: contractedContent,
      result: {
        wordsRemoved,
        targetWords,
        accuracy: (wordsRemoved / targetWords) * 100,
        method: 'intelligent_contraction'
      }
    };
  }

  // Apply specific contraction strategy
  async applyContractionStrategy(content, keyword, method, targetWords) {
    let contractedContent = content;
    let wordsRemoved = 0;

    switch (method) {
      case 'remove_redundancy':
        // Remove redundant sentences and phrases
        const redundancyResult = this.removeRedundantContent(content, keyword, targetWords);
        contractedContent = redundancyResult.content;
        wordsRemoved = redundancyResult.wordsRemoved;
        break;
        
      case 'condense_explanations':
        // Condense verbose explanations
        const condensationResult = this.condenseVerboseExplanations(content, targetWords);
        contractedContent = condensationResult.content;
        wordsRemoved = condensationResult.wordsRemoved;
        break;
        
      case 'eliminate_filler':
        // Remove filler words and unnecessary phrases
        const fillerResult = this.eliminateFillerContent(content, targetWords);
        contractedContent = fillerResult.content;
        wordsRemoved = fillerResult.wordsRemoved;
        break;
        
      case 'merge_similar_sections':
        // Merge similar sections to reduce word count
        const mergingResult = this.mergeSimilarSections(content, targetWords);
        contractedContent = mergingResult.content;
        wordsRemoved = mergingResult.wordsRemoved;
        break;
    }

    return {
      content: contractedContent,
      wordsRemoved
    };
  }

  // Adjust keyword density to match benchmark
  async adjustKeywordDensity(content, keyword, adjustment, options) {
    const currentDensity = adjustment.current;
    const targetDensity = adjustment.target;
    const difference = adjustment.difference;

    if (difference > 0) {
      // Increase keyword density
      return this.increaseKeywordDensity(content, keyword, difference, options);
    } else {
      // Decrease keyword density
      return this.decreaseKeywordDensity(content, keyword, Math.abs(difference), options);
    }
  }

  // Increase keyword density strategically
  async increaseKeywordDensity(content, keyword, densityIncrease, options) {
    const words = this.tokenizer.tokenize(content) || [];
    const currentOccurrences = this.countKeywordOccurrences(content, keyword);
    const targetOccurrences = Math.ceil((words.length * (this.calculatePreciseKeywordDensity(content, keyword) + densityIncrease)) / 100);
    const occurrencesToAdd = targetOccurrences - currentOccurrences;

    let optimizedContent = content;
    let addedOccurrences = 0;

    // Strategic insertion points
    const insertionStrategies = [
      { location: 'paragraph_beginnings', weight: 30 },
      { location: 'section_transitions', weight: 25 },
      { location: 'conclusion_reinforcement', weight: 20 },
      { location: 'contextual_variations', weight: 15 },
      { location: 'natural_flow_points', weight: 10 }
    ];

    for (const strategy of insertionStrategies) {
      if (addedOccurrences >= occurrencesToAdd) break;
      
      const insertionsForStrategy = Math.ceil((occurrencesToAdd - addedOccurrences) * (strategy.weight / 100));
      const insertionResult = await this.applyKeywordInsertionStrategy(
        optimizedContent, 
        keyword, 
        strategy.location, 
        insertionsForStrategy
      );
      
      optimizedContent = insertionResult.content;
      addedOccurrences += insertionResult.insertions;
    }

    return {
      content: optimizedContent,
      result: {
        occurrencesAdded: addedOccurrences,
        targetOccurrences: occurrencesToAdd,
        finalDensity: this.calculatePreciseKeywordDensity(optimizedContent, keyword),
        method: 'strategic_insertion'
      }
    };
  }

  // Decrease keyword density strategically
  async decreaseKeywordDensity(content, keyword, densityDecrease, options) {
    const words = this.tokenizer.tokenize(content) || [];
    const currentOccurrences = this.countKeywordOccurrences(content, keyword);
    const targetOccurrences = Math.floor((words.length * (this.calculatePreciseKeywordDensity(content, keyword) - densityDecrease)) / 100);
    const occurrencesToRemove = currentOccurrences - targetOccurrences;

    let optimizedContent = content;
    let removedOccurrences = 0;

    // Strategic removal prioritizing non-critical instances
    const removalStrategies = [
      { priority: 'redundant_mentions', weight: 40 },
      { priority: 'excessive_clustering', weight: 30 },
      { priority: 'non_strategic_positions', weight: 20 },
      { priority: 'less_impactful_contexts', weight: 10 }
    ];

    for (const strategy of removalStrategies) {
      if (removedOccurrences >= occurrencesToRemove) break;
      
      const removalsForStrategy = Math.ceil((occurrencesToRemove - removedOccurrences) * (strategy.weight / 100));
      const removalResult = await this.applyKeywordRemovalStrategy(
        optimizedContent, 
        keyword, 
        strategy.priority, 
        removalsForStrategy
      );
      
      optimizedContent = removalResult.content;
      removedOccurrences += removalResult.removals;
    }

    return {
      content: optimizedContent,
      result: {
        occurrencesRemoved: removedOccurrences,
        targetOccurrences: occurrencesToRemove,
        finalDensity: this.calculatePreciseKeywordDensity(optimizedContent, keyword),
        method: 'strategic_removal'
      }
    };
  }

  // Validate benchmark compliance
  validateBenchmarkCompliance(content, keyword, benchmarks) {
    const currentMetrics = {
      wordCount: (this.tokenizer.tokenize(content) || []).length,
      keywordDensity: this.calculatePreciseKeywordDensity(content, keyword),
      headingStructure: this.analyzeDetailedHeadingStructure(content, keyword)
    };

    const compliance = {
      wordCount: this.checkWordCountCompliance(currentMetrics.wordCount, benchmarks.wordCount),
      keywordDensity: this.checkKeywordDensityCompliance(currentMetrics.keywordDensity, benchmarks.keywordDensity),
      headingStructure: this.checkHeadingStructureCompliance(currentMetrics.headingStructure, benchmarks.headingStructure)
    };

    // Calculate overall compliance
    const complianceScores = Object.values(compliance).map(c => c.score);
    const overallAccuracy = complianceScores.reduce((sum, score) => sum + score, 0) / complianceScores.length;
    const benchmarksMatched = Object.values(compliance).filter(c => c.compliant).length;
    const totalBenchmarks = Object.keys(compliance).length;
    const perfectMatch = benchmarksMatched === totalBenchmarks && overallAccuracy >= 98;

    return {
      wordCount: compliance.wordCount,
      keywordDensity: compliance.keywordDensity,
      headingStructure: compliance.headingStructure,
      overallAccuracy,
      benchmarksMatched,
      totalBenchmarks,
      perfectMatch,
      complianceGrade: this.getComplianceGrade(overallAccuracy)
    };
  }

  // Fine-tune for perfect precision
  async fineTuneForPerfectPrecision(content, keyword, benchmarks, compliance) {
    if (compliance.perfectMatch) {
      return { content, refinements: [] };
    }

    let refinedContent = content;
    const refinements = [];

    // Apply micro-adjustments for perfect precision
    if (!compliance.wordCount.compliant) {
      const wordCountRefinement = await this.microAdjustWordCount(refinedContent, keyword, compliance.wordCount);
      refinedContent = wordCountRefinement.content;
      refinements.push(wordCountRefinement.refinement);
    }

    if (!compliance.keywordDensity.compliant) {
      const densityRefinement = await this.microAdjustKeywordDensity(refinedContent, keyword, compliance.keywordDensity);
      refinedContent = densityRefinement.content;
      refinements.push(densityRefinement.refinement);
    }

    if (!compliance.headingStructure.compliant) {
      const headingRefinement = await this.microAdjustHeadingStructure(refinedContent, keyword, compliance.headingStructure);
      refinedContent = headingRefinement.content;
      refinements.push(headingRefinement.refinement);
    }

    return {
      content: refinedContent,
      refinements
    };
  }

  // Helper methods for calculations and analysis
  calculatePreciseKeywordDensity(content, keyword) {
    const words = this.tokenizer.tokenize(content.toLowerCase()) || [];
    const keywordOccurrences = this.countKeywordOccurrences(content, keyword);
    return words.length > 0 ? (keywordOccurrences / words.length) * 100 : 0;
  }

  countKeywordOccurrences(content, keyword) {
    const keywordRegex = new RegExp(`\\b${keyword.replace(/\s+/g, '\\s+')}\\b`, 'gi');
    const matches = content.match(keywordRegex);
    return matches ? matches.length : 0;
  }

  analyzeDetailedHeadingStructure(content, keyword) {
    const h1Matches = content.match(/<h1[^>]*>(.*?)<\/h1>/gi) || content.match(/^# (.+)$/gm) || [];
    const h2Matches = content.match(/<h2[^>]*>(.*?)<\/h2>/gi) || content.match(/^## (.+)$/gm) || [];
    const h3Matches = content.match(/<h3[^>]*>(.*?)<\/h3>/gi) || content.match(/^### (.+)$/gm) || [];
    const h4Matches = content.match(/<h4[^>]*>(.*?)<\/h4>/gi) || content.match(/^#### (.+)$/gm) || [];

    return {
      h1Count: h1Matches.length,
      h2Count: h2Matches.length,
      h3Count: h3Matches.length,
      h4Count: h4Matches.length,
      totalHeadings: h1Matches.length + h2Matches.length + h3Matches.length + h4Matches.length,
      keywordOptimizedHeadings: this.countKeywordOptimizedHeadings([...h1Matches, ...h2Matches, ...h3Matches, ...h4Matches], keyword)
    };
  }

  countKeywordOptimizedHeadings(headings, keyword) {
    return headings.filter(heading => heading.toLowerCase().includes(keyword.toLowerCase())).length;
  }

  calculatePreciseReadabilityMetrics(content) {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = this.tokenizer.tokenize(content) || [];
    const syllables = this.countTotalSyllables(content);

    const avgWordsPerSentence = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;

    const fleschReadingEase = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord);
    const fleschKincaidGrade = 0.39 * avgWordsPerSentence + 11.8 * avgSyllablesPerWord - 15.59;

    return {
      fleschReadingEase: Math.round(fleschReadingEase * 100) / 100,
      fleschKincaidGrade: Math.round(fleschKincaidGrade * 100) / 100,
      avgWordsPerSentence: Math.round(avgWordsPerSentence * 100) / 100,
      avgSyllablesPerWord: Math.round(avgSyllablesPerWord * 100) / 100
    };
  }

  countTotalSyllables(text) {
    const words = this.tokenizer.tokenize(text.toLowerCase()) || [];
    return words.reduce((total, word) => total + this.countSyllablesInWord(word), 0);
  }

  countSyllablesInWord(word) {
    const vowels = 'aeiouy';
    let syllables = 0;
    let previousWasVowel = false;

    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i]);
      if (isVowel && !previousWasVowel) {
        syllables++;
      }
      previousWasVowel = isVowel;
    }

    // Handle silent e
    if (word.endsWith('e') && syllables > 1) {
      syllables--;
    }

    return Math.max(1, syllables);
  }

  analyzeContentQualityIndicators(content, keyword) {
    return {
      averageParagraphLength: this.calculateAverageParagraphLength(content),
      sentenceVariety: this.analyzeSentenceVariety(content),
      vocabularyRichness: this.analyzeVocabularyRichness(content),
      keywordDistribution: this.analyzeKeywordDistribution(content, keyword)
    };
  }

  // Additional helper methods would be implemented here...
  // (Simplified for brevity, but would include all matching and adjustment logic)

  checkWordCountCompliance(current, benchmark) {
    if (!benchmark || !benchmark.target) {
      return { compliant: true, score: 100, deviation: 0 };
    }

    const deviation = Math.abs(current - benchmark.target);
    const accuracy = Math.max(0, 100 - (deviation / benchmark.target) * 100);
    const compliant = deviation <= this.precisionTolerances.wordCount.exact;

    return {
      compliant,
      score: accuracy,
      current,
      target: benchmark.target,
      deviation,
      tolerance: this.precisionTolerances.wordCount.exact
    };
  }

  checkKeywordDensityCompliance(current, benchmark) {
    if (!benchmark || !benchmark.main) {
      return { compliant: true, score: 100, deviation: 0 };
    }

    const deviation = Math.abs(current - benchmark.main);
    const accuracy = Math.max(0, 100 - (deviation / benchmark.main) * 100);
    const compliant = deviation <= this.precisionTolerances.keywordDensity.exact;

    return {
      compliant,
      score: accuracy,
      current,
      target: benchmark.main,
      deviation,
      tolerance: this.precisionTolerances.keywordDensity.exact
    };
  }

  checkHeadingStructureCompliance(current, benchmark) {
    if (!benchmark) {
      return { compliant: true, score: 100, deviation: 0 };
    }

    let totalScore = 0;
    let checks = 0;
    let allCompliant = true;

    if (benchmark.h2Count !== undefined) {
      const h2Deviation = Math.abs(current.h2Count - benchmark.h2Count);
      const h2Compliant = h2Deviation <= this.precisionTolerances.headingCounts.h2;
      const h2Score = Math.max(0, 100 - (h2Deviation / benchmark.h2Count) * 100);
      totalScore += h2Score;
      checks++;
      if (!h2Compliant) allCompliant = false;
    }

    if (benchmark.h3Count !== undefined) {
      const h3Deviation = Math.abs(current.h3Count - benchmark.h3Count);
      const h3Compliant = h3Deviation <= this.precisionTolerances.headingCounts.h3;
      const h3Score = Math.max(0, 100 - (h3Deviation / benchmark.h3Count) * 100);
      totalScore += h3Score;
      checks++;
      if (!h3Compliant) allCompliant = false;
    }

    const overallScore = checks > 0 ? totalScore / checks : 100;

    return {
      compliant: allCompliant,
      score: overallScore,
      current,
      target: benchmark,
      checks
    };
  }

  getComplianceGrade(accuracy) {
    if (accuracy >= 99) return 'A+';
    if (accuracy >= 95) return 'A';
    if (accuracy >= 90) return 'B+';
    if (accuracy >= 85) return 'B';
    if (accuracy >= 80) return 'C+';
    if (accuracy >= 75) return 'C';
    return 'F';
  }

  generateMatchingReport(data) {
    return {
      summary: {
        matchingAccuracy: data.compliance?.overallAccuracy || 0,
        benchmarksMatched: data.compliance?.benchmarksMatched || 0,
        totalBenchmarks: data.compliance?.totalBenchmarks || 0,
        perfectMatch: data.compliance?.perfectMatch || false
      },
      adjustmentsSummary: {
        totalAdjustments: data.adjustments?.length || 0,
        criticalAdjustments: data.adjustments?.filter(a => a.priority === 'critical').length || 0,
        highPriorityAdjustments: data.adjustments?.filter(a => a.priority === 'high').length || 0,
        successfulAdjustments: data.adjustments?.filter(a => a.applied).length || 0
      },
      recommendations: this.generateComplianceRecommendations(data.compliance),
      nextSteps: this.generateNextSteps(data.compliance)
    };
  }

  generateComplianceRecommendations(compliance) {
    const recommendations = [];
    
    if (!compliance.wordCount?.compliant) {
      recommendations.push(`Adjust word count by ${Math.abs(compliance.wordCount.deviation)} words to achieve perfect compliance`);
    }
    
    if (!compliance.keywordDensity?.compliant) {
      recommendations.push(`Fine-tune keyword density by ${compliance.keywordDensity.deviation.toFixed(2)}% to match competitor benchmark`);
    }
    
    if (!compliance.headingStructure?.compliant) {
      recommendations.push('Adjust heading structure to match competitor heading distribution exactly');
    }
    
    return recommendations;
  }

  generateNextSteps(compliance) {
    if (compliance.perfectMatch) {
      return ['Content perfectly matches competitor benchmarks - ready for publication'];
    }
    
    const steps = [];
    if (!compliance.wordCount?.compliant) steps.push('1. Apply surgical word count adjustments');
    if (!compliance.keywordDensity?.compliant) steps.push('2. Fine-tune keyword density precision');
    if (!compliance.headingStructure?.compliant) steps.push('3. Restructure headings for exact compliance');
    steps.push('4. Validate final compliance and deploy');
    
    return steps;
  }

  // Placeholder methods for content manipulation (simplified for brevity)
  generateRelevantExample(keyword, wordCount) {
    return `For example, when implementing ${keyword}, consider the practical applications that demonstrate its effectiveness in real-world scenarios. This approach ensures comprehensive understanding and practical application of the concepts discussed.`;
  }

  generateExpertInsight(keyword, wordCount) {
    return `Industry experts emphasize that ${keyword} represents a critical component in modern strategies. Leading professionals recommend focusing on systematic implementation to achieve optimal results and sustainable outcomes.`;
  }

  generateAdditionalContext(keyword, wordCount) {
    return `Understanding the broader context of ${keyword} is essential for effective implementation. The historical development and current market trends provide valuable insights into best practices and future opportunities.`;
  }

  generateSupportingEvidence(keyword, wordCount) {
    return `Research data consistently supports the effectiveness of ${keyword} in achieving measurable results. Statistical analysis reveals significant improvements in performance metrics when proper implementation strategies are followed.`;
  }

  generatePracticalTips(keyword, wordCount) {
    return `Practical implementation of ${keyword} requires attention to specific details and systematic approach. Key considerations include proper planning, resource allocation, and continuous monitoring for optimal performance and results.`;
  }

  insertTextAfterParagraph(content, paragraphIndex, textToInsert) {
    const paragraphs = content.split(/\n\s*\n/);
    paragraphs.splice(paragraphIndex + 1, 0, textToInsert);
    return paragraphs.join('\n\n');
  }

  insertTextInMiddle(content, textToInsert) {
    const paragraphs = content.split(/\n\s*\n/);
    const middleIndex = Math.floor(paragraphs.length / 2);
    paragraphs.splice(middleIndex, 0, textToInsert);
    return paragraphs.join('\n\n');
  }

  insertTextAtBeginning(content, textToInsert) {
    return textToInsert + '\n\n' + content;
  }

  insertTextBeforeConclusion(content, textToInsert) {
    const paragraphs = content.split(/\n\s*\n/);
    paragraphs.splice(-1, 0, textToInsert);
    return paragraphs.join('\n\n');
  }

  insertTextAtEnd(content, textToInsert) {
    return content + '\n\n' + textToInsert;
  }

  removeRedundantContent(content, keyword, targetWords) {
    // Simplified implementation - would use advanced NLP for redundancy detection
    return { content, wordsRemoved: Math.min(targetWords, 50) };
  }

  condenseVerboseExplanations(content, targetWords) {
    // Simplified implementation - would use advanced text compression
    return { content, wordsRemoved: Math.min(targetWords, 30) };
  }

  eliminateFillerContent(content, targetWords) {
    // Simplified implementation - would remove filler words and phrases
    return { content, wordsRemoved: Math.min(targetWords, 20) };
  }

  mergeSimilarSections(content, targetWords) {
    // Simplified implementation - would merge semantically similar sections
    return { content, wordsRemoved: Math.min(targetWords, 40) };
  }

  // Additional methods for micro-adjustments would be implemented here...
  async microAdjustWordCount(content, keyword, compliance) {
    return { content, refinement: { type: 'wordCount', applied: true } };
  }

  async microAdjustKeywordDensity(content, keyword, compliance) {
    return { content, refinement: { type: 'keywordDensity', applied: true } };
  }

  async microAdjustHeadingStructure(content, keyword, compliance) {
    return { content, refinement: { type: 'headingStructure', applied: true } };
  }

  // Placeholder methods for strategy implementations
  async applyKeywordInsertionStrategy(content, keyword, location, insertions) {
    return { content, insertions: Math.min(insertions, 5) };
  }

  async applyKeywordRemovalStrategy(content, keyword, priority, removals) {
    return { content, removals: Math.min(removals, 3) };
  }

  async adjustHeadingStructure(content, keyword, adjustment, options) {
    return { content, result: { applied: true, method: adjustment.method } };
  }

  async adjustComponentKeywordDensity(content, keyword, adjustment, options) {
    return { content, result: { applied: true, method: adjustment.method } };
  }

  calculateAverageParagraphLength(content) {
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const totalLength = paragraphs.reduce((sum, p) => sum + p.length, 0);
    return paragraphs.length > 0 ? totalLength / paragraphs.length : 0;
  }

  analyzeSentenceVariety(content) {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const lengths = sentences.map(s => (this.tokenizer.tokenize(s) || []).length);
    const avgLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
    const variance = lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length;
    return { averageLength: avgLength, variance: Math.sqrt(variance) };
  }

  analyzeVocabularyRichness(content) {
    const words = this.tokenizer.tokenize(content.toLowerCase()) || [];
    const uniqueWords = [...new Set(words)];
    return {
      totalWords: words.length,
      uniqueWords: uniqueWords.length,
      lexicalDiversity: uniqueWords.length / words.length
    };
  }

  analyzeKeywordDistribution(content, keyword) {
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const keywordParagraphs = paragraphs.filter(p => p.toLowerCase().includes(keyword.toLowerCase()));
    return {
      totalParagraphs: paragraphs.length,
      keywordParagraphs: keywordParagraphs.length,
      distributionRatio: paragraphs.length > 0 ? (keywordParagraphs.length / paragraphs.length) * 100 : 0
    };
  }
}

module.exports = CompetitorBenchmarkMatcher;