const axios = require('axios');
const cheerio = require('cheerio');
const natural = require('natural');
const stopword = require('stopword');

class PrecisionCompetitorAnalyzer {
  constructor() {
    this.serperApiKey = process.env.SERPER_API_KEY;
    this.groqApiKey = process.env.GROQ_API_KEY;
  }

  // Main method to perform surgical competitor analysis
  async analyzePrecisionCompetitors(keyword, options = {}) {
    try {
      console.log(`Starting precision competitor analysis for: ${keyword}`);
      
      const location = options.location || 'United States';
      const searchEngine = options.searchEngine || 'google';
      const includeLocalResults = options.includeLocal || false;

      // Step 1: Get top 5 ranking competitors
      const searchResults = await this.getTopCompetitors(keyword, location, searchEngine);
      
      if (!searchResults || searchResults.length === 0) {
        throw new Error('No competitors found for analysis');
      }

      // Step 2: Extract detailed metrics from each competitor
      const competitorMetrics = await this.extractPrecisionMetrics(searchResults, keyword);
      
      // Step 3: Calculate surgical averages and benchmarks
      const precisionBenchmarks = this.calculatePrecisionBenchmarks(competitorMetrics, keyword);
      
      // Step 4: Identify content optimization opportunities
      const optimizationOpportunities = this.identifyOptimizationOpportunities(competitorMetrics, keyword);
      
      // Step 5: Generate LSI and semantic keyword recommendations
      const semanticKeywords = this.extractSemanticKeywords(competitorMetrics, keyword);

      return {
        success: true,
        keyword,
        location,
        searchEngine,
        competitorMetrics,
        precisionBenchmarks,
        optimizationOpportunities,
        semanticKeywords,
        generatedAt: new Date().toISOString()
      };

    } catch (error) {
      console.error('Precision competitor analysis error:', error);
      return {
        success: false,
        error: error.message,
        keyword,
        competitorMetrics: [],
        precisionBenchmarks: null
      };
    }
  }

  // Get top 5 competitors using Serper API
  async getTopCompetitors(keyword, location, searchEngine) {
    try {
      const response = await axios.post('https://google.serper.dev/search', {
        q: keyword,
        gl: this.getCountryCode(location),
        hl: 'en',
        num: 10, // Get 10 results to ensure we get 5 analyzable ones
        autocorrect: true
      }, {
        headers: {
          'X-API-KEY': this.serperApiKey,
          'Content-Type': 'application/json'
        }
      });

      const organicResults = response.data.organic || [];
      
      // Filter and prioritize results for analysis
      const validResults = organicResults
        .filter(result => 
          result.link && 
          !this.isExcludedDomain(result.link) &&
          result.title &&
          result.snippet
        )
        .slice(0, 5) // Top 5 competitors
        .map((result, index) => ({
          position: index + 1,
          title: result.title,
          url: result.link,
          description: result.snippet,
          displayUrl: result.displayLink || '',
          domain: this.extractDomain(result.link)
        }));

      console.log(`Found ${validResults.length} valid competitors for analysis`);
      return validResults;

    } catch (error) {
      console.error('Error fetching competitors:', error);
      throw new Error(`Failed to fetch competitors: ${error.message}`);
    }
  }

  // Extract surgical precision metrics from each competitor
  async extractPrecisionMetrics(competitors, keyword) {
    const competitorMetrics = [];
    
    for (const competitor of competitors) {
      try {
        console.log(`Analyzing competitor: ${competitor.domain}`);
        
        // Fetch competitor page content
        const pageContent = await this.fetchPageContent(competitor.url);
        
        if (!pageContent) {
          console.warn(`Could not fetch content for: ${competitor.url}`);
          continue;
        }

        // Extract all precision metrics
        const metrics = {
          position: competitor.position,
          url: competitor.url,
          domain: competitor.domain,
          title: competitor.title,
          metaDescription: competitor.description,
          
          // Word count metrics
          wordCount: this.calculateWordCount(pageContent.textContent),
          
          // Keyword density metrics (surgical precision)
          keywordDensity: this.calculateSurgicalKeywordDensity(pageContent.textContent, keyword),
          
          // Heading structure analysis
          headingStructure: this.analyzeHeadingStructure(pageContent.html, keyword),
          
          // Content quality metrics
          contentQuality: this.assessContentQuality(pageContent.textContent, keyword),
          
          // Technical SEO metrics
          technicalSEO: this.analyzeTechnicalSEO(pageContent.html, keyword),
          
          // Semantic analysis
          semanticAnalysis: this.performSemanticAnalysis(pageContent.textContent, keyword),
          
          // E-E-A-T indicators
          eatIndicators: this.assessEATIndicators(pageContent.textContent, pageContent.html),
          
          // Content structure
          contentStructure: this.analyzeContentStructure(pageContent.html),
          
          // LSI keywords found
          lsiKeywords: this.extractLSIKeywords(pageContent.textContent, keyword)
        };

        competitorMetrics.push(metrics);
        
        // Add delay to avoid overwhelming servers
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        console.error(`Error analyzing competitor ${competitor.url}:`, error);
        // Continue with other competitors even if one fails
      }
    }

    return competitorMetrics;
  }

  // Fetch page content with comprehensive extraction
  async fetchPageContent(url) {
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive'
        },
        timeout: 15000,
        maxRedirects: 3
      });

      const $ = cheerio.load(response.data);
      
      // Remove script, style, and other non-content elements
      $('script, style, noscript, nav, footer, .navigation, .menu, .sidebar, .ads, .advertisement').remove();
      
      // Extract main content
      const mainContent = this.extractMainContent($);
      
      return {
        html: response.data,
        textContent: mainContent,
        cleanHtml: $.html()
      };

    } catch (error) {
      console.error(`Error fetching ${url}:`, error.message);
      return null;
    }
  }

  // Extract main content from page (avoid navigation, ads, etc.)
  extractMainContent($) {
    // Try common content selectors
    const contentSelectors = [
      'main', 
      'article', 
      '.content', 
      '.main-content', 
      '.post-content', 
      '.entry-content',
      '.article-content',
      '.page-content',
      '#content',
      '#main'
    ];

    for (const selector of contentSelectors) {
      const content = $(selector).text();
      if (content && content.length > 500) {
        return content;
      }
    }

    // Fallback: get body content and clean it
    $('nav, footer, aside, .nav, .navigation, .menu, .sidebar').remove();
    return $('body').text() || '';
  }

  // Calculate surgical keyword density for main keyword and components
  calculateSurgicalKeywordDensity(content, keyword) {
    const text = content.toLowerCase();
    const words = text.split(/\s+/).filter(word => word.length > 0);
    const totalWords = words.length;

    if (totalWords === 0) return { main: 0, components: {} };

    // Main keyword density
    const mainKeywordRegex = new RegExp(keyword.toLowerCase().replace(/\s+/g, '\\s+'), 'gi');
    const mainMatches = (text.match(mainKeywordRegex) || []).length;
    const mainDensity = (mainMatches / totalWords) * 100;

    // Component keyword densities
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    const componentDensities = {};

    keywordComponents.forEach(component => {
      if (component.length > 2) { // Skip very short words
        const componentRegex = new RegExp(`\\b${component}\\b`, 'gi');
        const componentMatches = (text.match(componentRegex) || []).length;
        componentDensities[component] = (componentMatches / totalWords) * 100;
      }
    });

    // Related phrase analysis
    const relatedPhrases = this.findRelatedPhrases(text, keyword);

    return {
      main: parseFloat(mainDensity.toFixed(2)),
      components: componentDensities,
      related: relatedPhrases,
      totalWords,
      mainMatches
    };
  }

  // Analyze heading structure with keyword optimization
  analyzeHeadingStructure(html, keyword) {
    const $ = cheerio.load(html);
    const headings = {
      h1: [],
      h2: [],
      h3: [],
      h4: [],
      h5: [],
      h6: []
    };

    // Extract all headings
    for (let i = 1; i <= 6; i++) {
      $(`h${i}`).each((index, element) => {
        const text = $(element).text().trim();
        if (text) {
          headings[`h${i}`].push({
            text,
            hasMainKeyword: text.toLowerCase().includes(keyword.toLowerCase()),
            hasComponentKeywords: this.checkComponentKeywords(text, keyword),
            position: index + 1
          });
        }
      });
    }

    // Calculate heading statistics
    const totalHeadings = Object.values(headings).reduce((sum, arr) => sum + arr.length, 0);
    const headingsWithKeyword = Object.values(headings)
      .flat()
      .filter(h => h.hasMainKeyword).length;

    const keywordOptimizationRatio = totalHeadings > 0 ? (headingsWithKeyword / totalHeadings) * 100 : 0;

    return {
      headings,
      statistics: {
        total: totalHeadings,
        h1Count: headings.h1.length,
        h2Count: headings.h2.length,
        h3Count: headings.h3.length,
        h4Count: headings.h4.length,
        h5Count: headings.h5.length,
        h6Count: headings.h6.length,
        keywordOptimized: headingsWithKeyword,
        keywordOptimizationRatio: parseFloat(keywordOptimizationRatio.toFixed(2))
      }
    };
  }

  // Check if text contains component keywords
  checkComponentKeywords(text, keyword) {
    const components = keyword.toLowerCase().split(/\s+/);
    const textLower = text.toLowerCase();
    
    return components.reduce((acc, component) => {
      acc[component] = textLower.includes(component);
      return acc;
    }, {});
  }

  // Calculate word count accurately
  calculateWordCount(text) {
    if (!text) return 0;
    
    const words = text
      .replace(/[^\w\s]/g, ' ') // Replace punctuation with spaces
      .split(/\s+/)
      .filter(word => word.length > 0);
    
    return words.length;
  }

  // Assess content quality with multiple metrics
  assessContentQuality(content, keyword) {
    const wordCount = this.calculateWordCount(content);
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    
    // Readability assessment (simplified Flesch-Kincaid)
    const avgWordsPerSentence = sentences.length > 0 ? wordCount / sentences.length : 0;
    const readabilityScore = this.calculateReadabilityScore(content);
    
    // Content depth assessment
    const uniqueWords = new Set(content.toLowerCase().match(/\b\w+\b/g) || []).size;
    const lexicalDiversity = wordCount > 0 ? uniqueWords / wordCount : 0;

    return {
      wordCount,
      sentenceCount: sentences.length,
      paragraphCount: paragraphs.length,
      avgWordsPerSentence: parseFloat(avgWordsPerSentence.toFixed(1)),
      readabilityScore: parseFloat(readabilityScore.toFixed(1)),
      lexicalDiversity: parseFloat(lexicalDiversity.toFixed(3)),
      contentDepth: this.assessContentDepth(content, keyword)
    };
  }

  // Calculate readability score (simplified)
  calculateReadabilityScore(text) {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const syllables = words.reduce((count, word) => count + this.countSyllables(word), 0);
    
    if (sentences.length === 0 || words.length === 0) return 0;
    
    // Simplified Flesch Reading Ease
    const avgSentenceLength = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;
    
    return 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
  }

  // Count syllables in a word (simplified)
  countSyllables(word) {
    const vowels = 'aeiouy';
    let count = 0;
    let previousWasVowel = false;
    
    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i].toLowerCase());
      if (isVowel && !previousWasVowel) {
        count++;
      }
      previousWasVowel = isVowel;
    }
    
    // Adjust for silent e
    if (word.endsWith('e') && count > 1) {
      count--;
    }
    
    return Math.max(1, count);
  }

  // Assess content depth and topical coverage
  assessContentDepth(content, keyword) {
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    const contentLower = content.toLowerCase();
    
    // Look for topic coverage indicators
    const topicIndicators = [
      'benefits', 'advantages', 'features', 'types', 'how to', 'guide', 'tips',
      'best practices', 'examples', 'case study', 'research', 'study', 'analysis',
      'comparison', 'review', 'pros', 'cons', 'cost', 'price', 'quality'
    ];
    
    const topicCoverage = topicIndicators.filter(indicator => 
      contentLower.includes(indicator)
    ).length;
    
    // Semantic richness
    const semanticWords = this.extractSemanticWords(content, keyword);
    
    return {
      topicCoverage,
      topicCoverageRatio: topicCoverage / topicIndicators.length,
      semanticRichness: semanticWords.length,
      hasExamples: contentLower.includes('example') || contentLower.includes('for instance'),
      hasStatistics: /\d+%|\d+\s*(million|billion|thousand)/.test(contentLower),
      hasComparisons: contentLower.includes('vs') || contentLower.includes('versus') || contentLower.includes('compared to')
    };
  }

  // Analyze technical SEO elements
  analyzeTechnicalSEO(html, keyword) {
    const $ = cheerio.load(html);
    
    // Title tag analysis
    const title = $('title').text();
    const titleHasKeyword = title.toLowerCase().includes(keyword.toLowerCase());
    
    // Meta description analysis
    const metaDescription = $('meta[name="description"]').attr('content') || '';
    const metaHasKeyword = metaDescription.toLowerCase().includes(keyword.toLowerCase());
    
    // Schema markup detection
    const hasSchema = $('script[type="application/ld+json"]').length > 0;
    
    // Image optimization
    const images = $('img');
    let optimizedImages = 0;
    images.each((i, img) => {
      const alt = $(img).attr('alt') || '';
      if (alt.toLowerCase().includes(keyword.toLowerCase())) {
        optimizedImages++;
      }
    });
    
    return {
      title: {
        text: title,
        hasKeyword: titleHasKeyword,
        length: title.length
      },
      metaDescription: {
        text: metaDescription,
        hasKeyword: metaHasKeyword,
        length: metaDescription.length
      },
      schema: {
        hasSchema,
        schemaCount: $('script[type="application/ld+json"]').length
      },
      images: {
        total: images.length,
        optimized: optimizedImages,
        optimizationRatio: images.length > 0 ? (optimizedImages / images.length) * 100 : 0
      }
    };
  }

  // Perform semantic analysis
  performSemanticAnalysis(content, keyword) {
    const semanticWords = this.extractSemanticWords(content, keyword);
    const lsiKeywords = this.extractLSIKeywords(content, keyword);
    const entities = this.extractEntities(content);
    
    return {
      semanticWords,
      lsiKeywords,
      entities,
      semanticDensity: semanticWords.length / this.calculateWordCount(content)
    };
  }

  // Extract semantic words related to keyword
  extractSemanticWords(content, keyword) {
    const words = content.toLowerCase().match(/\b\w+\b/g) || [];
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    
    // Simple semantic extraction (would be enhanced with word embeddings in production)
    const semanticWords = new Set();
    
    // Add plural/singular forms
    keywordComponents.forEach(component => {
      words.forEach(word => {
        if (word.includes(component) || component.includes(word)) {
          if (Math.abs(word.length - component.length) <= 2) {
            semanticWords.add(word);
          }
        }
      });
    });
    
    return Array.from(semanticWords).slice(0, 20);
  }

  // Extract LSI keywords
  extractLSIKeywords(content, keyword) {
    const sentences = content.split(/[.!?]+/);
    const keywordSentences = sentences.filter(sentence => 
      sentence.toLowerCase().includes(keyword.toLowerCase())
    );
    
    const lsiKeywords = new Set();
    
    keywordSentences.forEach(sentence => {
      const words = sentence.toLowerCase().match(/\b\w+\b/g) || [];
      words.forEach(word => {
        if (word.length > 3 && !this.isStopWord(word) && !keyword.toLowerCase().includes(word)) {
          lsiKeywords.add(word);
        }
      });
    });
    
    return Array.from(lsiKeywords).slice(0, 15);
  }

  // Extract entities (simplified)
  extractEntities(content) {
    const entities = [];
    
    // Extract capitalized words that might be entities
    const words = content.match(/\b[A-Z][a-z]+\b/g) || [];
    const entityCandidates = new Set(words);
    
    // Filter out common words
    const filteredEntities = Array.from(entityCandidates).filter(word => 
      word.length > 3 && !this.isCommonWord(word)
    );
    
    return filteredEntities.slice(0, 10);
  }

  // Assess E-A-T indicators
  assessEATIndicators(content, html) {
    const $ = cheerio.load(html);
    const contentLower = content.toLowerCase();
    
    // Expertise indicators
    const expertiseIndicators = [
      'expert', 'professional', 'certified', 'licensed', 'qualified', 'experienced',
      'specialist', 'authority', 'researcher', 'phd', 'dr.', 'years of experience'
    ];
    
    // Authority indicators
    const authorityIndicators = [
      'published in', 'featured in', 'award', 'recognition', 'accredited',
      'established', 'founded', 'industry leader', 'trusted by'
    ];
    
    // Trust indicators
    const trustIndicators = [
      'privacy policy', 'terms of service', 'contact us', 'about us',
      'testimonial', 'review', 'guarantee', 'secure', 'ssl'
    ];
    
    const expertise = expertiseIndicators.filter(indicator => contentLower.includes(indicator)).length;
    const authority = authorityIndicators.filter(indicator => contentLower.includes(indicator)).length;
    const trust = trustIndicators.filter(indicator => contentLower.includes(indicator) || $(html).text().toLowerCase().includes(indicator)).length;
    
    return {
      expertise: {
        score: expertise,
        indicators: expertiseIndicators.filter(indicator => contentLower.includes(indicator))
      },
      authority: {
        score: authority,
        indicators: authorityIndicators.filter(indicator => contentLower.includes(indicator))
      },
      trust: {
        score: trust,
        indicators: trustIndicators.filter(indicator => contentLower.includes(indicator) || $(html).text().toLowerCase().includes(indicator))
      },
      overallEATScore: (expertise + authority + trust) / 3
    };
  }

  // Analyze content structure
  analyzeContentStructure(html) {
    const $ = cheerio.load(html);
    
    return {
      hasList: $('ul, ol').length > 0,
      listCount: $('ul, ol').length,
      hasTable: $('table').length > 0,
      tableCount: $('table').length,
      hasImages: $('img').length > 0,
      imageCount: $('img').length,
      hasVideos: $('video, iframe[src*="youtube"], iframe[src*="vimeo"]').length > 0,
      paragraphCount: $('p').length,
      linkCount: $('a').length
    };
  }

  // Calculate precision benchmarks from competitor metrics
  calculatePrecisionBenchmarks(competitorMetrics, keyword) {
    if (!competitorMetrics || competitorMetrics.length === 0) {
      return null;
    }

    const validMetrics = competitorMetrics.filter(m => m.wordCount > 0);
    
    if (validMetrics.length === 0) {
      return null;
    }

    // Word count statistics
    const wordCounts = validMetrics.map(m => m.wordCount);
    const avgWordCount = Math.round(wordCounts.reduce((sum, count) => sum + count, 0) / wordCounts.length);
    const minWordCount = Math.min(...wordCounts);
    const maxWordCount = Math.max(...wordCounts);

    // Keyword density statistics
    const mainDensities = validMetrics.map(m => m.keywordDensity.main).filter(d => d > 0);
    const avgMainDensity = mainDensities.length > 0 ? 
      parseFloat((mainDensities.reduce((sum, d) => sum + d, 0) / mainDensities.length).toFixed(2)) : 0;

    // Component keyword densities
    const componentBenchmarks = {};
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    
    keywordComponents.forEach(component => {
      const componentDensities = validMetrics
        .map(m => m.keywordDensity.components[component])
        .filter(d => d !== undefined && d > 0);
      
      if (componentDensities.length > 0) {
        componentBenchmarks[component] = parseFloat(
          (componentDensities.reduce((sum, d) => sum + d, 0) / componentDensities.length).toFixed(2)
        );
      }
    });

    // Heading statistics
    const headingCounts = validMetrics.map(m => m.headingStructure.statistics);
    const avgTotalHeadings = Math.round(
      headingCounts.reduce((sum, h) => sum + h.total, 0) / headingCounts.length
    );
    const avgH2Count = Math.round(
      headingCounts.reduce((sum, h) => sum + h.h2Count, 0) / headingCounts.length
    );
    const avgH3Count = Math.round(
      headingCounts.reduce((sum, h) => sum + h.h3Count, 0) / headingCounts.length
    );

    // Content quality benchmarks
    const qualityMetrics = validMetrics.map(m => m.contentQuality);
    const avgSentences = Math.round(
      qualityMetrics.reduce((sum, q) => sum + q.sentenceCount, 0) / qualityMetrics.length
    );
    const avgParagraphs = Math.round(
      qualityMetrics.reduce((sum, q) => sum + q.paragraphCount, 0) / qualityMetrics.length
    );

    return {
      wordCount: {
        target: avgWordCount,
        min: minWordCount,
        max: maxWordCount,
        range: maxWordCount - minWordCount
      },
      keywordDensity: {
        main: avgMainDensity,
        components: componentBenchmarks
      },
      headingStructure: {
        totalHeadings: avgTotalHeadings,
        h2Count: avgH2Count,
        h3Count: avgH3Count
      },
      contentStructure: {
        sentences: avgSentences,
        paragraphs: avgParagraphs
      },
      competitorCount: validMetrics.length
    };
  }

  // Identify optimization opportunities
  identifyOptimizationOpportunities(competitorMetrics, keyword) {
    const opportunities = [];
    
    if (!competitorMetrics || competitorMetrics.length === 0) {
      return opportunities;
    }

    // Word count opportunity
    const wordCounts = competitorMetrics.map(m => m.wordCount).filter(w => w > 0);
    if (wordCounts.length > 0) {
      const avgWordCount = wordCounts.reduce((sum, count) => sum + count, 0) / wordCounts.length;
      opportunities.push({
        type: 'word_count',
        recommendation: `Target ${Math.round(avgWordCount)} words for optimal competition`,
        priority: 'high',
        benchmark: Math.round(avgWordCount)
      });
    }

    // Keyword density opportunity
    const densities = competitorMetrics.map(m => m.keywordDensity.main).filter(d => d > 0);
    if (densities.length > 0) {
      const avgDensity = densities.reduce((sum, d) => sum + d, 0) / densities.length;
      opportunities.push({
        type: 'keyword_density',
        recommendation: `Target ${avgDensity.toFixed(2)}% keyword density`,
        priority: 'high',
        benchmark: parseFloat(avgDensity.toFixed(2))
      });
    }

    // Heading optimization opportunity
    const headingOptimization = competitorMetrics
      .map(m => m.headingStructure.statistics.keywordOptimizationRatio)
      .filter(r => r > 0);
    
    if (headingOptimization.length > 0) {
      const avgOptimization = headingOptimization.reduce((sum, r) => sum + r, 0) / headingOptimization.length;
      opportunities.push({
        type: 'heading_optimization',
        recommendation: `Optimize ${avgOptimization.toFixed(1)}% of headings with target keyword`,
        priority: 'medium',
        benchmark: parseFloat(avgOptimization.toFixed(1))
      });
    }

    return opportunities;
  }

  // Extract semantic keywords from all competitors
  extractSemanticKeywords(competitorMetrics, keyword) {
    const allSemanticWords = new Set();
    const allLSIKeywords = new Set();
    
    competitorMetrics.forEach(competitor => {
      if (competitor.semanticAnalysis) {
        competitor.semanticAnalysis.semanticWords.forEach(word => allSemanticWords.add(word));
        competitor.semanticAnalysis.lsiKeywords.forEach(word => allLSIKeywords.add(word));
      }
      
      if (competitor.lsiKeywords) {
        competitor.lsiKeywords.forEach(word => allLSIKeywords.add(word));
      }
    });

    return {
      semantic: Array.from(allSemanticWords).slice(0, 25),
      lsi: Array.from(allLSIKeywords).slice(0, 25)
    };
  }

  // Helper methods
  getCountryCode(location) {
    const countryMap = {
      'United States': 'us',
      'United Kingdom': 'gb',
      'Canada': 'ca',
      'Australia': 'au',
      'Germany': 'de',
      'France': 'fr',
      'Spain': 'es',
      'Italy': 'it',
      'Netherlands': 'nl',
      'India': 'in'
    };
    return countryMap[location] || 'us';
  }

  extractDomain(url) {
    try {
      return new URL(url).hostname;
    } catch {
      return url;
    }
  }

  isExcludedDomain(url) {
    const excludedDomains = [
      'youtube.com', 'facebook.com', 'twitter.com', 'instagram.com',
      'linkedin.com', 'pinterest.com', 'reddit.com', 'quora.com',
      'amazon.com', 'ebay.com', 'wikipedia.org'
    ];
    
    const domain = this.extractDomain(url);
    return excludedDomains.some(excluded => domain.includes(excluded));
  }

  findRelatedPhrases(text, keyword) {
    const keywordWords = keyword.toLowerCase().split(/\s+/);
    const sentences = text.split(/[.!?]+/);
    const relatedPhrases = new Set();
    
    sentences.forEach(sentence => {
      const sentenceLower = sentence.toLowerCase();
      if (keywordWords.some(word => sentenceLower.includes(word))) {
        // Extract 2-3 word phrases that include keyword components
        const words = sentenceLower.match(/\b\w+\b/g) || [];
        for (let i = 0; i < words.length - 1; i++) {
          const phrase = `${words[i]} ${words[i + 1]}`;
          if (keywordWords.some(kw => phrase.includes(kw)) && phrase !== keyword.toLowerCase()) {
            relatedPhrases.add(phrase);
          }
          
          if (i < words.length - 2) {
            const triPhrase = `${words[i]} ${words[i + 1]} ${words[i + 2]}`;
            if (keywordWords.some(kw => triPhrase.includes(kw)) && triPhrase !== keyword.toLowerCase()) {
              relatedPhrases.add(triPhrase);
            }
          }
        }
      }
    });
    
    return Array.from(relatedPhrases).slice(0, 10);
  }

  isStopWord(word) {
    return stopword.removeStopwords([word]).length === 0;
  }

  isCommonWord(word) {
    const commonWords = [
      'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'has', 'let', 'put', 'say', 'she', 'too', 'use'
    ];
    return commonWords.includes(word.toLowerCase());
  }
}

module.exports = PrecisionCompetitorAnalyzer;