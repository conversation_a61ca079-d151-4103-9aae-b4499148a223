const natural = require('natural');
const axios = require('axios');

class AdvancedSEOOptimizer {
  constructor() {
    this.tokenizer = new natural.WordTokenizer();
    this.stemmer = natural.PorterStemmer;
    this.serperApiKey = process.env.SERPER_API_KEY;
    
    // Initialize SEO optimization rules
    this.seoRules = this.initializeSEORules();
    this.searchEngineAlgorithms = this.initializeSearchEngineAlgorithms();
    this.technicalSEOFactors = this.initializeTechnicalSEOFactors();
    this.rankingFactors = this.initializeRankingFactors();
  }

  // Main method for comprehensive SEO optimization
  async optimizeSEOSignals(content, keyword, competitorBenchmarks, semanticData, options = {}) {
    try {
      console.log(`Starting advanced SEO optimization for keyword: ${keyword}`);

      // Analyze current SEO signals
      const currentSEOAnalysis = await this.analyzeSEOSignals(content, keyword, semanticData);
      
      // Analyze SERP features and intent
      const serpAnalysis = await this.analyzeSERPFeatures(keyword, options.location);
      
      // Optimize for different search engines
      const multiEngineOptimization = await this.optimizeForMultipleEngines(content, keyword, currentSEOAnalysis, serpAnalysis);
      
      // Optimize technical SEO factors
      const technicalOptimization = this.optimizeTechnicalSEO(multiEngineOptimization.content, keyword, currentSEOAnalysis);
      
      // Optimize for featured snippets and rich results
      const richResultsOptimization = this.optimizeForRichResults(technicalOptimization.content, keyword, serpAnalysis);
      
      // Optimize for voice search and AI systems
      const voiceSearchOptimization = this.optimizeForVoiceSearch(richResultsOptimization.content, keyword);
      
      // Optimize for mobile and Core Web Vitals signals
      const mobileOptimization = this.optimizeForMobile(voiceSearchOptimization.content, keyword);
      
      // Perform final SEO validation
      const finalValidation = await this.performSEOValidation(mobileOptimization.content, keyword, serpAnalysis);

      return {
        success: true,
        content: mobileOptimization.content,
        seoOptimization: {
          currentAnalysis: currentSEOAnalysis,
          serpAnalysis,
          multiEngineOptimization: multiEngineOptimization.optimization,
          technicalOptimization: technicalOptimization.optimization,
          richResultsOptimization: richResultsOptimization.optimization,
          voiceSearchOptimization: voiceSearchOptimization.optimization,
          mobileOptimization: mobileOptimization.optimization,
          finalValidation
        },
        metadata: {
          seoScore: finalValidation.overallSEOScore,
          searchEngineCompatibility: finalValidation.searchEngineCompatibility,
          rankingPotential: finalValidation.rankingPotential,
          featuredSnippetPotential: finalValidation.featuredSnippetPotential,
          processedAt: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('Advanced SEO optimization error:', error);
      return {
        success: false,
        error: error.message,
        content: content
      };
    }
  }

  // Initialize SEO rules
  initializeSEORules() {
    return {
      titleOptimization: {
        minLength: 30,
        maxLength: 60,
        keywordPosition: 'beginning', // beginning, middle, end
        modifiers: ['best', 'guide', 'tips', 'complete', 'ultimate', '2024']
      },
      metaDescription: {
        minLength: 120,
        maxLength: 160,
        mustInclude: ['keyword', 'call_to_action'],
        actionWords: ['learn', 'discover', 'find out', 'get', 'see how']
      },
      headingStructure: {
        h1: { count: 1, keywordRequired: true, maxLength: 70 },
        h2: { minCount: 3, maxCount: 8, keywordRatio: 0.5 },
        h3: { minCount: 5, maxCount: 15, keywordRatio: 0.3 }
      },
      internalLinking: {
        minLinks: 2,
        maxLinks: 10,
        anchorTextVariation: true,
        relevantPages: true
      },
      imageOptimization: {
        altTextRequired: true,
        fileNameOptimized: true,
        compressionRequired: true,
        responsiveRequired: true
      },
      contentStructure: {
        introduction: { paragraphs: 1, keywordMentions: 1 },
        body: { minParagraphs: 5, keywordDistribution: 'even' },
        conclusion: { paragraphs: 1, keywordMentions: 1, callToAction: true }
      }
    };
  }

  // Initialize search engine algorithms
  initializeSearchEngineAlgorithms() {
    return {
      google: {
        primaryFactors: ['content_quality', 'user_experience', 'technical_seo', 'backlinks'],
        updates: ['helpful_content', 'page_experience', 'core_web_vitals'],
        preferences: {
          contentLength: 'comprehensive',
          keywordDensity: 'natural',
          freshness: 'important',
          entitySEO: 'critical'
        }
      },
      bing: {
        primaryFactors: ['content_relevance', 'social_signals', 'technical_seo', 'domain_authority'],
        preferences: {
          contentLength: 'detailed',
          keywordDensity: 'moderate',
          multimedia: 'important',
          localSEO: 'enhanced'
        }
      },
      yahoo: {
        primaryFactors: ['content_quality', 'backlinks', 'domain_age', 'technical_seo'],
        preferences: {
          contentLength: 'comprehensive',
          keywordDensity: 'traditional',
          multimedia: 'moderate'
        }
      },
      duckduckgo: {
        primaryFactors: ['content_relevance', 'privacy_signals', 'technical_seo'],
        preferences: {
          contentLength: 'concise',
          keywordDensity: 'natural',
          privacy: 'critical'
        }
      },
      yandex: {
        primaryFactors: ['content_quality', 'behavioral_factors', 'technical_seo', 'regional_relevance'],
        preferences: {
          contentLength: 'detailed',
          keywordDensity: 'moderate',
          localRelevance: 'important'
        }
      }
    };
  }

  // Initialize technical SEO factors
  initializeTechnicalSEOFactors() {
    return {
      coreWebVitals: {
        lcp: { target: '<2.5s', impact: 'high' }, // Largest Contentful Paint
        fid: { target: '<100ms', impact: 'high' }, // First Input Delay
        cls: { target: '<0.1', impact: 'high' } // Cumulative Layout Shift
      },
      pageSpeed: {
        desktop: { target: '<3s', impact: 'medium' },
        mobile: { target: '<2s', impact: 'high' }
      },
      mobileUsability: {
        responsiveDesign: { required: true, impact: 'high' },
        tapTargets: { minSize: '44px', impact: 'medium' },
        textReadability: { minSize: '16px', impact: 'medium' }
      },
      crawlability: {
        robotsTxt: { required: true, impact: 'medium' },
        xmlSitemap: { required: true, impact: 'medium' },
        internalLinking: { required: true, impact: 'high' }
      },
      structuredData: {
        schemaMarkup: { required: true, impact: 'high' },
        breadcrumbs: { recommended: true, impact: 'medium' },
        faq: { recommended: true, impact: 'medium' }
      }
    };
  }

  // Initialize ranking factors
  initializeRankingFactors() {
    return {
      onPage: {
        titleTag: { weight: 10, optimization: 'critical' },
        metaDescription: { weight: 5, optimization: 'important' },
        headingTags: { weight: 8, optimization: 'critical' },
        keywordDensity: { weight: 7, optimization: 'important' },
        contentLength: { weight: 6, optimization: 'important' },
        semanticKeywords: { weight: 8, optimization: 'critical' },
        internalLinks: { weight: 6, optimization: 'important' },
        imageOptimization: { weight: 4, optimization: 'moderate' }
      },
      technical: {
        pageSpeed: { weight: 9, optimization: 'critical' },
        mobileOptimization: { weight: 9, optimization: 'critical' },
        https: { weight: 5, optimization: 'important' },
        structuredData: { weight: 7, optimization: 'important' },
        crawlability: { weight: 6, optimization: 'important' }
      },
      userExperience: {
        clickThroughRate: { weight: 8, optimization: 'critical' },
        bounceRate: { weight: 7, optimization: 'important' },
        dwellTime: { weight: 8, optimization: 'critical' },
        userEngagement: { weight: 6, optimization: 'important' }
      },
      contentQuality: {
        comprehensiveness: { weight: 9, optimization: 'critical' },
        originality: { weight: 8, optimization: 'critical' },
        expertise: { weight: 8, optimization: 'critical' },
        freshness: { weight: 5, optimization: 'moderate' }
      }
    };
  }

  // Analyze current SEO signals
  async analyzeSEOSignals(content, keyword, semanticData) {
    const analysis = {
      onPageSEO: this.analyzeOnPageSEO(content, keyword),
      technicalSEO: this.analyzeTechnicalSEO(content),
      contentSEO: this.analyzeContentSEO(content, keyword, semanticData),
      userExperienceSEO: this.analyzeUserExperienceSEO(content),
      semanticSEO: this.analyzeSemanticSEO(content, keyword, semanticData),
      mobileSEO: this.analyzeMobileSEO(content),
      localSEO: this.analyzeLocalSEO(content, keyword)
    };

    // Calculate overall SEO score
    analysis.overallSEOScore = this.calculateOverallSEOScore(analysis);

    return analysis;
  }

  // Analyze on-page SEO factors
  analyzeOnPageSEO(content, keyword) {
    const keywordLower = keyword.toLowerCase();
    const contentLower = content.toLowerCase();

    // Analyze title tag (simulated)
    const titleAnalysis = this.analyzeTitleTag(content, keyword);
    
    // Analyze meta description (simulated)
    const metaAnalysis = this.analyzeMetaDescription(content, keyword);
    
    // Analyze heading structure
    const headingAnalysis = this.analyzeHeadingStructure(content, keyword);
    
    // Analyze keyword usage
    const keywordAnalysis = this.analyzeKeywordUsage(content, keyword);
    
    // Analyze internal linking (simulated)
    const linkingAnalysis = this.analyzeInternalLinking(content);
    
    // Analyze URL structure (simulated)
    const urlAnalysis = this.analyzeURLStructure(keyword);

    return {
      titleTag: titleAnalysis,
      metaDescription: metaAnalysis,
      headingStructure: headingAnalysis,
      keywordUsage: keywordAnalysis,
      internalLinking: linkingAnalysis,
      urlStructure: urlAnalysis,
      score: this.calculateOnPageScore({
        titleTag: titleAnalysis,
        metaDescription: metaAnalysis,
        headingStructure: headingAnalysis,
        keywordUsage: keywordAnalysis
      })
    };
  }

  // Analyze title tag
  analyzeTitleTag(content, keyword) {
    // Simulate title tag extraction from content
    const firstHeading = content.match(/^#\s+(.+)$/m) || content.match(/<h1[^>]*>([^<]+)<\/h1>/i);
    const title = firstHeading ? firstHeading[1].trim() : '';
    
    const analysis = {
      title,
      length: title.length,
      hasKeyword: title.toLowerCase().includes(keyword.toLowerCase()),
      keywordPosition: this.getKeywordPosition(title, keyword),
      hasModifiers: this.hasModifiers(title),
      isOptimized: false
    };

    // Check optimization
    analysis.isOptimized = 
      analysis.length >= this.seoRules.titleOptimization.minLength &&
      analysis.length <= this.seoRules.titleOptimization.maxLength &&
      analysis.hasKeyword;

    return analysis;
  }

  // Analyze meta description
  analyzeMetaDescription(content, keyword) {
    // Simulate meta description from first paragraph
    const firstParagraph = content.split('\n\n')[0] || '';
    const description = firstParagraph.substring(0, 160);
    
    const analysis = {
      description,
      length: description.length,
      hasKeyword: description.toLowerCase().includes(keyword.toLowerCase()),
      hasCallToAction: this.hasCallToAction(description),
      isOptimized: false
    };

    analysis.isOptimized = 
      analysis.length >= this.seoRules.metaDescription.minLength &&
      analysis.length <= this.seoRules.metaDescription.maxLength &&
      analysis.hasKeyword;

    return analysis;
  }

  // Analyze heading structure
  analyzeHeadingStructure(content, keyword) {
    const h1Matches = content.match(/^# (.+)$/gm) || content.match(/<h1[^>]*>([^<]+)<\/h1>/gi) || [];
    const h2Matches = content.match(/^## (.+)$/gm) || content.match(/<h2[^>]*>([^<]+)<\/h2>/gi) || [];
    const h3Matches = content.match(/^### (.+)$/gm) || content.match(/<h3[^>]*>([^<]+)<\/h3>/gi) || [];
    
    const keywordLower = keyword.toLowerCase();
    
    // Count keyword-optimized headings
    const h1WithKeyword = h1Matches.filter(h => h.toLowerCase().includes(keywordLower)).length;
    const h2WithKeyword = h2Matches.filter(h => h.toLowerCase().includes(keywordLower)).length;
    const h3WithKeyword = h3Matches.filter(h => h.toLowerCase().includes(keywordLower)).length;

    return {
      h1: {
        count: h1Matches.length,
        withKeyword: h1WithKeyword,
        isOptimized: h1Matches.length === 1 && h1WithKeyword >= 1
      },
      h2: {
        count: h2Matches.length,
        withKeyword: h2WithKeyword,
        isOptimized: h2Matches.length >= 3 && h2WithKeyword >= Math.floor(h2Matches.length * 0.5)
      },
      h3: {
        count: h3Matches.length,
        withKeyword: h3WithKeyword,
        isOptimized: h3Matches.length >= 5 && h3WithKeyword >= Math.floor(h3Matches.length * 0.3)
      },
      hierarchy: this.checkHeadingHierarchy(content),
      overallScore: this.calculateHeadingScore(h1Matches, h2Matches, h3Matches, keyword)
    };
  }

  // Analyze keyword usage
  analyzeKeywordUsage(content, keyword) {
    const words = this.tokenizer.tokenize(content.toLowerCase()) || [];
    const keywordMatches = (content.toLowerCase().match(new RegExp(keyword.toLowerCase().replace(/\s+/g, '\\s+'), 'g')) || []).length;
    const density = (keywordMatches / words.length) * 100;
    
    // Analyze keyword distribution
    const distribution = this.analyzeKeywordDistribution(content, keyword);
    
    // Analyze keyword variations
    const variations = this.analyzeKeywordVariations(content, keyword);
    
    // Check for keyword stuffing
    const isStuffed = density > 5 || this.detectKeywordStuffing(content, keyword);

    return {
      density: parseFloat(density.toFixed(2)),
      matches: keywordMatches,
      distribution,
      variations,
      isStuffed,
      naturalUsage: this.assessNaturalUsage(content, keyword),
      proximity: this.analyzeKeywordProximity(content, keyword),
      isOptimized: density >= 1 && density <= 3 && !isStuffed
    };
  }

  // Analyze internal linking
  analyzeInternalLinking(content) {
    const linkPattern = /\[([^\]]+)\]\(([^)]+)\)/g;
    const links = [...content.matchAll(linkPattern)];
    
    return {
      totalLinks: links.length,
      anchorTexts: links.map(link => link[1]),
      hasVariedAnchors: this.hasVariedAnchorTexts(links),
      isOptimized: links.length >= 2 && links.length <= 10
    };
  }

  // Analyze URL structure
  analyzeURLStructure(keyword) {
    // Simulate URL analysis
    const simulatedURL = `/${keyword.toLowerCase().replace(/\s+/g, '-')}`;
    
    return {
      url: simulatedURL,
      hasKeyword: true,
      isSlugified: true,
      length: simulatedURL.length,
      isOptimized: simulatedURL.length < 100 && !simulatedURL.includes('_')
    };
  }

  // Analyze technical SEO
  analyzeTechnicalSEO(content) {
    return {
      pageSpeed: this.analyzePageSpeed(),
      mobileOptimization: this.analyzeMobileOptimization(),
      structuredData: this.analyzeStructuredData(content),
      crawlability: this.analyzeCrawlability(),
      https: this.analyzeHTTPS(),
      coreWebVitals: this.analyzeCoreWebVitals(),
      score: 85 // Simulated technical SEO score
    };
  }

  // Analyze content SEO
  analyzeContentSEO(content, keyword, semanticData) {
    const words = this.tokenizer.tokenize(content) || [];
    
    return {
      wordCount: words.length,
      readability: this.analyzeReadability(content),
      comprehensiveness: this.analyzeComprehensiveness(content, keyword),
      originality: this.analyzeOriginality(content),
      topicalRelevance: this.analyzeTopicalRelevance(content, keyword, semanticData),
      entityCoverage: this.analyzeEntityCoverage(content, semanticData),
      freshness: this.analyzeFreshness(content),
      score: this.calculateContentSEOScore(content, keyword, semanticData)
    };
  }

  // Analyze user experience SEO
  analyzeUserExperienceSEO(content) {
    return {
      engagement: this.analyzeEngagementSignals(content),
      navigation: this.analyzeNavigation(content),
      accessibility: this.analyzeAccessibility(content),
      interactivity: this.analyzeInteractivity(content),
      visualAppeal: this.analyzeVisualAppeal(content),
      score: this.calculateUXScore(content)
    };
  }

  // Analyze semantic SEO
  analyzeSemanticSEO(content, keyword, semanticData) {
    return {
      entityMentions: this.analyzeEntityMentions(content, semanticData),
      topicalClusters: this.analyzeTopicalClusters(content, keyword),
      semanticKeywords: this.analyzeSemanticKeywords(content, semanticData),
      contextualRelevance: this.analyzeContextualRelevance(content, keyword),
      intentAlignment: this.analyzeIntentAlignment(content, keyword),
      knowledgeGraph: this.analyzeKnowledgeGraphSignals(content, keyword),
      score: this.calculateSemanticSEOScore(content, keyword, semanticData)
    };
  }

  // Analyze mobile SEO
  analyzeMobileSEO(content) {
    return {
      responsiveness: this.analyzeResponsiveness(),
      touchOptimization: this.analyzeTouchOptimization(),
      loadSpeed: this.analyzeMobileLoadSpeed(),
      usability: this.analyzeMobileUsability(),
      amp: this.analyzeAMPCompatibility(),
      score: this.calculateMobileSEOScore()
    };
  }

  // Analyze local SEO
  analyzeLocalSEO(content, keyword) {
    return {
      locationMentions: this.analyzeLocationMentions(content),
      localKeywords: this.analyzeLocalKeywords(content, keyword),
      businessInfo: this.analyzeBusinessInfo(content),
      localSchema: this.analyzeLocalSchema(content),
      score: this.calculateLocalSEOScore(content)
    };
  }

  // Analyze SERP features
  async analyzeSERPFeatures(keyword, location = 'United States') {
    try {
      // Mock SERP analysis - in production, use real SERP API
      return {
        featuredSnippet: {
          present: Math.random() > 0.5,
          type: 'paragraph', // paragraph, list, table
          content: 'Sample featured snippet content',
          optimization: this.generateFeaturedSnippetOptimization(keyword)
        },
        peopleAlsoAsk: {
          present: true,
          questions: [
            `What is ${keyword}?`,
            `How does ${keyword} work?`,
            `Benefits of ${keyword}`,
            `${keyword} vs alternatives`
          ]
        },
        relatedSearches: [
          `${keyword} guide`,
          `${keyword} tips`,
          `best ${keyword}`,
          `${keyword} examples`
        ],
        knowledgePanel: {
          present: Math.random() > 0.7,
          type: 'entity'
        },
        localPack: {
          present: this.isLocalQuery(keyword),
          businesses: this.isLocalQuery(keyword) ? 3 : 0
        },
        images: {
          present: true,
          count: 8
        },
        videos: {
          present: Math.random() > 0.3,
          count: Math.random() > 0.5 ? 3 : 0
        },
        shoppingResults: {
          present: this.isCommercialQuery(keyword),
          count: this.isCommercialQuery(keyword) ? 4 : 0
        },
        searchIntent: this.analyzeSearchIntent(keyword),
        competitorAnalysis: await this.analyzeTopCompetitors(keyword)
      };
    } catch (error) {
      console.error('SERP analysis error:', error);
      return this.getMockSERPData(keyword);
    }
  }

  // Optimize for multiple search engines
  async optimizeForMultipleEngines(content, keyword, currentAnalysis, serpAnalysis) {
    let optimizedContent = content;
    const optimizations = {};

    // Google optimization
    const googleOptimized = this.optimizeForGoogle(optimizedContent, keyword, currentAnalysis, serpAnalysis);
    optimizedContent = googleOptimized.content;
    optimizations.google = googleOptimized.optimizations;

    // Bing optimization
    const bingOptimized = this.optimizeForBing(optimizedContent, keyword, currentAnalysis);
    optimizedContent = bingOptimized.content;
    optimizations.bing = bingOptimized.optimizations;

    // Other search engines
    const otherEnginesOptimized = this.optimizeForOtherEngines(optimizedContent, keyword, currentAnalysis);
    optimizedContent = otherEnginesOptimized.content;
    optimizations.otherEngines = otherEnginesOptimized.optimizations;

    return {
      content: optimizedContent,
      optimization: {
        enginesOptimized: ['google', 'bing', 'yahoo', 'duckduckgo', 'yandex'],
        optimizations,
        compatibility: this.calculateEngineCompatibility(optimizations)
      }
    };
  }

  // Optimize for Google
  optimizeForGoogle(content, keyword, analysis, serpAnalysis) {
    let optimized = content;
    const optimizations = [];

    // Optimize for helpful content update
    if (analysis.contentSEO.score < 80) {
      optimized = this.enhanceContentHelpfulness(optimized, keyword);
      optimizations.push('helpful_content_enhancement');
    }

    // Optimize for E-E-A-T (if not already done)
    optimized = this.enhanceGoogleEEAT(optimized, keyword);
    optimizations.push('eeat_optimization');

    // Optimize for featured snippets
    if (serpAnalysis.featuredSnippet.present) {
      optimized = this.optimizeForFeaturedSnippet(optimized, keyword, serpAnalysis.featuredSnippet);
      optimizations.push('featured_snippet_optimization');
    }

    // Optimize for People Also Ask
    if (serpAnalysis.peopleAlsoAsk.present) {
      optimized = this.optimizeForPeopleAlsoAsk(optimized, serpAnalysis.peopleAlsoAsk.questions);
      optimizations.push('people_also_ask_optimization');
    }

    // Optimize for entity SEO
    optimized = this.optimizeForEntitySEO(optimized, keyword);
    optimizations.push('entity_seo_optimization');

    return {
      content: optimized,
      optimizations
    };
  }

  // Optimize for Bing
  optimizeForBing(content, keyword, analysis) {
    let optimized = content;
    const optimizations = [];

    // Bing prefers more traditional SEO signals
    optimized = this.enhanceBingSignals(optimized, keyword);
    optimizations.push('traditional_seo_enhancement');

    // Optimize for Bing's multimedia preferences
    optimized = this.optimizeForMultimedia(optimized, keyword);
    optimizations.push('multimedia_optimization');

    // Enhance social signals context
    optimized = this.enhanceSocialContext(optimized, keyword);
    optimizations.push('social_signals_enhancement');

    return {
      content: optimized,
      optimizations
    };
  }

  // Optimize for other search engines
  optimizeForOtherEngines(content, keyword, analysis) {
    let optimized = content;
    const optimizations = [];

    // Privacy-focused optimization for DuckDuckGo
    optimized = this.optimizeForPrivacy(optimized, keyword);
    optimizations.push('privacy_optimization');

    // Regional optimization for Yandex
    optimized = this.optimizeForRegional(optimized, keyword);
    optimizations.push('regional_optimization');

    // Universal compatibility
    optimized = this.ensureUniversalCompatibility(optimized, keyword);
    optimizations.push('universal_compatibility');

    return {
      content: optimized,
      optimizations
    };
  }

  // Optimize technical SEO
  optimizeTechnicalSEO(content, keyword, analysis) {
    let optimized = content;
    const optimizations = [];

    // Optimize for Core Web Vitals
    const coreWebVitalsOptimized = this.optimizeForCoreWebVitals(optimized);
    optimized = coreWebVitalsOptimized.content;
    optimizations.push(...coreWebVitalsOptimized.optimizations);

    // Optimize structured data
    const structuredDataOptimized = this.optimizeStructuredData(optimized, keyword);
    optimized = structuredDataOptimized.content;
    optimizations.push(...structuredDataOptimized.optimizations);

    // Optimize crawlability
    const crawlabilityOptimized = this.optimizeCrawlability(optimized, keyword);
    optimized = crawlabilityOptimized.content;
    optimizations.push(...crawlabilityOptimized.optimizations);

    return {
      content: optimized,
      optimization: {
        optimizations,
        technicalScore: this.calculateTechnicalScore(optimized, keyword)
      }
    };
  }

  // Optimize for rich results
  optimizeForRichResults(content, keyword, serpAnalysis) {
    let optimized = content;
    const optimizations = [];

    // FAQ optimization
    const faqOptimized = this.optimizeForFAQ(optimized, keyword, serpAnalysis);
    optimized = faqOptimized.content;
    optimizations.push(...faqOptimized.optimizations);

    // How-to optimization
    const howToOptimized = this.optimizeForHowTo(optimized, keyword);
    optimized = howToOptimized.content;
    optimizations.push(...howToOptimized.optimizations);

    // Article schema optimization
    const articleOptimized = this.optimizeForArticleSchema(optimized, keyword);
    optimized = articleOptimized.content;
    optimizations.push(...articleOptimized.optimizations);

    // Breadcrumb optimization
    const breadcrumbOptimized = this.optimizeForBreadcrumbs(optimized, keyword);
    optimized = breadcrumbOptimized.content;
    optimizations.push(...breadcrumbOptimized.optimizations);

    return {
      content: optimized,
      optimization: {
        optimizations,
        richResultsPotential: this.calculateRichResultsPotential(optimized, keyword)
      }
    };
  }

  // Optimize for voice search
  optimizeForVoiceSearch(content, keyword) {
    let optimized = content;
    const optimizations = [];

    // Add conversational language
    optimized = this.addConversationalLanguage(optimized, keyword);
    optimizations.push('conversational_language');

    // Optimize for question-based queries
    optimized = this.optimizeForQuestions(optimized, keyword);
    optimizations.push('question_optimization');

    // Add local context for voice searches
    optimized = this.addLocalContext(optimized, keyword);
    optimizations.push('local_context');

    // Optimize for featured snippet format
    optimized = this.optimizeAnswerFormat(optimized, keyword);
    optimizations.push('answer_format');

    return {
      content: optimized,
      optimization: {
        optimizations,
        voiceSearchReadiness: this.calculateVoiceSearchReadiness(optimized, keyword)
      }
    };
  }

  // Optimize for mobile
  optimizeForMobile(content, keyword) {
    let optimized = content;
    const optimizations = [];

    // Optimize content structure for mobile
    optimized = this.optimizeMobileStructure(optimized);
    optimizations.push('mobile_structure');

    // Optimize reading experience
    optimized = this.optimizeMobileReadability(optimized);
    optimizations.push('mobile_readability');

    // Add mobile-specific call-to-actions
    optimized = this.addMobileCTA(optimized, keyword);
    optimizations.push('mobile_cta');

    return {
      content: optimized,
      optimization: {
        optimizations,
        mobileScore: this.calculateMobileScore(optimized)
      }
    };
  }

  // Perform SEO validation
  async performSEOValidation(content, keyword, serpAnalysis) {
    // Re-analyze SEO signals
    const finalAnalysis = await this.analyzeSEOSignals(content, keyword, null);

    // Calculate search engine compatibility
    const searchEngineCompatibility = this.calculateSearchEngineCompatibility(finalAnalysis);

    // Calculate ranking potential
    const rankingPotential = this.calculateRankingPotential(finalAnalysis, serpAnalysis);

    // Calculate featured snippet potential
    const featuredSnippetPotential = this.calculateFeaturedSnippetPotential(content, keyword, serpAnalysis);

    // Generate recommendations
    const recommendations = this.generateSEORecommendations(finalAnalysis);

    return {
      overallSEOScore: finalAnalysis.overallSEOScore,
      searchEngineCompatibility,
      rankingPotential,
      featuredSnippetPotential,
      recommendations,
      detailedAnalysis: finalAnalysis,
      passed: finalAnalysis.overallSEOScore >= 85
    };
  }

  // Helper methods for analysis

  getKeywordPosition(text, keyword) {
    const position = text.toLowerCase().indexOf(keyword.toLowerCase());
    const length = text.length;
    
    if (position === -1) return 'none';
    if (position < length * 0.2) return 'beginning';
    if (position > length * 0.8) return 'end';
    return 'middle';
  }

  hasModifiers(text) {
    return this.seoRules.titleOptimization.modifiers.some(modifier => 
      text.toLowerCase().includes(modifier)
    );
  }

  hasCallToAction(text) {
    return this.seoRules.metaDescription.actionWords.some(word => 
      text.toLowerCase().includes(word)
    );
  }

  checkHeadingHierarchy(content) {
    // Simplified hierarchy check
    const headings = content.match(/^#{1,6}\s+.+$/gm) || [];
    let currentLevel = 0;
    let hierarchyValid = true;

    headings.forEach(heading => {
      const level = heading.match(/^#{1,6}/)[0].length;
      if (level > currentLevel + 1) {
        hierarchyValid = false;
      }
      currentLevel = level;
    });

    return {
      valid: hierarchyValid,
      issues: hierarchyValid ? [] : ['Heading hierarchy skips levels']
    };
  }

  calculateHeadingScore(h1s, h2s, h3s, keyword) {
    let score = 0;
    const keywordLower = keyword.toLowerCase();

    // H1 score
    if (h1s.length === 1) score += 25;
    if (h1s.some(h => h.toLowerCase().includes(keywordLower))) score += 15;

    // H2 score
    if (h2s.length >= 3) score += 20;
    const h2WithKeyword = h2s.filter(h => h.toLowerCase().includes(keywordLower)).length;
    score += Math.min(20, (h2WithKeyword / h2s.length) * 20);

    // H3 score
    if (h3s.length >= 5) score += 10;
    const h3WithKeyword = h3s.filter(h => h.toLowerCase().includes(keywordLower)).length;
    score += Math.min(10, (h3WithKeyword / h3s.length) * 10);

    return Math.round(score);
  }

  analyzeKeywordDistribution(content, keyword) {
    const paragraphs = content.split(/\n\s*\n/);
    const distribution = paragraphs.map((paragraph, index) => {
      const matches = (paragraph.toLowerCase().match(new RegExp(keyword.toLowerCase(), 'g')) || []).length;
      return {
        paragraph: index + 1,
        matches,
        hasKeyword: matches > 0
      };
    });

    const paragraphsWithKeyword = distribution.filter(p => p.hasKeyword).length;
    const evenDistribution = paragraphsWithKeyword / paragraphs.length;

    return {
      paragraphs: distribution,
      evenlyDistributed: evenDistribution >= 0.3,
      coverage: evenDistribution
    };
  }

  analyzeKeywordVariations(content, keyword) {
    const variations = [
      keyword,
      keyword + 's',
      keyword + 'ing',
      keyword.replace(/ /g, '-'),
      keyword.replace(/ /g, '_')
    ];

    const found = variations.filter(variation => 
      content.toLowerCase().includes(variation.toLowerCase())
    );

    return {
      variations: found,
      count: found.length,
      hasVariations: found.length > 1
    };
  }

  detectKeywordStuffing(content, keyword) {
    const sentences = content.split(/[.!?]+/);
    const stuffedSentences = sentences.filter(sentence => {
      const matches = (sentence.toLowerCase().match(new RegExp(keyword.toLowerCase(), 'g')) || []).length;
      const words = this.tokenizer.tokenize(sentence).length;
      return words > 0 && (matches / words) > 0.1; // More than 10% keyword density in a sentence
    });

    return stuffedSentences.length > sentences.length * 0.2; // More than 20% of sentences are stuffed
  }

  assessNaturalUsage(content, keyword) {
    const sentences = content.split(/[.!?]+/);
    const keywordSentences = sentences.filter(s => s.toLowerCase().includes(keyword.toLowerCase()));
    
    let naturalCount = 0;
    keywordSentences.forEach(sentence => {
      // Check if keyword appears in natural contexts
      const naturalPhrases = [
        `what is ${keyword}`,
        `how ${keyword} works`,
        `benefits of ${keyword}`,
        `${keyword} helps`,
        `using ${keyword}`
      ];
      
      if (naturalPhrases.some(phrase => sentence.toLowerCase().includes(phrase.toLowerCase()))) {
        naturalCount++;
      }
    });

    return {
      naturalUsage: keywordSentences.length > 0 ? naturalCount / keywordSentences.length : 0,
      isNatural: naturalCount / keywordSentences.length > 0.3
    };
  }

  analyzeKeywordProximity(content, keyword) {
    const keywordParts = keyword.split(' ');
    if (keywordParts.length < 2) return { proximity: 1, optimal: true };

    const words = this.tokenizer.tokenize(content.toLowerCase());
    let totalDistance = 0;
    let proximityCount = 0;

    for (let i = 0; i < words.length - 1; i++) {
      if (keywordParts.includes(words[i])) {
        for (let j = i + 1; j < Math.min(i + 6, words.length); j++) {
          if (keywordParts.includes(words[j]) && words[j] !== words[i]) {
            totalDistance += j - i;
            proximityCount++;
          }
        }
      }
    }

    const averageDistance = proximityCount > 0 ? totalDistance / proximityCount : 6;
    
    return {
      proximity: Math.max(0, 1 - (averageDistance - 1) / 5),
      optimal: averageDistance <= 3
    };
  }

  hasVariedAnchorTexts(links) {
    const anchorTexts = links.map(link => link[1].toLowerCase());
    const uniqueAnchors = new Set(anchorTexts);
    return uniqueAnchors.size > anchorTexts.length * 0.7;
  }

  // Simplified analysis methods
  analyzePageSpeed() {
    return { score: 85, lcp: 2.1, fid: 95, cls: 0.08 };
  }

  analyzeMobileOptimization() {
    return { score: 90, responsive: true, touchOptimized: true };
  }

  analyzeStructuredData(content) {
    return { hasSchema: false, types: [], score: 60 };
  }

  analyzeCrawlability() {
    return { score: 85, robotsTxt: true, sitemap: true };
  }

  analyzeHTTPS() {
    return { secure: true, score: 100 };
  }

  analyzeCoreWebVitals() {
    return { lcp: 2.1, fid: 95, cls: 0.08, score: 85 };
  }

  analyzeReadability(content) {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = this.tokenizer.tokenize(content) || [];
    const avgWordsPerSentence = words.length / sentences.length;
    
    return {
      fleschScore: Math.max(0, 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * 1.5)),
      gradeLevel: Math.max(0, (0.39 * avgWordsPerSentence) + (11.8 * 1.5) - 15.59),
      avgWordsPerSentence
    };
  }

  analyzeComprehensiveness(content, keyword) {
    const words = this.tokenizer.tokenize(content).length;
    const topics = this.countTopicalCoverage(content, keyword);
    
    return {
      wordCount: words,
      topicsCovered: topics,
      isComprehensive: words >= 1000 && topics >= 5,
      score: Math.min(100, (words / 20) + (topics * 10))
    };
  }

  countTopicalCoverage(content, keyword) {
    const topics = [
      'definition', 'benefits', 'how to', 'examples', 'comparison',
      'best practices', 'tips', 'guide', 'implementation', 'features'
    ];
    
    return topics.filter(topic => 
      content.toLowerCase().includes(topic) || 
      content.toLowerCase().includes(`${keyword} ${topic}`)
    ).length;
  }

  analyzeOriginality(content) {
    // Simplified originality check
    const uniquePhrases = new Set();
    const sentences = content.split(/[.!?]+/);
    
    sentences.forEach(sentence => {
      if (sentence.trim().length > 20) {
        uniquePhrases.add(sentence.trim().toLowerCase());
      }
    });

    return {
      uniqueContent: uniquePhrases.size / sentences.length,
      score: Math.min(100, (uniquePhrases.size / sentences.length) * 100)
    };
  }

  analyzeTopicalRelevance(content, keyword, semanticData) {
    if (!semanticData) return { score: 70 };

    let relevanceScore = 0;
    const contentLower = content.toLowerCase();

    // Check for semantic keywords
    if (semanticData.headingOptimizedTerms) {
      const semanticMatches = semanticData.headingOptimizedTerms.filter(term => 
        contentLower.includes(term.toLowerCase())
      ).length;
      relevanceScore += (semanticMatches / semanticData.headingOptimizedTerms.length) * 50;
    }

    // Check for entities
    if (semanticData.prioritizedEntities) {
      const entityMatches = semanticData.prioritizedEntities.filter(entity => 
        contentLower.includes(entity.term.toLowerCase())
      ).length;
      relevanceScore += (entityMatches / semanticData.prioritizedEntities.length) * 50;
    }

    return {
      score: Math.min(100, relevanceScore),
      semanticAlignment: relevanceScore > 70
    };
  }

  analyzeEntityCoverage(content, semanticData) {
    if (!semanticData || !semanticData.prioritizedEntities) {
      return { coverage: 0, entities: [] };
    }

    const contentLower = content.toLowerCase();
    const coveredEntities = semanticData.prioritizedEntities.filter(entity => 
      contentLower.includes(entity.term.toLowerCase())
    );

    return {
      coverage: coveredEntities.length / semanticData.prioritizedEntities.length,
      entities: coveredEntities,
      score: (coveredEntities.length / semanticData.prioritizedEntities.length) * 100
    };
  }

  analyzeFreshness(content) {
    // Check for current year, recent trends, etc.
    const currentYear = new Date().getFullYear();
    const hasFreshness = content.includes(currentYear.toString()) || 
                        content.includes('latest') || 
                        content.includes('recent') ||
                        content.includes('updated');

    return {
      hasFreshnessSignals: hasFreshness,
      score: hasFreshness ? 85 : 60
    };
  }

  // Calculate various scores
  calculateOnPageScore(analysis) {
    let score = 0;
    
    if (analysis.titleTag.isOptimized) score += 25;
    if (analysis.metaDescription.isOptimized) score += 15;
    if (analysis.headingStructure.h1.isOptimized) score += 20;
    if (analysis.headingStructure.h2.isOptimized) score += 15;
    if (analysis.keywordUsage.isOptimized) score += 25;

    return Math.round(score);
  }

  calculateContentSEOScore(content, keyword, semanticData) {
    const readability = this.analyzeReadability(content);
    const comprehensiveness = this.analyzeComprehensiveness(content, keyword);
    const originality = this.analyzeOriginality(content);
    const topicalRelevance = this.analyzeTopicalRelevance(content, keyword, semanticData);

    return Math.round((
      readability.fleschScore * 0.2 +
      comprehensiveness.score * 0.3 +
      originality.score * 0.2 +
      topicalRelevance.score * 0.3
    ));
  }

  calculateUXScore(content) {
    // Simplified UX scoring
    const sentences = content.split(/[.!?]+/).length;
    const paragraphs = content.split(/\n\s*\n/).length;
    const avgSentencesPerParagraph = sentences / paragraphs;

    let score = 70; // Base score

    // Good paragraph structure
    if (avgSentencesPerParagraph >= 3 && avgSentencesPerParagraph <= 6) score += 15;
    
    // Has headings for navigation
    const headingCount = (content.match(/^#{1,6}\s+/gm) || []).length;
    if (headingCount >= 5) score += 15;

    return Math.round(score);
  }

  calculateSemanticSEOScore(content, keyword, semanticData) {
    const entityCoverage = this.analyzeEntityCoverage(content, semanticData);
    const topicalRelevance = this.analyzeTopicalRelevance(content, keyword, semanticData);
    
    return Math.round((entityCoverage.score * 0.4 + topicalRelevance.score * 0.6));
  }

  calculateMobileSEOScore() {
    // Simplified mobile SEO score
    return 85;
  }

  calculateLocalSEOScore(content) {
    // Simplified local SEO score
    return 75;
  }

  calculateOverallSEOScore(analysis) {
    const weights = {
      onPageSEO: 0.25,
      technicalSEO: 0.20,
      contentSEO: 0.25,
      userExperienceSEO: 0.15,
      semanticSEO: 0.15
    };

    return Math.round(
      analysis.onPageSEO.score * weights.onPageSEO +
      analysis.technicalSEO.score * weights.technicalSEO +
      analysis.contentSEO.score * weights.contentSEO +
      analysis.userExperienceSEO.score * weights.userExperienceSEO +
      analysis.semanticSEO.score * weights.semanticSEO
    );
  }

  // Search intent and SERP analysis
  analyzeSearchIntent(keyword) {
    const keywordLower = keyword.toLowerCase();
    
    // Informational intent
    if (keywordLower.includes('what') || keywordLower.includes('how') || 
        keywordLower.includes('why') || keywordLower.includes('guide')) {
      return 'informational';
    }
    
    // Commercial intent
    if (keywordLower.includes('best') || keywordLower.includes('top') || 
        keywordLower.includes('review') || keywordLower.includes('compare')) {
      return 'commercial';
    }
    
    // Transactional intent
    if (keywordLower.includes('buy') || keywordLower.includes('price') || 
        keywordLower.includes('cost') || keywordLower.includes('purchase')) {
      return 'transactional';
    }
    
    // Navigational intent
    if (keywordLower.includes('login') || keywordLower.includes('website') || 
        keywordLower.includes('official')) {
      return 'navigational';
    }

    return 'informational'; // Default
  }

  isLocalQuery(keyword) {
    const localIndicators = ['near me', 'in', 'local', 'nearby', 'around'];
    return localIndicators.some(indicator => keyword.toLowerCase().includes(indicator));
  }

  isCommercialQuery(keyword) {
    const commercialIndicators = ['buy', 'price', 'cost', 'cheap', 'deal', 'sale', 'shop'];
    return commercialIndicators.some(indicator => keyword.toLowerCase().includes(indicator));
  }

  async analyzeTopCompetitors(keyword) {
    // Mock competitor analysis
    return [
      { domain: 'competitor1.com', title: `Complete ${keyword} Guide`, position: 1 },
      { domain: 'competitor2.com', title: `Best ${keyword} Tips`, position: 2 },
      { domain: 'competitor3.com', title: `${keyword} Explained`, position: 3 }
    ];
  }

  getMockSERPData(keyword) {
    return {
      featuredSnippet: { present: false },
      peopleAlsoAsk: { present: true, questions: [] },
      relatedSearches: [],
      searchIntent: this.analyzeSearchIntent(keyword),
      competitorAnalysis: []
    };
  }

  // Content optimization methods
  enhanceContentHelpfulness(content, keyword) {
    // Add practical examples and actionable advice
    return content + `\n\n## Practical Implementation\n\nImplementing ${keyword} effectively requires following best practices and learning from real-world examples. Consider these actionable steps to get started.`;
  }

  enhanceGoogleEEAT(content, keyword) {
    // Enhance expertise signals
    return content + `\n\n*This comprehensive guide on ${keyword} is based on industry best practices and expert insights.*`;
  }

  optimizeForFeaturedSnippet(content, keyword, snippetData) {
    if (snippetData.type === 'paragraph') {
      const definition = `${keyword} is a comprehensive approach that provides significant benefits through systematic implementation.`;
      return definition + '\n\n' + content;
    }
    return content;
  }

  optimizeForPeopleAlsoAsk(content, questions) {
    let optimized = content;
    questions.forEach(question => {
      if (!content.toLowerCase().includes(question.toLowerCase())) {
        optimized += `\n\n### ${question}\n\nThis is an important question that many people have about this topic.`;
      }
    });
    return optimized;
  }

  optimizeForEntitySEO(content, keyword) {
    // Add entity context
    return content + `\n\n${keyword} is closely related to various industry concepts and best practices that enhance overall effectiveness.`;
  }

  enhanceBingSignals(content, keyword) {
    // Add more traditional SEO signals that Bing values
    return content.replace(/\n\n/g, `\n\n*${keyword} optimization continues to be important for achieving the best results.*\n\n`);
  }

  optimizeForMultimedia(content, keyword) {
    // Add multimedia context
    return content + `\n\n## Visual Examples\n\n[Include relevant images and videos about ${keyword} here]`;
  }

  enhanceSocialContext(content, keyword) {
    // Add social sharing context
    return content + `\n\n*Share your experience with ${keyword} on social media to help others learn.*`;
  }

  optimizeForPrivacy(content, keyword) {
    // Privacy-focused optimization
    return content.replace(/track|cookie|analytics/gi, 'privacy-focused');
  }

  optimizeForRegional(content, keyword) {
    // Regional optimization
    return content + `\n\n## Regional Considerations\n\nDifferent regions may have specific requirements for ${keyword} implementation.`;
  }

  ensureUniversalCompatibility(content, keyword) {
    // Ensure content works across all search engines
    return content.replace(/Google|Bing|Yahoo/gi, 'search engines');
  }

  // Technical optimization methods
  optimizeForCoreWebVitals(content) {
    return {
      content: content + '\n\n<!-- Optimized for Core Web Vitals -->',
      optimizations: ['lcp_optimization', 'fid_optimization', 'cls_optimization']
    };
  }

  optimizeStructuredData(content, keyword) {
    return {
      content: content + `\n\n<!-- JSON-LD Schema for ${keyword} -->`,
      optimizations: ['article_schema', 'breadcrumb_schema', 'faq_schema']
    };
  }

  optimizeCrawlability(content, keyword) {
    return {
      content: content + '\n\n<!-- Optimized internal linking structure -->',
      optimizations: ['internal_linking', 'url_structure', 'navigation']
    };
  }

  // Rich results optimization
  optimizeForFAQ(content, keyword, serpAnalysis) {
    const faqSection = `\n\n## Frequently Asked Questions\n\n### What is ${keyword}?\n${keyword} is a comprehensive solution that provides significant benefits.\n\n### How does ${keyword} work?\n${keyword} works through systematic implementation of best practices.`;
    
    return {
      content: content + faqSection,
      optimizations: ['faq_schema', 'question_optimization']
    };
  }

  optimizeForHowTo(content, keyword) {
    const howToSection = `\n\n## How to Implement ${keyword}\n\n1. First, understand the basics\n2. Then, plan your approach\n3. Finally, execute systematically`;
    
    return {
      content: content + howToSection,
      optimizations: ['howto_schema', 'step_by_step']
    };
  }

  optimizeForArticleSchema(content, keyword) {
    return {
      content: content + `\n\n<!-- Article Schema for ${keyword} -->`,
      optimizations: ['article_schema']
    };
  }

  optimizeForBreadcrumbs(content, keyword) {
    return {
      content: content + '\n\n<!-- Breadcrumb navigation optimized -->',
      optimizations: ['breadcrumb_schema']
    };
  }

  // Voice search optimization
  addConversationalLanguage(content, keyword) {
    return content.replace(/\. /g, '. You might wonder ').replace(/ is /g, ' is exactly ');
  }

  optimizeForQuestions(content, keyword) {
    return content + `\n\n## Common Questions About ${keyword}\n\nPeople often ask about ${keyword} and how it can benefit them.`;
  }

  addLocalContext(content, keyword) {
    return content + `\n\n${keyword} is available in most locations and can be implemented locally.`;
  }

  optimizeAnswerFormat(content, keyword) {
    return `${keyword} is best understood as a comprehensive approach that delivers results.\n\n` + content;
  }

  // Mobile optimization
  optimizeMobileStructure(content) {
    // Break long paragraphs for mobile
    return content.replace(/(.{200,}?)\. /g, '$1.\n\n');
  }

  optimizeMobileReadability(content) {
    // Shorter sentences for mobile
    return content.replace(/,\s+and\s+/g, '. Also, ');
  }

  addMobileCTA(content, keyword) {
    return content + `\n\n## Ready to Get Started with ${keyword}?\n\nTake action today and see immediate results.`;
  }

  // Calculation methods
  calculateEngineCompatibility(optimizations) {
    const engines = Object.keys(optimizations);
    return {
      compatible: engines,
      score: engines.length * 20,
      universal: engines.length >= 5
    };
  }

  calculateTechnicalScore(content, keyword) {
    return 85; // Simplified scoring
  }

  calculateRichResultsPotential(content, keyword) {
    let potential = 0;
    
    if (content.includes('FAQ') || content.includes('Questions')) potential += 25;
    if (content.includes('How to') || content.includes('Steps')) potential += 25;
    if (content.includes('### ') || content.includes('## ')) potential += 25;
    if (content.length > 1000) potential += 25;

    return Math.min(100, potential);
  }

  calculateVoiceSearchReadiness(content, keyword) {
    let readiness = 0;
    
    if (content.includes('?')) readiness += 25;
    if (content.includes('how') || content.includes('what')) readiness += 25;
    if (content.includes('you')) readiness += 25;
    if (content.includes(keyword)) readiness += 25;

    return Math.min(100, readiness);
  }

  calculateMobileScore(content) {
    const paragraphs = content.split(/\n\s*\n/);
    const avgParagraphLength = paragraphs.reduce((sum, p) => sum + p.length, 0) / paragraphs.length;
    
    let score = 70;
    if (avgParagraphLength < 300) score += 15;
    if (content.includes('##')) score += 15;
    
    return Math.min(100, score);
  }

  calculateSearchEngineCompatibility(analysis) {
    return {
      google: analysis.overallSEOScore,
      bing: analysis.overallSEOScore * 0.9,
      yahoo: analysis.overallSEOScore * 0.85,
      duckduckgo: analysis.overallSEOScore * 0.8,
      yandex: analysis.overallSEOScore * 0.85,
      average: analysis.overallSEOScore * 0.87
    };
  }

  calculateRankingPotential(analysis, serpAnalysis) {
    let potential = analysis.overallSEOScore;
    
    if (serpAnalysis.searchIntent === 'informational' && analysis.contentSEO.score > 80) {
      potential += 10;
    }
    
    return Math.min(100, potential);
  }

  calculateFeaturedSnippetPotential(content, keyword, serpAnalysis) {
    if (!serpAnalysis.featuredSnippet.present) return 0;
    
    let potential = 0;
    
    if (content.includes(`${keyword} is`)) potential += 30;
    if (content.includes('###') || content.includes('##')) potential += 20;
    if (content.includes('1.') || content.includes('•')) potential += 25;
    if (content.length > 300 && content.length < 2000) potential += 25;

    return Math.min(100, potential);
  }

  generateSEORecommendations(analysis) {
    const recommendations = [];
    
    if (analysis.onPageSEO.score < 80) {
      recommendations.push({
        type: 'on_page',
        priority: 'high',
        message: 'Improve title tags, meta descriptions, and heading structure'
      });
    }
    
    if (analysis.technicalSEO.score < 80) {
      recommendations.push({
        type: 'technical',
        priority: 'high',
        message: 'Optimize page speed and Core Web Vitals'
      });
    }
    
    if (analysis.contentSEO.score < 80) {
      recommendations.push({
        type: 'content',
        priority: 'medium',
        message: 'Enhance content comprehensiveness and topical coverage'
      });
    }

    return recommendations;
  }

  // Placeholder methods for additional analysis
  analyzeEngagementSignals(content) {
    return { score: 75, signals: ['time_on_page', 'scroll_depth'] };
  }

  analyzeNavigation(content) {
    return { score: 80, hasHeadings: true, hasLinks: true };
  }

  analyzeAccessibility(content) {
    return { score: 85, hasHeadings: true, hasAltText: false };
  }

  analyzeInteractivity(content) {
    return { score: 70, hasCTA: true, hasMultimedia: false };
  }

  analyzeVisualAppeal(content) {
    return { score: 75, hasHeadings: true, hasLists: content.includes('•') };
  }

  analyzeEntityMentions(content, semanticData) {
    if (!semanticData) return { score: 60, mentions: [] };
    return { score: 80, mentions: semanticData.prioritizedEntities || [] };
  }

  analyzeTopicalClusters(content, keyword) {
    return { score: 75, clusters: ['primary', 'related'] };
  }

  analyzeSemanticKeywords(content, semanticData) {
    if (!semanticData) return { score: 60, keywords: [] };
    return { score: 85, keywords: semanticData.headingOptimizedTerms || [] };
  }

  analyzeContextualRelevance(content, keyword) {
    return { score: 80, relevant: content.toLowerCase().includes(keyword.toLowerCase()) };
  }

  analyzeIntentAlignment(content, keyword) {
    const intent = this.analyzeSearchIntent(keyword);
    return { score: 85, intent, aligned: true };
  }

  analyzeKnowledgeGraphSignals(content, keyword) {
    return { score: 70, signals: ['entity_mentions', 'structured_data'] };
  }

  analyzeResponsiveness() {
    return { score: 90, responsive: true };
  }

  analyzeTouchOptimization() {
    return { score: 85, touchOptimized: true };
  }

  analyzeMobileLoadSpeed() {
    return { score: 80, loadTime: 2.5 };
  }

  analyzeMobileUsability() {
    return { score: 85, usable: true };
  }

  analyzeAMPCompatibility() {
    return { score: 70, ampReady: false };
  }

  analyzeLocationMentions(content) {
    return { score: 60, mentions: [] };
  }

  analyzeLocalKeywords(content, keyword) {
    return { score: 65, localVariations: [] };
  }

  analyzeBusinessInfo(content) {
    return { score: 70, hasInfo: false };
  }

  analyzeLocalSchema(content) {
    return { score: 60, hasSchema: false };
  }

  generateFeaturedSnippetOptimization(keyword) {
    return {
      targetFormat: 'paragraph',
      suggestedContent: `${keyword} is a comprehensive approach that provides significant benefits through systematic implementation.`,
      wordCount: 50
    };
  }
}

module.exports = AdvancedSEOOptimizer;