-- Enhanced Database Schema for SEO Content Generation SAAS
-- Execute this in Supabase SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Profiles table (extends auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'enterprise')),
  subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'past_due', 'trialing')),
  api_usage_limit INTEGER DEFAULT 100,
  total_content_generated INTEGER DEFAULT 0,
  total_api_calls INTEGER DEFAULT 0,
  onboarding_completed BOOLEAN DEFAULT false,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Projects table
CREATE TABLE projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  industry TEXT NOT NULL,
  target_country TEXT NOT NULL DEFAULT 'US',
  target_language TEXT NOT NULL DEFAULT 'en',
  website_url TEXT,
  competitor_urls TEXT[],
  brand_voice TEXT,
  target_audience TEXT,
  settings JSONB DEFAULT '{}',
  is_archived BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content generations table (ENHANCED)
CREATE TABLE content_generations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  
  -- Input parameters
  keyword TEXT NOT NULL,
  location TEXT NOT NULL,
  industry TEXT NOT NULL,
  content_type TEXT NOT NULL CHECK (content_type IN ('service', 'blog', 'product', 'landing', 'category', 'faq')),
  tone TEXT NOT NULL CHECK (tone IN ('professional', 'conversational', 'authoritative', 'friendly', 'technical', 'casual')),
  intent TEXT NOT NULL CHECK (intent IN ('informational', 'commercial', 'transactional', 'navigational')),
  target_word_count INTEGER DEFAULT 1000,
  include_images BOOLEAN DEFAULT false,
  include_schema BOOLEAN DEFAULT false,
  include_faq BOOLEAN DEFAULT false,
  
  -- Generation results
  generated_title TEXT,
  generated_meta_description TEXT,
  generated_content TEXT,
  content_outline JSONB,
  seo_analysis JSONB,
  competitor_data JSONB,
  quality_score DECIMAL(3,2),
  seo_score DECIMAL(3,2),
  readability_score DECIMAL(3,2),
  
  -- Content structure analysis
  word_count INTEGER,
  heading_count JSONB, -- {h1: 1, h2: 5, h3: 8, etc}
  keyword_density DECIMAL(4,2),
  lsi_keywords TEXT[],
  entities TEXT[],
  internal_links_suggested INTEGER DEFAULT 0,
  external_links_suggested INTEGER DEFAULT 0,
  
  -- Performance metrics
  generation_time_ms INTEGER,
  tokens_used INTEGER,
  api_cost DECIMAL(10,4),
  
  -- Status and tracking
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  error_message TEXT,
  is_exported BOOLEAN DEFAULT false,
  export_formats TEXT[], -- ['html', 'markdown', 'docx']
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API usage logs (CRITICAL FOR MONITORING)
CREATE TABLE api_usage_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  content_generation_id UUID REFERENCES content_generations(id) ON DELETE CASCADE,
  api_name TEXT NOT NULL CHECK (api_name IN ('groq', 'serper', 'supabase', 'openai')),
  endpoint TEXT,
  method TEXT,
  request_data JSONB,
  response_data JSONB,
  tokens_used INTEGER DEFAULT 0,
  cost DECIMAL(10,4) DEFAULT 0,
  response_time_ms INTEGER,
  status_code INTEGER,
  error_message TEXT,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User subscriptions (CRITICAL FOR BILLING)
CREATE TABLE user_subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE UNIQUE,
  plan_type TEXT NOT NULL CHECK (plan_type IN ('free', 'pro', 'enterprise')),
  status TEXT NOT NULL CHECK (status IN ('active', 'cancelled', 'past_due', 'trialing', 'incomplete')),
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  stripe_customer_id TEXT UNIQUE,
  stripe_subscription_id TEXT UNIQUE,
  stripe_price_id TEXT,
  cancel_at_period_end BOOLEAN DEFAULT false,
  cancelled_at TIMESTAMP WITH TIME ZONE,
  billing_cycle_anchor TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content templates (FOR INDUSTRY TEMPLATES)
CREATE TABLE content_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  industry TEXT NOT NULL,
  content_type TEXT NOT NULL,
  template_structure JSONB NOT NULL,
  prompt_template TEXT NOT NULL,
  example_output TEXT,
  tags TEXT[],
  is_active BOOLEAN DEFAULT true,
  is_premium BOOLEAN DEFAULT false,
  usage_count INTEGER DEFAULT 0,
  created_by UUID REFERENCES profiles(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SEO analyses (ENHANCED)
CREATE TABLE seo_analyses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content_generation_id UUID REFERENCES content_generations(id) ON DELETE CASCADE,
  keyword TEXT NOT NULL,
  location TEXT NOT NULL,
  search_volume INTEGER,
  keyword_difficulty INTEGER, -- 1-100
  competition_level TEXT CHECK (competition_level IN ('low', 'medium', 'high')),
  competitors JSONB NOT NULL, -- Array of competitor analysis data
  averages JSONB NOT NULL, -- Average metrics across competitors
  recommendations JSONB NOT NULL, -- Optimization recommendations
  technical_seo JSONB, -- Technical SEO analysis
  content_gaps JSONB, -- Missing topics/keywords
  topic_clusters JSONB, -- Related topic clusters
  user_intent_analysis JSONB, -- Intent classification and analysis
  optimization_score DECIMAL(3,2),
  opportunity_score DECIMAL(3,2), -- 1-100 representing opportunity
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Competitor analysis cache (NEW - FOR PERFORMANCE)
CREATE TABLE competitor_cache (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  cache_key TEXT UNIQUE NOT NULL, -- Hash of keyword + location + other params
  keyword TEXT NOT NULL,
  location TEXT NOT NULL,
  industry TEXT,
  competitor_data JSONB NOT NULL,
  serp_data JSONB NOT NULL,
  analysis_metadata JSONB,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User activity logs (FOR ANALYTICS)
CREATE TABLE user_activity_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL, -- 'login', 'content_generated', 'export', 'settings_changed'
  action_details JSONB,
  ip_address INET,
  user_agent TEXT,
  session_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content exports (TRACK EXPORTS)
CREATE TABLE content_exports (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  content_generation_id UUID REFERENCES content_generations(id) ON DELETE CASCADE NOT NULL,
  export_format TEXT NOT NULL CHECK (export_format IN ('html', 'markdown', 'docx', 'pdf', 'json')),
  export_settings JSONB,
  file_url TEXT,
  file_size_bytes INTEGER,
  download_count INTEGER DEFAULT 0,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Feedback and ratings (USER FEEDBACK)
CREATE TABLE content_feedback (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  content_generation_id UUID REFERENCES content_generations(id) ON DELETE CASCADE NOT NULL,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  feedback_text TEXT,
  feedback_categories TEXT[], -- ['accuracy', 'relevance', 'tone', 'structure']
  is_helpful BOOLEAN,
  improvement_suggestions TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance indexes (CRITICAL FOR SPEED)
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_profiles_subscription ON profiles(subscription_tier, subscription_status);
CREATE INDEX idx_projects_user_id ON projects(user_id) WHERE is_archived = false;
CREATE INDEX idx_projects_industry ON projects(industry);
CREATE INDEX idx_content_generations_user_project ON content_generations(user_id, project_id);
CREATE INDEX idx_content_generations_keyword ON content_generations(keyword);
CREATE INDEX idx_content_generations_status ON content_generations(status);
CREATE INDEX idx_content_generations_created_at ON content_generations(created_at DESC);
CREATE INDEX idx_api_usage_logs_user_date ON api_usage_logs(user_id, created_at DESC);
CREATE INDEX idx_api_usage_logs_api_name ON api_usage_logs(api_name, created_at DESC);
CREATE INDEX idx_seo_analyses_keyword ON seo_analyses(keyword, location);
CREATE INDEX idx_competitor_cache_key ON competitor_cache(cache_key);
CREATE INDEX idx_competitor_cache_expires ON competitor_cache(expires_at);
CREATE INDEX idx_user_activity_logs_user_date ON user_activity_logs(user_id, created_at DESC);
CREATE INDEX idx_content_exports_user ON content_exports(user_id, created_at DESC);
CREATE INDEX idx_content_feedback_content ON content_feedback(content_generation_id);

-- Full-text search indexes
CREATE INDEX idx_content_generations_fts ON content_generations USING gin(to_tsvector('english', generated_content || ' ' || generated_title));
CREATE INDEX idx_projects_fts ON projects USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- Row Level Security (RLS) - MANDATORY FOR SECURITY
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_generations ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE seo_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE competitor_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_exports ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_feedback ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Projects policies
CREATE POLICY "Users can manage own projects" ON projects FOR ALL USING (auth.uid() = user_id);

-- Content generations policies
CREATE POLICY "Users can manage own content" ON content_generations FOR ALL USING (auth.uid() = user_id);

-- API usage logs policies
CREATE POLICY "Users can view own usage" ON api_usage_logs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can insert usage logs" ON api_usage_logs FOR INSERT WITH CHECK (true);

-- User subscriptions policies
CREATE POLICY "Users can view own subscription" ON user_subscriptions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own subscription" ON user_subscriptions FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "System can manage subscriptions" ON user_subscriptions FOR INSERT WITH CHECK (true);

-- SEO analyses policies
CREATE POLICY "Users can view related SEO analyses" ON seo_analyses FOR SELECT USING (
  EXISTS (SELECT 1 FROM content_generations WHERE id = content_generation_id AND user_id = auth.uid())
);
CREATE POLICY "System can insert SEO analyses" ON seo_analyses FOR INSERT WITH CHECK (true);

-- Competitor cache policies (read-only for users)
CREATE POLICY "Users can read competitor cache" ON competitor_cache FOR SELECT USING (true);
CREATE POLICY "System can manage competitor cache" ON competitor_cache FOR ALL USING (true);

-- User activity logs policies
CREATE POLICY "Users can view own activity" ON user_activity_logs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can insert activity logs" ON user_activity_logs FOR INSERT WITH CHECK (true);

-- Content exports policies
CREATE POLICY "Users can manage own exports" ON content_exports FOR ALL USING (auth.uid() = user_id);

-- Content feedback policies
CREATE POLICY "Users can manage own feedback" ON content_feedback FOR ALL USING (auth.uid() = user_id);

-- Content templates policies (read for all, admin manage)
CREATE POLICY "All users can read active templates" ON content_templates FOR SELECT USING (is_active = true);

-- Functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_generations_updated_at BEFORE UPDATE ON content_generations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_subscriptions_updated_at BEFORE UPDATE ON user_subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_templates_updated_at BEFORE UPDATE ON content_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to handle user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to increment usage counters
CREATE OR REPLACE FUNCTION increment_content_generated()
RETURNS trigger AS $$
BEGIN
  IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
    UPDATE profiles 
    SET total_content_generated = total_content_generated + 1 
    WHERE id = NEW.user_id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for content generation completion
CREATE TRIGGER on_content_completed
  AFTER UPDATE ON content_generations
  FOR EACH ROW EXECUTE FUNCTION increment_content_generated();

-- Function to increment API call counter
CREATE OR REPLACE FUNCTION increment_api_calls()
RETURNS trigger AS $$
BEGIN
  UPDATE profiles 
  SET total_api_calls = total_api_calls + 1 
  WHERE id = NEW.user_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for API usage tracking
CREATE TRIGGER on_api_call_logged
  AFTER INSERT ON api_usage_logs
  FOR EACH ROW EXECUTE FUNCTION increment_api_calls();

-- Function to clean expired cache
CREATE OR REPLACE FUNCTION clean_expired_cache()
RETURNS void AS $$
BEGIN
  DELETE FROM competitor_cache WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a scheduled job to clean cache (uncomment when ready)
-- SELECT cron.schedule('clean-cache', '0 */6 * * *', 'SELECT clean_expired_cache();');