const { createClient } = require('@supabase/supabase-js');
const archiver = require('archiver');
const fs = require('fs').promises;
const path = require('path');
const XLSX = require('xlsx');

class BulkExporter {
  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );
    
    // Export directory configuration
    this.exportDir = process.env.BULK_EXPORT_DIR || path.join(process.cwd(), 'exports');
    this.maxFileAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    
    // Ensure export directory exists
    this.ensureExportDirectory();
  }

  async ensureExportDirectory() {
    try {
      await fs.mkdir(this.exportDir, { recursive: true });
    } catch (error) {
      console.error('Error creating export directory:', error);
    }
  }

  // Create bulk export
  async createBulkExport(operationId, userId, exportFormat = 'zip') {
    try {
      // Verify operation belongs to user
      const { data: operation } = await this.supabase
        .from('bulk_operations')
        .select('*')
        .eq('id', operationId)
        .eq('user_id', userId)
        .single();
      
      if (!operation) {
        throw new Error('Operation not found or access denied');
      }
      
      if (operation.status !== 'completed') {
        throw new Error('Operation must be completed before export');
      }
      
      // Get all results for this operation
      const { data: results } = await this.supabase
        .from('bulk_results')
        .select(`
          *,
          bulk_keywords (
            keyword,
            input_data,
            status
          )
        `)
        .eq('bulk_operation_id', operationId)
        .order('created_at');
      
      if (!results || results.length === 0) {
        throw new Error('No results found for export');
      }
      
      let exportPath;
      let fileSize;
      
      // Generate export based on format
      switch (exportFormat.toLowerCase()) {
        case 'zip':
          { const result = await this.createZipExport(operation, results); exportPath = result.exportPath; fileSize = result.fileSize; }
          break;
        case 'csv':
          { const result = await this.createCSVExport(operation, results); exportPath = result.exportPath; fileSize = result.fileSize; }
          break;
        case 'excel':
          { const result = await this.createExcelExport(operation, results); exportPath = result.exportPath; fileSize = result.fileSize; }
          break;
        case 'json':
          { const result = await this.createJSONExport(operation, results); exportPath = result.exportPath; fileSize = result.fileSize; }
          break;
        default:
          throw new Error('Unsupported export format');
      }
      
      // Save export record
      const expiresAt = new Date();
      expiresAt.setTime(expiresAt.getTime() + this.maxFileAge);
      
      const { data: exportRecord, error } = await this.supabase
        .from('bulk_exports')
        .insert({
          bulk_operation_id: operationId,
          user_id: userId,
          export_format: exportFormat,
          file_path: exportPath,
          file_size: fileSize,
          expires_at: expiresAt.toISOString()
        })
        .select()
        .single();
      
      if (error) {
        throw new Error(`Failed to save export record: ${error.message}`);
      }
      
      return {
        success: true,
        exportId: exportRecord.id,
        filePath: exportPath,
        fileSize,
        expiresAt: expiresAt.toISOString()
      };
      
    } catch (error) {
      console.error('Error creating bulk export:', error);
      throw error;
    }
  }

  // Create ZIP export with multiple formats
  async createZipExport(operation, results) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `bulk-export-${operation.operation_type}-${timestamp}.zip`;
    const exportPath = path.join(this.exportDir, fileName);
    
    return new Promise((resolve, reject) => {
      const output = require('fs').createWriteStream(exportPath);
      const archive = archiver('zip', { zlib: { level: 9 } });
      
      output.on('close', async () => {
        const stats = await fs.stat(exportPath);
        resolve({ exportPath, fileSize: stats.size });
      });
      
      archive.on('error', (err) => {
        reject(err);
      });
      
      archive.pipe(output);
      
      // Add results in different formats
      this.addResultsToZip(archive, operation, results);
      
      archive.finalize();
    });
  }

  // Add results to ZIP archive in multiple formats
  addResultsToZip(archive, operation, results) {
    // Create summary CSV
    const csvData = this.generateCSVData(results);
    archive.append(csvData, { name: 'summary.csv' });
    
    // Create individual files for each result
    results.forEach((result, index) => {
      const keyword = result.bulk_keywords.keyword.replace(/[^a-zA-Z0-9]/g, '_');
      const resultData = result.result_data;
      
      // Add HTML file
      if (resultData.content) {
        const htmlContent = this.generateHTMLContent(result);
        archive.append(htmlContent, { name: `content/${keyword}_${index + 1}.html` });
      }
      
      // Add Markdown file
      if (resultData.content) {
        const markdownContent = this.generateMarkdownContent(result);
        archive.append(markdownContent, { name: `content/${keyword}_${index + 1}.md` });
      }
      
      // Add JSON file with full data
      archive.append(JSON.stringify(result, null, 2), { name: `json/${keyword}_${index + 1}.json` });
      
      // Add meta tags file if available
      if (resultData.metaTags || resultData.meta) {
        const metaData = resultData.metaTags || resultData.meta;
        archive.append(JSON.stringify(metaData, null, 2), { name: `meta/${keyword}_${index + 1}_meta.json` });
      }
    });
    
    // Add operation summary
    const operationSummary = {
      operation_id: operation.id,
      operation_type: operation.operation_type,
      total_keywords: operation.total_keywords,
      successful_results: results.filter(r => r.bulk_keywords.status === 'completed').length,
      failed_results: results.filter(r => r.bulk_keywords.status === 'failed').length,
      created_at: operation.created_at,
      completed_at: operation.completed_at,
      settings: operation.settings
    };
    
    archive.append(JSON.stringify(operationSummary, null, 2), { name: 'operation_summary.json' });
  }

  // Create CSV export
  async createCSVExport(operation, results) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `bulk-export-${operation.operation_type}-${timestamp}.csv`;
    const exportPath = path.join(this.exportDir, fileName);
    
    const csvData = this.generateCSVData(results);
    await fs.writeFile(exportPath, csvData);
    
    const stats = await fs.stat(exportPath);
    return { exportPath, fileSize: stats.size };
  }

  // Create Excel export
  async createExcelExport(operation, results) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `bulk-export-${operation.operation_type}-${timestamp}.xlsx`;
    const exportPath = path.join(this.exportDir, fileName);
    
    const workbook = XLSX.utils.book_new();
    
    // Summary sheet
    const summaryData = results.map(result => ({
      keyword: result.bulk_keywords.keyword,
      status: result.bulk_keywords.status,
      quality_score: result.quality_score,
      word_count: result.word_count,
      seo_score: result.seo_score,
      created_at: result.created_at,
      industry: result.bulk_keywords.input_data.industry,
      content_type: result.bulk_keywords.input_data.contentType,
      tone: result.bulk_keywords.input_data.tone
    }));
    
    const summarySheet = XLSX.utils.json_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
    
    // Content sheet (for content generation operations)
    if (operation.operation_type === 'content_generation') {
      const contentData = results
        .filter(r => r.result_data.content)
        .map(result => ({
          keyword: result.bulk_keywords.keyword,
          title: result.result_data.title || '',
          content: result.result_data.content.substring(0, 32000), // Excel cell limit
          word_count: result.word_count,
          seo_score: result.seo_score
        }));
      
      const contentSheet = XLSX.utils.json_to_sheet(contentData);
      XLSX.utils.book_append_sheet(workbook, contentSheet, 'Content');
    }
    
    // Meta tags sheet (for meta tag operations)
    if (operation.operation_type === 'meta_tags') {
      const metaData = results
        .filter(r => r.result_data.metaTags || r.result_data.meta)
        .map(result => {
          const meta = result.result_data.metaTags || result.result_data.meta;
          return {
            keyword: result.bulk_keywords.keyword,
            title: meta.title || '',
            description: meta.description || '',
            keywords_meta: meta.keywords || '',
            og_title: meta.openGraph?.title || '',
            og_description: meta.openGraph?.description || ''
          };
        });
      
      const metaSheet = XLSX.utils.json_to_sheet(metaData);
      XLSX.utils.book_append_sheet(workbook, metaSheet, 'Meta Tags');
    }
    
    XLSX.writeFile(workbook, exportPath);
    
    const stats = await fs.stat(exportPath);
    return { exportPath, fileSize: stats.size };
  }

  // Create JSON export
  async createJSONExport(operation, results) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `bulk-export-${operation.operation_type}-${timestamp}.json`;
    const exportPath = path.join(this.exportDir, fileName);
    
    const exportData = {
      operation: {
        id: operation.id,
        type: operation.operation_type,
        total_keywords: operation.total_keywords,
        created_at: operation.created_at,
        completed_at: operation.completed_at,
        settings: operation.settings
      },
      results: results.map(result => ({
        keyword: result.bulk_keywords.keyword,
        status: result.bulk_keywords.status,
        input_data: result.bulk_keywords.input_data,
        result_data: result.result_data,
        quality_score: result.quality_score,
        word_count: result.word_count,
        seo_score: result.seo_score,
        created_at: result.created_at
      }))
    };
    
    await fs.writeFile(exportPath, JSON.stringify(exportData, null, 2));
    
    const stats = await fs.stat(exportPath);
    return { exportPath, fileSize: stats.size };
  }

  // Generate CSV data
  generateCSVData(results) {
    const headers = [
      'keyword',
      'status',
      'quality_score',
      'word_count',
      'seo_score',
      'industry',
      'content_type',
      'tone',
      'title',
      'content_preview',
      'created_at'
    ];
    
    const rows = results.map(result => {
      const content = result.result_data.content || '';
      const contentPreview = content.substring(0, 200).replace(/"/g, '""');
      
      return [
        `"${result.bulk_keywords.keyword}"`,
        `"${result.bulk_keywords.status}"`,
        result.quality_score || '',
        result.word_count || '',
        result.seo_score || '',
        `"${result.bulk_keywords.input_data.industry || ''}"`,
        `"${result.bulk_keywords.input_data.contentType || ''}"`,
        `"${result.bulk_keywords.input_data.tone || ''}"`,
        `"${result.result_data.title || ''}"`,
        `"${contentPreview}"`,
        `"${result.created_at}"`
      ].join(',');
    });
    
    return [headers.join(','), ...rows].join('\n');
  }

  // Generate HTML content for individual results
  generateHTMLContent(result) {
    const keyword = result.bulk_keywords.keyword;
    const resultData = result.result_data;
    
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${resultData.title || keyword}</title>
    ${resultData.metaTags ? this.generateMetaTagsHTML(resultData.metaTags) : ''}
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; }
        .header { border-bottom: 2px solid #eee; padding-bottom: 20px; margin-bottom: 30px; }
        .meta-info { background: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 30px; }
        .content { white-space: pre-wrap; }
        .stats { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${resultData.title || keyword}</h1>
        <p><strong>Target Keyword:</strong> ${keyword}</p>
    </div>
    
    <div class="meta-info">
        <h3>Content Information</h3>
        <p><strong>Word Count:</strong> ${result.word_count || 'N/A'}</p>
        <p><strong>Quality Score:</strong> ${result.quality_score || 'N/A'}</p>
        <p><strong>SEO Score:</strong> ${result.seo_score || 'N/A'}</p>
        <p><strong>Generated:</strong> ${new Date(result.created_at).toLocaleString()}</p>
    </div>
    
    <div class="content">
        ${resultData.content || 'No content available'}
    </div>
    
    ${resultData.analysis ? `
    <div class="stats">
        <h3>SEO Analysis</h3>
        <pre>${JSON.stringify(resultData.analysis, null, 2)}</pre>
    </div>
    ` : ''}
</body>
</html>`;
  }

  // Generate Markdown content for individual results
  generateMarkdownContent(result) {
    const keyword = result.bulk_keywords.keyword;
    const resultData = result.result_data;
    
    return `# ${resultData.title || keyword}

**Target Keyword:** ${keyword}  
**Word Count:** ${result.word_count || 'N/A'}  
**Quality Score:** ${result.quality_score || 'N/A'}  
**SEO Score:** ${result.seo_score || 'N/A'}  
**Generated:** ${new Date(result.created_at).toLocaleString()}

---

${resultData.content || 'No content available'}

${resultData.analysis ? `
## SEO Analysis

\`\`\`json
${JSON.stringify(resultData.analysis, null, 2)}
\`\`\`
` : ''}`;
  }

  // Generate meta tags HTML
  generateMetaTagsHTML(metaTags) {
    let html = '';
    
    if (metaTags.title) {
      html += `    <meta name="title" content="${metaTags.title}">\n`;
    }
    if (metaTags.description) {
      html += `    <meta name="description" content="${metaTags.description}">\n`;
    }
    if (metaTags.keywords) {
      html += `    <meta name="keywords" content="${metaTags.keywords}">\n`;
    }
    
    // Open Graph tags
    if (metaTags.openGraph) {
      const og = metaTags.openGraph;
      if (og.title) html += `    <meta property="og:title" content="${og.title}">\n`;
      if (og.description) html += `    <meta property="og:description" content="${og.description}">\n`;
      if (og.type) html += `    <meta property="og:type" content="${og.type}">\n`;
    }
    
    return html;
  }

  // Get export download info
  async getExportDownload(exportId, userId) {
    const { data: exportRecord } = await this.supabase
      .from('bulk_exports')
      .select('*')
      .eq('id', exportId)
      .eq('user_id', userId)
      .single();
    
    if (!exportRecord) {
      throw new Error('Export not found or access denied');
    }
    
    // Check if file has expired
    if (new Date(exportRecord.expires_at) < new Date()) {
      throw new Error('Export file has expired');
    }
    
    // Check if file exists
    try {
      await fs.access(exportRecord.file_path);
    } catch (error) {
      throw new Error('Export file not found');
    }
    
    // Update download count
    await this.supabase
      .from('bulk_exports')
      .update({ download_count: exportRecord.download_count + 1 })
      .eq('id', exportId);
    
    return {
      filePath: exportRecord.file_path,
      fileName: path.basename(exportRecord.file_path),
      fileSize: exportRecord.file_size,
      contentType: this.getContentType(exportRecord.export_format)
    };
  }

  // Get content type for download
  getContentType(format) {
    const contentTypes = {
      zip: 'application/zip',
      csv: 'text/csv',
      excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      json: 'application/json'
    };
    
    return contentTypes[format] || 'application/octet-stream';
  }

  // Clean up expired exports
  async cleanupExpiredExports() {
    try {
      const { data: expiredExports } = await this.supabase
        .from('bulk_exports')
        .select('*')
        .lt('expires_at', new Date().toISOString());
      
      if (expiredExports && expiredExports.length > 0) {
        // Delete files
        for (const exportRecord of expiredExports) {
          try {
            await fs.unlink(exportRecord.file_path);
          } catch (error) {
            console.warn(`Failed to delete export file: ${exportRecord.file_path}`);
          }
        }
        
        // Delete database records
        await this.supabase
          .from('bulk_exports')
          .delete()
          .lt('expires_at', new Date().toISOString());
        
        console.log(`Cleaned up ${expiredExports.length} expired exports`);
      }
    } catch (error) {
      console.error('Error cleaning up expired exports:', error);
    }
  }

  // Get user's exports
  async getUserExports(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const { data: exports, count } = await this.supabase
      .from('bulk_exports')
      .select(`
        *,
        bulk_operations (
          operation_type,
          total_keywords,
          created_at
        )
      `, { count: 'exact' })
      .eq('user_id', userId)
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });
    
    return {
      exports: exports || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: Math.ceil((count || 0) / limit)
      }
    };
  }
}

module.exports = BulkExporter;