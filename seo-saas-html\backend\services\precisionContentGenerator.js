const axios = require('axios');
const natural = require('natural');
const MultiKeywordOptimizer = require('./multiKeywordOptimizer');
const EEATOptimizer = require('./eeatOptimizer');
const NLPSemanticIntegrator = require('./nlpSemanticIntegrator');
const GrammarReadabilityOptimizer = require('./grammarReadabilityOptimizer');
const AdvancedSEOOptimizer = require('./advancedSEOOptimizer');
const ContentQualityValidator = require('./contentQualityValidator');
const CompetitorBenchmarkMatcher = require('./competitorBenchmarkMatcher');
const MultiEngineCompatibilityTester = require('./multiEngineCompatibilityTester');

class PrecisionContentGenerator {
  constructor() {
    this.groqApiKey = process.env.GROQ_API_KEY;
    this.stemmer = natural.PorterStemmer;
    this.tokenizer = new natural.WordTokenizer();
    this.multiKeywordOptimizer = new MultiKeywordOptimizer();
    this.eeatOptimizer = new EEATOptimizer();
    this.nlpSemanticIntegrator = new NLPSemanticIntegrator();
    this.grammarReadabilityOptimizer = new GrammarReadabilityOptimizer();
    this.advancedSEOOptimizer = new AdvancedSEOOptimizer();
    this.contentQualityValidator = new ContentQualityValidator();
    this.competitorBenchmarkMatcher = new CompetitorBenchmarkMatcher();
    this.multiEngineCompatibilityTester = new MultiEngineCompatibilityTester();
  }

  // Main method to generate content with surgical precision matching competitors
  async generatePrecisionContent(keyword, competitorBenchmarks, semanticData, options = {}) {
    try {
      console.log(`Generating precision content for keyword: ${keyword}`);
      
      if (!competitorBenchmarks) {
        throw new Error('Competitor benchmarks are required for precision content generation');
      }

      // Calculate exact content specifications
      const contentSpecs = this.calculateContentSpecifications(competitorBenchmarks, keyword, semanticData);
      
      // Build precision prompt with exact requirements
      const precisionPrompt = this.buildPrecisionPrompt(keyword, contentSpecs, semanticData, options);
      
      // Generate content using AI with surgical precision
      const generatedContent = await this.generateWithAI(precisionPrompt, contentSpecs);
      
      // Validate and adjust content for precision compliance
      const validatedContent = await this.validateAndAdjustPrecision(generatedContent, contentSpecs, keyword, semanticData);
      
      // Perform multi-keyword density optimization
      const multiKeywordOptimized = await this.performMultiKeywordOptimization(validatedContent.content, keyword, competitorBenchmarks, semanticData);
      
      // Perform E-E-A-T optimization
      const eeatOptimized = await this.performEEATOptimization(multiKeywordOptimized.content, keyword, options);
      
      // Perform NLP semantic integration
      const semanticIntegrated = await this.performNLPSemanticIntegration(eeatOptimized.content, keyword, semanticData, options);
      
      // Perform grammar and readability optimization
      const grammarOptimized = await this.performGrammarReadabilityOptimization(semanticIntegrated.content, keyword, options);
      
      // Perform advanced SEO optimization
      const seoOptimized = await this.performSEOOptimization(grammarOptimized.content, keyword, competitorBenchmarks, semanticData, options);
      
      // Perform comprehensive quality validation
      const qualityValidation = await this.performQualityValidation(seoOptimized.content, keyword, competitorBenchmarks, semanticData, options);
      
      // Perform competitor benchmark matching for surgical precision
      const benchmarkMatching = await this.performBenchmarkMatching(qualityValidation.content, keyword, competitorBenchmarks, options);
      
      // Perform multi-engine compatibility testing
      const compatibilityTesting = await this.performCompatibilityTesting(benchmarkMatching.content, keyword, options);
      
      // Perform final quality assurance
      const finalContent = await this.performQualityAssurance({
        content: compatibilityTesting.content,
        analysis: validatedContent.analysis,
        iterations: validatedContent.iterations,
        multiKeywordOptimization: multiKeywordOptimized.optimization,
        eeatOptimization: eeatOptimized.eeatOptimization,
        semanticIntegration: semanticIntegrated.semanticIntegration,
        grammarOptimization: grammarOptimized.optimization,
        seoOptimization: seoOptimized.seoOptimization,
        qualityValidation: qualityValidation.validation,
        benchmarkMatching: benchmarkMatching.matching,
        compatibilityTesting: compatibilityTesting.compatibility
      }, contentSpecs, keyword);

      return {
        success: true,
        content: finalContent.content,
        metadata: {
          targetWordCount: contentSpecs.wordCount.target,
          actualWordCount: finalContent.actualWordCount,
          keywordDensity: finalContent.keywordDensity,
          headingStructure: finalContent.headingStructure,
          semanticIntegration: finalContent.semanticIntegration,
          eeatCompliance: finalContent.eeatCompliance,
          precisionScore: finalContent.precisionScore,
          multiKeywordOptimization: multiKeywordOptimized.metadata,
          eeatOptimization: eeatOptimized.metadata,
          semanticIntegration: semanticIntegrated.metadata,
          grammarOptimization: grammarOptimized.metadata,
          seoOptimization: seoOptimized.metadata,
          qualityValidation: qualityValidation.metadata,
          benchmarkMatching: benchmarkMatching.metadata,
          compatibilityTesting: compatibilityTesting.metadata,
          generatedAt: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('Precision content generation error:', error);
      return {
        success: false,
        error: error.message,
        content: null
      };
    }
  }

  // Calculate exact content specifications from competitor benchmarks
  calculateContentSpecifications(benchmarks, keyword, semanticData) {
    const specs = {
      wordCount: {
        target: Math.round(benchmarks.wordCount.target), // Exact word count
        tolerance: 0, // Zero tolerance - must be exact
        min: benchmarks.wordCount.target - 5,
        max: benchmarks.wordCount.target + 5
      },
      keywordDensity: {
        main: {
          target: benchmarks.keywordDensity.main,
          tolerance: 0.1, // 0.1% tolerance
          targetCount: Math.round((benchmarks.keywordDensity.main / 100) * benchmarks.wordCount.target)
        },
        components: {}
      },
      headingStructure: {
        total: benchmarks.headingStructure.totalHeadings,
        h1: 1, // Always exactly 1
        h2: benchmarks.headingStructure.h2Count,
        h3: benchmarks.headingStructure.h3Count,
        keywordOptimization: benchmarks.keywordOptimization.targetRatio
      },
      contentStructure: {
        sentences: benchmarks.contentStructure.sentences,
        paragraphs: benchmarks.contentStructure.paragraphs,
        avgSentenceLength: Math.round(benchmarks.wordCount.target / benchmarks.contentStructure.sentences)
      },
      semanticRequirements: semanticData ? {
        lsiKeywords: {
          required: semanticData.headingOptimizedTerms.slice(0, 10),
          targetDensity: 0.5, // 0.5% density for LSI keywords
          targetCount: Math.round((0.5 / 100) * benchmarks.wordCount.target)
        },
        entities: {
          required: semanticData.prioritizedEntities.slice(0, 5).map(e => e.term),
          targetMentions: 2 // Each entity mentioned at least 2 times
        },
        variations: {
          required: semanticData.prioritizedVariations.slice(0, 8).map(v => v.term),
          targetUsage: 1 // Each variation used at least once
        }
      } : null
    };

    // Calculate component keyword densities
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    keywordComponents.forEach(component => {
      if (benchmarks.keywordDensity.components[component]) {
        specs.keywordDensity.components[component] = {
          target: benchmarks.keywordDensity.components[component],
          tolerance: 0.1,
          targetCount: Math.round((benchmarks.keywordDensity.components[component] / 100) * benchmarks.wordCount.target)
        };
      }
    });

    return specs;
  }

  // Build precision prompt with exact requirements
  buildPrecisionPrompt(keyword, specs, semanticData, options) {
    const industry = options.industry || 'general';
    const contentType = options.contentType || 'comprehensive guide';
    const targetAudience = options.targetAudience || 'professionals';
    const tone = options.tone || 'professional';

    // Build heading structure requirements
    const headingRequirements = `
HEADING STRUCTURE (MANDATORY):
- Exactly 1 H1 tag containing "${keyword}"
- Exactly ${specs.headingStructure.h2} H2 tags
- Exactly ${specs.headingStructure.h3} H3 tags
- ${Math.round(specs.headingStructure.keywordOptimization)}% of headings must contain keyword or components
${semanticData ? `- Integrate these LSI terms in headings: ${semanticData.headingOptimizedTerms.slice(0, 5).join(', ')}` : ''}
${semanticData && semanticData.prioritizedEntities.length > 0 ? `- Include these entities in headings: ${semanticData.prioritizedEntities.slice(0, 2).map(e => e.term).join(', ')}` : ''}`;

    // Build keyword density requirements
    const keywordRequirements = `
KEYWORD DENSITY (SURGICAL PRECISION):
- Main keyword "${keyword}": EXACTLY ${specs.keywordDensity.main.targetCount} mentions (${specs.keywordDensity.main.target}% density)
${Object.entries(specs.keywordDensity.components).map(([component, data]) => 
  `- Component "${component}": EXACTLY ${data.targetCount} mentions (${data.target}% density)`
).join('\n')}`;

    // Build semantic requirements
    const semanticRequirements = specs.semanticRequirements ? `
SEMANTIC INTEGRATION (MANDATORY):
- LSI Keywords: Include these terms ${specs.semanticRequirements.lsiKeywords.targetCount} times total: ${specs.semanticRequirements.lsiKeywords.required.join(', ')}
- Entities: Mention each entity ${specs.semanticRequirements.entities.targetMentions} times: ${specs.semanticRequirements.entities.required.join(', ')}
- Variations: Use each variation at least once: ${specs.semanticRequirements.variations.required.join(', ')}
- Semantic Clusters: Integrate primary cluster terms naturally throughout content` : '';

    // Build content structure requirements
    const structureRequirements = `
CONTENT STRUCTURE (EXACT):
- EXACTLY ${specs.wordCount.target} words (not ${specs.wordCount.target - 1} or ${specs.wordCount.target + 1})
- EXACTLY ${specs.contentStructure.sentences} sentences
- EXACTLY ${specs.contentStructure.paragraphs} paragraphs
- Average sentence length: ${specs.contentStructure.avgSentenceLength} words`;

    // Build E-E-A-T requirements
    const eeatRequirements = `
E-E-A-T COMPLIANCE (MANDATORY):
- Experience: Include real-world examples, case studies, practical applications
- Expertise: Demonstrate deep knowledge with technical details, industry insights
- Authoritativeness: Reference authoritative sources, include expert opinions
- Trustworthiness: Provide transparent information, include disclaimers where appropriate`;

    // Build quality requirements
    const qualityRequirements = `
CONTENT QUALITY (NON-NEGOTIABLE):
- Natural, human-like writing that flows seamlessly
- Perfect grammar and readability
- NLP-friendly structure with proper semantic relationships
- Topical authority establishment through comprehensive coverage
- Zero keyword stuffing - all integrations must be natural
- Content must rank on all search engines and AI systems`;

    const fullPrompt = `You are an expert SEO content writer tasked with creating surgical precision content that matches top competitor benchmarks exactly.

TARGET KEYWORD: "${keyword}"
INDUSTRY: ${industry}
CONTENT TYPE: ${contentType}
TARGET AUDIENCE: ${targetAudience}
TONE: ${tone}

${headingRequirements}

${keywordRequirements}

${semanticRequirements}

${structureRequirements}

${eeatRequirements}

${qualityRequirements}

CRITICAL INSTRUCTIONS:
1. You MUST hit the exact word count of ${specs.wordCount.target} words
2. You MUST use the exact keyword density specified
3. You MUST create the exact heading structure specified
4. You MUST integrate all LSI keywords and entities naturally
5. The content MUST read naturally and provide genuine value
6. Every requirement is NON-NEGOTIABLE

Write comprehensive, valuable content about "${keyword}" that meets ALL specifications exactly. Start with the H1 heading and create a complete, well-structured article.`;

    return fullPrompt;
  }

  // Generate content using AI with surgical precision
  async generateWithAI(prompt, specs) {
    try {
      const response = await axios.post('https://api.groq.com/openai/v1/chat/completions', {
        model: 'llama-3.1-70b-versatile',
        messages: [
          {
            role: 'system',
            content: 'You are a precision SEO content generator. You MUST follow all specifications exactly. Every word count, keyword density, and structural requirement is mandatory and non-negotiable.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3, // Lower temperature for more consistent output
        max_tokens: Math.min(8000, specs.wordCount.target * 2), // Ensure enough tokens
        top_p: 0.9
      }, {
        headers: {
          'Authorization': `Bearer ${this.groqApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.data.choices || response.data.choices.length === 0) {
        throw new Error('No content generated from AI');
      }

      const content = response.data.choices[0].message.content;
      const usage = response.data.usage;

      return {
        content,
        usage,
        model: 'llama-3.1-70b-versatile'
      };

    } catch (error) {
      console.error('AI generation error:', error);
      throw new Error(`AI content generation failed: ${error.message}`);
    }
  }

  // Validate and adjust content for precision compliance
  async validateAndAdjustPrecision(generatedContent, specs, keyword, semanticData) {
    const content = generatedContent.content;
    let adjustedContent = content;
    let iterations = 0;
    const maxIterations = 3;

    while (iterations < maxIterations) {
      // Analyze current content
      const analysis = this.analyzeContentPrecision(adjustedContent, specs, keyword, semanticData);
      
      console.log(`Precision validation iteration ${iterations + 1}:`, {
        targetWordCount: specs.wordCount.target,
        actualWordCount: analysis.wordCount,
        keywordDensityTarget: specs.keywordDensity.main.target,
        keywordDensityActual: analysis.keywordDensity.main
      });

      // Check if content meets precision requirements
      if (this.isPrecisionCompliant(analysis, specs)) {
        console.log('Content is precision compliant!');
        return {
          content: adjustedContent,
          analysis,
          iterations: iterations + 1
        };
      }

      // Adjust content for precision compliance
      adjustedContent = await this.adjustContentPrecision(adjustedContent, analysis, specs, keyword, semanticData);
      iterations++;
    }

    // If still not compliant after max iterations, return best attempt
    console.warn(`Content not fully compliant after ${maxIterations} iterations`);
    const finalAnalysis = this.analyzeContentPrecision(adjustedContent, specs, keyword, semanticData);
    
    return {
      content: adjustedContent,
      analysis: finalAnalysis,
      iterations,
      warning: 'Content may not be fully precision compliant'
    };
  }

  // Analyze content precision against specifications
  analyzeContentPrecision(content, specs, keyword, semanticData) {
    const words = this.tokenizer.tokenize(content.toLowerCase()) || [];
    const wordCount = words.length;
    
    // Analyze keyword density
    const keywordAnalysis = this.analyzeKeywordDensity(content, keyword);
    
    // Analyze heading structure
    const headingAnalysis = this.analyzeHeadingStructure(content, keyword);
    
    // Analyze semantic integration
    const semanticAnalysis = semanticData ? this.analyzeSemanticIntegration(content, semanticData) : null;
    
    // Analyze content structure
    const structureAnalysis = this.analyzeContentStructure(content);

    return {
      wordCount,
      keywordDensity: keywordAnalysis,
      headingStructure: headingAnalysis,
      semanticIntegration: semanticAnalysis,
      contentStructure: structureAnalysis,
      compliance: this.calculateComplianceScore(specs, {
        wordCount,
        keywordDensity: keywordAnalysis,
        headingStructure: headingAnalysis,
        semanticIntegration: semanticAnalysis
      })
    };
  }

  // Analyze keyword density in content
  analyzeKeywordDensity(content, keyword) {
    const words = this.tokenizer.tokenize(content.toLowerCase()) || [];
    const totalWords = words.length;
    
    if (totalWords === 0) return { main: 0, components: {} };

    // Main keyword analysis
    const keywordLower = keyword.toLowerCase();
    const mainMatches = (content.toLowerCase().match(new RegExp(keywordLower.replace(/\s+/g, '\\s+'), 'g')) || []).length;
    const mainDensity = (mainMatches / totalWords) * 100;

    // Component analysis
    const components = {};
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    
    keywordComponents.forEach(component => {
      if (component.length > 2) {
        const componentMatches = (content.toLowerCase().match(new RegExp(`\\b${component}\\b`, 'g')) || []).length;
        components[component] = {
          matches: componentMatches,
          density: (componentMatches / totalWords) * 100
        };
      }
    });

    return {
      main: {
        matches: mainMatches,
        density: parseFloat(mainDensity.toFixed(2))
      },
      components
    };
  }

  // Analyze heading structure
  analyzeHeadingStructure(content, keyword) {
    const h1Matches = content.match(/<h1[^>]*>(.*?)<\/h1>/gi) || content.match(/^# (.+)$/gm) || [];
    const h2Matches = content.match(/<h2[^>]*>(.*?)<\/h2>/gi) || content.match(/^## (.+)$/gm) || [];
    const h3Matches = content.match(/<h3[^>]*>(.*?)<\/h3>/gi) || content.match(/^### (.+)$/gm) || [];
    
    const keywordLower = keyword.toLowerCase();
    
    // Count keyword-optimized headings
    const allHeadings = [...h1Matches, ...h2Matches, ...h3Matches];
    const keywordOptimizedHeadings = allHeadings.filter(heading => 
      heading.toLowerCase().includes(keywordLower)
    ).length;

    return {
      h1Count: h1Matches.length,
      h2Count: h2Matches.length,
      h3Count: h3Matches.length,
      totalHeadings: allHeadings.length,
      keywordOptimizedHeadings,
      keywordOptimizationRatio: allHeadings.length > 0 ? (keywordOptimizedHeadings / allHeadings.length) * 100 : 0
    };
  }

  // Analyze semantic integration
  analyzeSemanticIntegration(content, semanticData) {
    const contentLower = content.toLowerCase();
    
    // Analyze LSI keyword usage
    const lsiUsage = {};
    semanticData.headingOptimizedTerms.forEach(term => {
      const matches = (contentLower.match(new RegExp(`\\b${term.toLowerCase()}\\b`, 'g')) || []).length;
      lsiUsage[term] = matches;
    });

    // Analyze entity usage
    const entityUsage = {};
    semanticData.prioritizedEntities.forEach(entity => {
      const matches = (contentLower.match(new RegExp(`\\b${entity.term.toLowerCase()}\\b`, 'g')) || []).length;
      entityUsage[entity.term] = matches;
    });

    // Analyze variation usage
    const variationUsage = {};
    semanticData.prioritizedVariations.forEach(variation => {
      const matches = (contentLower.match(new RegExp(`\\b${variation.term.toLowerCase()}\\b`, 'g')) || []).length;
      variationUsage[variation.term] = matches;
    });

    return {
      lsiUsage,
      entityUsage,
      variationUsage,
      totalLSIMatches: Object.values(lsiUsage).reduce((sum, count) => sum + count, 0),
      totalEntityMatches: Object.values(entityUsage).reduce((sum, count) => sum + count, 0),
      totalVariationMatches: Object.values(variationUsage).reduce((sum, count) => sum + count, 0)
    };
  }

  // Analyze content structure
  analyzeContentStructure(content) {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const words = this.tokenizer.tokenize(content) || [];

    return {
      sentences: sentences.length,
      paragraphs: paragraphs.length,
      avgSentenceLength: sentences.length > 0 ? words.length / sentences.length : 0
    };
  }

  // Check if content is precision compliant
  isPrecisionCompliant(analysis, specs) {
    // Word count compliance (exact match required)
    const wordCountCompliant = Math.abs(analysis.wordCount - specs.wordCount.target) <= specs.wordCount.tolerance;
    
    // Keyword density compliance
    const mainKeywordCompliant = Math.abs(analysis.keywordDensity.main.density - specs.keywordDensity.main.target) <= specs.keywordDensity.main.tolerance;
    
    // Component keyword compliance
    const componentCompliant = Object.entries(specs.keywordDensity.components).every(([component, spec]) => {
      const actual = analysis.keywordDensity.components[component]?.density || 0;
      return Math.abs(actual - spec.target) <= spec.tolerance;
    });

    // Heading structure compliance
    const headingCompliant = 
      analysis.headingStructure.h1Count === 1 &&
      analysis.headingStructure.h2Count === specs.headingStructure.h2 &&
      analysis.headingStructure.h3Count === specs.headingStructure.h3;

    // Semantic compliance (if required)
    const semanticCompliant = !specs.semanticRequirements || (
      analysis.semanticIntegration.totalLSIMatches >= specs.semanticRequirements.lsiKeywords.targetCount &&
      Object.values(analysis.semanticIntegration.entityUsage).every(count => count >= specs.semanticRequirements.entities.targetMentions) &&
      Object.values(analysis.semanticIntegration.variationUsage).every(count => count >= specs.semanticRequirements.variations.targetUsage)
    );

    console.log('Compliance check:', {
      wordCount: wordCountCompliant,
      mainKeyword: mainKeywordCompliant,
      components: componentCompliant,
      headings: headingCompliant,
      semantic: semanticCompliant
    });

    return wordCountCompliant && mainKeywordCompliant && componentCompliant && headingCompliant && semanticCompliant;
  }

  // Calculate compliance score
  calculateComplianceScore(specs, analysis) {
    let score = 0;
    let maxScore = 0;

    // Word count score (25 points)
    maxScore += 25;
    const wordCountAccuracy = 1 - Math.abs(analysis.wordCount - specs.wordCount.target) / specs.wordCount.target;
    score += Math.max(0, wordCountAccuracy * 25);

    // Keyword density score (30 points)
    maxScore += 30;
    const keywordAccuracy = 1 - Math.abs(analysis.keywordDensity.main.density - specs.keywordDensity.main.target) / specs.keywordDensity.main.target;
    score += Math.max(0, keywordAccuracy * 30);

    // Heading structure score (20 points)
    maxScore += 20;
    const headingScore = 
      (analysis.headingStructure.h1Count === 1 ? 5 : 0) +
      (analysis.headingStructure.h2Count === specs.headingStructure.h2 ? 8 : 0) +
      (analysis.headingStructure.h3Count === specs.headingStructure.h3 ? 7 : 0);
    score += headingScore;

    // Semantic integration score (25 points)
    if (specs.semanticRequirements && analysis.semanticIntegration) {
      maxScore += 25;
      const lsiScore = Math.min(25, (analysis.semanticIntegration.totalLSIMatches / specs.semanticRequirements.lsiKeywords.targetCount) * 15);
      const entityScore = Math.min(10, Object.values(analysis.semanticIntegration.entityUsage).filter(count => count >= 2).length * 2);
      score += lsiScore + entityScore;
    }

    return {
      score: Math.round(score),
      maxScore,
      percentage: Math.round((score / maxScore) * 100)
    };
  }

  // Adjust content for precision compliance
  async adjustContentPrecision(content, analysis, specs, keyword, semanticData) {
    let adjustedContent = content;

    // Adjust word count
    if (analysis.wordCount !== specs.wordCount.target) {
      adjustedContent = await this.adjustWordCount(adjustedContent, analysis.wordCount, specs.wordCount.target);
    }

    // Adjust keyword density
    if (Math.abs(analysis.keywordDensity.main.density - specs.keywordDensity.main.target) > specs.keywordDensity.main.tolerance) {
      adjustedContent = await this.adjustKeywordDensity(adjustedContent, keyword, specs.keywordDensity.main);
    }

    // Adjust heading structure
    if (analysis.headingStructure.h2Count !== specs.headingStructure.h2 || 
        analysis.headingStructure.h3Count !== specs.headingStructure.h3) {
      adjustedContent = await this.adjustHeadingStructure(adjustedContent, specs.headingStructure, keyword);
    }

    return adjustedContent;
  }

  // Adjust word count by adding or removing content
  async adjustWordCount(content, currentCount, targetCount) {
    const difference = targetCount - currentCount;
    
    if (Math.abs(difference) <= 5) return content; // Close enough
    
    if (difference > 0) {
      // Need to add words
      return await this.expandContent(content, difference);
    } else {
      // Need to remove words
      return await this.condenseContent(content, Math.abs(difference));
    }
  }

  // Expand content to add more words
  async expandContent(content, wordsToAdd) {
    // Add more detailed explanations, examples, or elaborations
    // This is a simplified implementation - in production would use AI to expand naturally
    const sentences = content.split(/[.!?]+/);
    const expansions = [
      ', providing comprehensive benefits',
      ' and ensuring optimal results',
      ', which is particularly important for professionals',
      ' through proven methodologies',
      ', according to industry best practices'
    ];

    let expandedContent = content;
    let addedWords = 0;
    
    for (let i = 0; i < sentences.length && addedWords < wordsToAdd; i++) {
      if (sentences[i].trim().length > 0) {
        const expansion = expansions[i % expansions.length];
        expandedContent = expandedContent.replace(sentences[i] + '.', sentences[i] + expansion + '.');
        addedWords += expansion.split(' ').length;
      }
    }

    return expandedContent;
  }

  // Condense content to remove words
  async condenseContent(content, wordsToRemove) {
    // Remove redundant words, phrases, or sentences
    // This is a simplified implementation
    let condensedContent = content;
    
    // Remove common redundant phrases
    const redundantPhrases = [
      'it is important to note that',
      'it should be mentioned that',
      'as we can see',
      'in other words',
      'basically'
    ];

    redundantPhrases.forEach(phrase => {
      condensedContent = condensedContent.replace(new RegExp(phrase, 'gi'), '');
    });

    return condensedContent.replace(/\s+/g, ' ').trim();
  }

  // Adjust keyword density
  async adjustKeywordDensity(content, keyword, spec) {
    const currentMatches = (content.toLowerCase().match(new RegExp(keyword.toLowerCase().replace(/\s+/g, '\\s+'), 'g')) || []).length;
    const targetMatches = spec.targetCount;
    
    if (currentMatches === targetMatches) return content;
    
    let adjustedContent = content;
    
    if (currentMatches < targetMatches) {
      // Add more keyword instances
      const toAdd = targetMatches - currentMatches;
      adjustedContent = await this.addKeywordInstances(adjustedContent, keyword, toAdd);
    } else {
      // Remove keyword instances
      const toRemove = currentMatches - targetMatches;
      adjustedContent = await this.removeKeywordInstances(adjustedContent, keyword, toRemove);
    }

    return adjustedContent;
  }

  // Add keyword instances naturally
  async addKeywordInstances(content, keyword, count) {
    // Add keyword instances in natural contexts
    const keywordVariations = [
      `effective ${keyword}`,
      `professional ${keyword}`,
      `quality ${keyword}`,
      `${keyword} solutions`,
      `${keyword} strategies`
    ];

    let modifiedContent = content;
    for (let i = 0; i < count && i < keywordVariations.length; i++) {
      const variation = keywordVariations[i];
      // Find a suitable place to insert the keyword variation
      const sentences = modifiedContent.split('.');
      if (sentences.length > i + 1) {
        sentences[i + 1] = sentences[i + 1].replace(/\b(the|a|an)\b/i, variation);
        modifiedContent = sentences.join('.');
      }
    }

    return modifiedContent;
  }

  // Remove keyword instances
  async removeKeywordInstances(content, keyword, count) {
    // Replace some keyword instances with pronouns or synonyms
    const keywordRegex = new RegExp(keyword.replace(/\s+/g, '\\s+'), 'gi');
    const matches = content.match(keywordRegex) || [];
    
    let modifiedContent = content;
    for (let i = 0; i < count && i < matches.length; i++) {
      modifiedContent = modifiedContent.replace(keywordRegex, 'this solution');
    }

    return modifiedContent;
  }

  // Adjust heading structure
  async adjustHeadingStructure(content, spec, keyword) {
    // This is a simplified implementation
    // In production, would use AI to properly restructure headings
    let adjustedContent = content;
    
    // Ensure proper heading hierarchy
    const h2Count = (adjustedContent.match(/^## /gm) || []).length;
    const h3Count = (adjustedContent.match(/^### /gm) || []).length;
    
    // Add missing H2 headings if needed
    if (h2Count < spec.h2) {
      const additionalH2s = spec.h2 - h2Count;
      for (let i = 0; i < additionalH2s; i++) {
        adjustedContent += `\n\n## Additional ${keyword} Information\n\nThis section provides supplementary details about ${keyword}.`;
      }
    }
    
    // Add missing H3 headings if needed
    if (h3Count < spec.h3) {
      const additionalH3s = spec.h3 - h3Count;
      for (let i = 0; i < additionalH3s; i++) {
        adjustedContent += `\n\n### ${keyword} Details\n\nSpecific information about ${keyword} implementation.`;
      }
    }

    return adjustedContent;
  }

  // Perform final quality assurance
  async performQualityAssurance(validatedContent, specs, keyword) {
    const content = validatedContent.content;
    
    // Final analysis
    const finalAnalysis = this.analyzeContentPrecision(content, specs, keyword);
    
    // Calculate precision score
    const precisionScore = this.calculatePrecisionScore(finalAnalysis, specs);
    
    // Assess E-E-A-T compliance
    const eeatCompliance = this.assessEEATCompliance(content);
    
    // Check readability and naturalness
    const qualityMetrics = this.assessContentQuality(content);

    return {
      content,
      actualWordCount: finalAnalysis.wordCount,
      keywordDensity: finalAnalysis.keywordDensity,
      headingStructure: finalAnalysis.headingStructure,
      semanticIntegration: finalAnalysis.semanticIntegration,
      eeatCompliance,
      qualityMetrics,
      precisionScore
    };
  }

  // Calculate precision score (0-100)
  calculatePrecisionScore(analysis, specs) {
    const wordCountAccuracy = 1 - Math.abs(analysis.wordCount - specs.wordCount.target) / specs.wordCount.target;
    const keywordAccuracy = 1 - Math.abs(analysis.keywordDensity.main.density - specs.keywordDensity.main.target) / specs.keywordDensity.main.target;
    const headingAccuracy = (
      (analysis.headingStructure.h1Count === 1 ? 1 : 0) +
      (analysis.headingStructure.h2Count === specs.headingStructure.h2 ? 1 : 0) +
      (analysis.headingStructure.h3Count === specs.headingStructure.h3 ? 1 : 0)
    ) / 3;

    const overallScore = (wordCountAccuracy * 0.4 + keywordAccuracy * 0.4 + headingAccuracy * 0.2) * 100;
    
    return {
      overall: Math.round(overallScore),
      wordCountAccuracy: Math.round(wordCountAccuracy * 100),
      keywordAccuracy: Math.round(keywordAccuracy * 100),
      headingAccuracy: Math.round(headingAccuracy * 100)
    };
  }

  // Assess E-E-A-T compliance
  assessEEATCompliance(content) {
    const contentLower = content.toLowerCase();
    
    const experienceSignals = [
      'experience', 'worked with', 'helped clients', 'case study', 'real-world',
      'hands-on', 'practical', 'first-hand', 'personal experience'
    ];
    
    const expertiseSignals = [
      'expert', 'specialist', 'professional', 'certified', 'qualified',
      'according to', 'research shows', 'studies indicate'
    ];
    
    const authoritySignals = [
      'industry leader', 'recognized', 'award', 'published', 'featured'
    ];
    
    const trustSignals = [
      'guarantee', 'secure', 'verified', 'testimonial', 'review'
    ];

    return {
      experience: experienceSignals.filter(signal => contentLower.includes(signal)).length,
      expertise: expertiseSignals.filter(signal => contentLower.includes(signal)).length,
      authority: authoritySignals.filter(signal => contentLower.includes(signal)).length,
      trust: trustSignals.filter(signal => contentLower.includes(signal)).length
    };
  }

  // Assess content quality
  assessContentQuality(content) {
    const words = this.tokenizer.tokenize(content) || [];
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    return {
      wordCount: words.length,
      sentenceCount: sentences.length,
      avgSentenceLength: sentences.length > 0 ? words.length / sentences.length : 0,
      readabilityScore: this.calculateReadabilityScore(content),
      uniqueWords: new Set(words.map(w => w.toLowerCase())).size,
      lexicalDiversity: words.length > 0 ? new Set(words.map(w => w.toLowerCase())).size / words.length : 0
    };
  }

  // Calculate readability score (simplified Flesch Reading Ease)
  calculateReadabilityScore(content) {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = this.tokenizer.tokenize(content) || [];
    const syllables = words.reduce((count, word) => count + this.countSyllables(word), 0);
    
    if (sentences.length === 0 || words.length === 0) return 0;
    
    const avgSentenceLength = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;
    
    return 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
  }

  // Count syllables in a word (simplified)
  countSyllables(word) {
    const vowels = 'aeiouy';
    let count = 0;
    let previousWasVowel = false;
    
    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i].toLowerCase());
      if (isVowel && !previousWasVowel) {
        count++;
      }
      previousWasVowel = isVowel;
    }
    
    if (word.endsWith('e') && count > 1) {
      count--;
    }
    
    return Math.max(1, count);
  }

  // Perform multi-keyword density optimization
  async performMultiKeywordOptimization(content, keyword, competitorBenchmarks, semanticData) {
    try {
      console.log('Performing multi-keyword density optimization...');
      
      const optimizationResult = await this.multiKeywordOptimizer.optimizeMultiKeywordDensity(
        content,
        keyword,
        competitorBenchmarks,
        semanticData,
        {
          preserveReadability: true,
          maintainNaturalFlow: true,
          maxIterations: 3
        }
      );

      if (!optimizationResult.success) {
        console.warn('Multi-keyword optimization failed, using original content');
        return {
          content,
          optimization: null,
          metadata: {
            optimizationAttempted: true,
            optimizationSuccessful: false,
            error: optimizationResult.error
          }
        };
      }

      console.log('Multi-keyword optimization completed successfully:', {
        keywordsOptimized: optimizationResult.metadata.totalKeywordsOptimized,
        accuracy: optimizationResult.metadata.optimizationAccuracy
      });

      return {
        content: optimizationResult.content,
        optimization: optimizationResult.optimization,
        metadata: {
          optimizationAttempted: true,
          optimizationSuccessful: true,
          totalKeywordsOptimized: optimizationResult.metadata.totalKeywordsOptimized,
          optimizationAccuracy: optimizationResult.metadata.optimizationAccuracy,
          improvements: optimizationResult.optimization.improvements
        }
      };

    } catch (error) {
      console.error('Multi-keyword optimization error:', error);
      return {
        content,
        optimization: null,
        metadata: {
          optimizationAttempted: true,
          optimizationSuccessful: false,
          error: error.message
        }
      };
    }
  }

  // Perform E-E-A-T optimization
  async performEEATOptimization(content, keyword, options) {
    try {
      console.log('Performing E-E-A-T optimization...');
      
      const eeatResult = await this.eeatOptimizer.optimizeEEATCompliance(content, keyword, {
        industry: options.industry || 'general',
        contentType: options.contentType || 'guide',
        targetAudience: options.targetAudience || 'professionals'
      });

      if (!eeatResult.success) {
        console.warn('E-E-A-T optimization failed, using original content');
        return {
          content,
          eeatOptimization: null,
          metadata: {
            eeatOptimizationAttempted: true,
            eeatOptimizationSuccessful: false,
            error: eeatResult.error
          }
        };
      }

      console.log('E-E-A-T optimization completed successfully:', {
        eeatScore: eeatResult.metadata.eeatScore,
        improvementScore: eeatResult.metadata.improvementScore,
        signalsAdded: eeatResult.metadata.signalsAdded
      });

      return {
        content: eeatResult.content,
        eeatOptimization: eeatResult.eeatOptimization,
        metadata: {
          eeatOptimizationAttempted: true,
          eeatOptimizationSuccessful: true,
          eeatScore: eeatResult.metadata.eeatScore,
          improvementScore: eeatResult.metadata.improvementScore,
          signalsAdded: eeatResult.metadata.signalsAdded,
          complianceReport: eeatResult.eeatOptimization.complianceReport
        }
      };

    } catch (error) {
      console.error('E-E-A-T optimization error:', error);
      return {
        content,
        eeatOptimization: null,
        metadata: {
          eeatOptimizationAttempted: true,
          eeatOptimizationSuccessful: false,
          error: error.message
        }
      };
    }
  }

  // Perform NLP semantic integration
  async performNLPSemanticIntegration(content, keyword, semanticData, options) {
    try {
      console.log('Performing NLP semantic integration...');
      
      const semanticResult = await this.nlpSemanticIntegrator.performSemanticIntegration(
        content, 
        keyword, 
        semanticData, 
        {
          industry: options.industry || 'general',
          contentType: options.contentType || 'guide',
          targetAudience: options.targetAudience || 'professionals'
        }
      );

      if (!semanticResult.success) {
        console.warn('NLP semantic integration failed, using original content');
        return {
          content,
          semanticIntegration: null,
          metadata: {
            semanticIntegrationAttempted: true,
            semanticIntegrationSuccessful: false,
            error: semanticResult.error
          }
        };
      }

      console.log('NLP semantic integration completed successfully:', {
        semanticScore: semanticResult.metadata.semanticScore,
        topicalAuthorityScore: semanticResult.metadata.topicalAuthorityScore,
        entitiesIntegrated: semanticResult.metadata.entitiesIntegrated,
        semanticRelationships: semanticResult.metadata.semanticRelationships
      });

      return {
        content: semanticResult.content,
        semanticIntegration: semanticResult.semanticIntegration,
        metadata: {
          semanticIntegrationAttempted: true,
          semanticIntegrationSuccessful: true,
          semanticScore: semanticResult.metadata.semanticScore,
          topicalAuthorityScore: semanticResult.metadata.topicalAuthorityScore,
          entitiesIntegrated: semanticResult.metadata.entitiesIntegrated,
          semanticRelationships: semanticResult.metadata.semanticRelationships,
          integrationReport: semanticResult.semanticIntegration.integrationReport
        }
      };

    } catch (error) {
      console.error('NLP semantic integration error:', error);
      return {
        content,
        semanticIntegration: null,
        metadata: {
          semanticIntegrationAttempted: true,
          semanticIntegrationSuccessful: false,
          error: error.message
        }
      };
    }
  }

  // Perform grammar and readability optimization
  async performGrammarReadabilityOptimization(content, keyword, options) {
    try {
      console.log('Performing grammar and readability optimization...');
      
      const grammarResult = await this.grammarReadabilityOptimizer.optimizeGrammarAndReadability(
        content, 
        keyword, 
        {
          targetAudience: options.targetAudience || 'general',
          industry: options.industry || 'general',
          contentType: options.contentType || 'guide'
        }
      );

      if (!grammarResult.success) {
        console.warn('Grammar and readability optimization failed, using original content');
        return {
          content,
          optimization: null,
          metadata: {
            grammarOptimizationAttempted: true,
            grammarOptimizationSuccessful: false,
            error: grammarResult.error
          }
        };
      }

      console.log('Grammar and readability optimization completed successfully:', {
        grammarScore: grammarResult.metadata.grammarScore,
        readabilityScore: grammarResult.metadata.readabilityScore,
        fleschReadingEase: grammarResult.metadata.fleschReadingEase,
        gradeLevel: grammarResult.metadata.gradeLevel
      });

      return {
        content: grammarResult.content,
        optimization: grammarResult.optimization,
        metadata: {
          grammarOptimizationAttempted: true,
          grammarOptimizationSuccessful: true,
          grammarScore: grammarResult.metadata.grammarScore,
          readabilityScore: grammarResult.metadata.readabilityScore,
          fleschReadingEase: grammarResult.metadata.fleschReadingEase,
          gradeLevel: grammarResult.metadata.gradeLevel,
          corrections: grammarResult.optimization.corrections,
          finalValidation: grammarResult.optimization.finalValidation
        }
      };

    } catch (error) {
      console.error('Grammar and readability optimization error:', error);
      return {
        content,
        optimization: null,
        metadata: {
          grammarOptimizationAttempted: true,
          grammarOptimizationSuccessful: false,
          error: error.message
        }
      };
    }
  }

  // Perform advanced SEO optimization
  async performSEOOptimization(content, keyword, competitorBenchmarks, semanticData, options) {
    try {
      console.log('Performing advanced SEO optimization...');
      
      const seoResult = await this.advancedSEOOptimizer.optimizeSEOSignals(
        content, 
        keyword, 
        competitorBenchmarks, 
        semanticData, 
        {
          location: options.location || 'United States',
          searchEngine: options.searchEngine || 'google',
          industry: options.industry || 'general',
          contentType: options.contentType || 'guide',
          targetAudience: options.targetAudience || 'professionals'
        }
      );

      if (!seoResult.success) {
        console.warn('Advanced SEO optimization failed, using original content');
        return {
          content,
          seoOptimization: null,
          metadata: {
            seoOptimizationAttempted: true,
            seoOptimizationSuccessful: false,
            error: seoResult.error
          }
        };
      }

      console.log('Advanced SEO optimization completed successfully:', {
        seoScore: seoResult.metadata.seoScore,
        searchEngineCompatibility: seoResult.metadata.searchEngineCompatibility,
        rankingPotential: seoResult.metadata.rankingPotential,
        featuredSnippetPotential: seoResult.metadata.featuredSnippetPotential
      });

      return {
        content: seoResult.content,
        seoOptimization: seoResult.seoOptimization,
        metadata: {
          seoOptimizationAttempted: true,
          seoOptimizationSuccessful: true,
          seoScore: seoResult.metadata.seoScore,
          searchEngineCompatibility: seoResult.metadata.searchEngineCompatibility,
          rankingPotential: seoResult.metadata.rankingPotential,
          featuredSnippetPotential: seoResult.metadata.featuredSnippetPotential,
          serpAnalysis: seoResult.seoOptimization.serpAnalysis,
          multiEngineOptimization: seoResult.seoOptimization.multiEngineOptimization,
          technicalOptimization: seoResult.seoOptimization.technicalOptimization,
          richResultsOptimization: seoResult.seoOptimization.richResultsOptimization,
          voiceSearchOptimization: seoResult.seoOptimization.voiceSearchOptimization,
          mobileOptimization: seoResult.seoOptimization.mobileOptimization,
          finalValidation: seoResult.seoOptimization.finalValidation
        }
      };

    } catch (error) {
      console.error('Advanced SEO optimization error:', error);
      return {
        content,
        seoOptimization: null,
        metadata: {
          seoOptimizationAttempted: true,
          seoOptimizationSuccessful: false,
          error: error.message
        }
      };
    }
  }

  // Perform comprehensive quality validation
  async performQualityValidation(content, keyword, competitorBenchmarks, semanticData, options) {
    try {
      console.log('Performing comprehensive content quality validation...');
      
      const validationResult = await this.contentQualityValidator.validateContentQuality(
        content, 
        keyword, 
        competitorBenchmarks, 
        {
          semanticData,
          industry: options.industry || 'general',
          contentType: options.contentType || 'guide',
          targetAudience: options.targetAudience || 'professionals'
        }
      );

      if (!validationResult.success) {
        console.warn('Content quality validation failed, proceeding with warnings');
        return {
          content,
          validation: null,
          metadata: {
            qualityValidationAttempted: true,
            qualityValidationSuccessful: false,
            error: validationResult.error
          }
        };
      }

      console.log('Content quality validation completed successfully:', {
        overallQualityScore: validationResult.validation.overallQualityScore,
        qualityGrade: validationResult.metadata.qualityGrade,
        passed: validationResult.validation.passed,
        criticalIssues: validationResult.metadata.criticalIssues.length,
        improvementAreas: validationResult.metadata.improvementAreas.length
      });

      return {
        content,
        validation: validationResult.validation,
        metadata: {
          qualityValidationAttempted: true,
          qualityValidationSuccessful: true,
          overallQualityScore: validationResult.validation.overallQualityScore,
          qualityGrade: validationResult.metadata.qualityGrade,
          passed: validationResult.validation.passed,
          criticalIssues: validationResult.metadata.criticalIssues.length,
          majorIssues: validationResult.metadata.majorIssues.length,
          minorIssues: validationResult.metadata.minorIssues.length,
          improvementAreas: validationResult.metadata.improvementAreas,
          validationReport: validationResult.validation.validationReport,
          recommendations: validationResult.validation.validationReport.recommendations,
          nextSteps: validationResult.validation.validationReport.nextSteps
        }
      };

    } catch (error) {
      console.error('Content quality validation error:', error);
      return {
        content,
        validation: null,
        metadata: {
          qualityValidationAttempted: true,
          qualityValidationSuccessful: false,
          error: error.message
        }
      };
    }
  }

  // Perform competitor benchmark matching for surgical precision
  async performBenchmarkMatching(content, keyword, competitorBenchmarks, options) {
    try {
      console.log('Performing competitor benchmark matching for surgical precision...');
      
      const matchingResult = await this.competitorBenchmarkMatcher.matchCompetitorBenchmarks(
        content, 
        keyword, 
        competitorBenchmarks, 
        {
          industry: options.industry || 'general',
          contentType: options.contentType || 'guide',
          targetAudience: options.targetAudience || 'professionals',
          location: options.location || 'United States'
        }
      );

      if (!matchingResult.success) {
        console.warn('Competitor benchmark matching failed, using original content');
        return {
          content,
          matching: null,
          metadata: {
            benchmarkMatchingAttempted: true,
            benchmarkMatchingSuccessful: false,
            error: matchingResult.error
          }
        };
      }

      console.log('Competitor benchmark matching completed successfully:', {
        matchingAccuracy: matchingResult.metadata.matchingAccuracy,
        benchmarksMatched: matchingResult.metadata.benchmarksMatched,
        totalBenchmarks: matchingResult.metadata.totalBenchmarks,
        perfectMatch: matchingResult.metadata.perfectMatch,
        adjustmentsApplied: matchingResult.metadata.adjustmentsApplied
      });

      return {
        content: matchingResult.content,
        matching: matchingResult.matching,
        metadata: {
          benchmarkMatchingAttempted: true,
          benchmarkMatchingSuccessful: true,
          matchingAccuracy: matchingResult.metadata.matchingAccuracy,
          benchmarksMatched: matchingResult.metadata.benchmarksMatched,
          totalBenchmarks: matchingResult.metadata.totalBenchmarks,
          perfectMatch: matchingResult.metadata.perfectMatch,
          adjustmentsApplied: matchingResult.metadata.adjustmentsApplied,
          originalMetrics: matchingResult.matching.originalMetrics,
          finalMetrics: matchingResult.matching.finalMetrics,
          adjustmentsMade: matchingResult.matching.adjustmentsMade,
          complianceValidation: matchingResult.matching.complianceValidation,
          matchingReport: matchingResult.matching.matchingReport
        }
      };

    } catch (error) {
      console.error('Competitor benchmark matching error:', error);
      return {
        content,
        matching: null,
        metadata: {
          benchmarkMatchingAttempted: true,
          benchmarkMatchingSuccessful: false,
          error: error.message
        }
      };
    }
  }

  // Perform multi-engine compatibility testing
  async performCompatibilityTesting(content, keyword, options) {
    try {
      console.log('Performing multi-engine compatibility testing...');
      
      const compatibilityResult = await this.multiEngineCompatibilityTester.testMultiEngineCompatibility(
        content, 
        keyword, 
        {
          engines: options.engines || ['openai', 'anthropic', 'google', 'microsoft', 'cohere'],
          industry: options.industry || 'general',
          contentType: options.contentType || 'guide',
          targetAudience: options.targetAudience || 'professionals',
          testDepth: options.testDepth || 'comprehensive'
        }
      );

      if (!compatibilityResult.success) {
        console.warn('Multi-engine compatibility testing failed, proceeding with warnings');
        return {
          content,
          compatibility: null,
          metadata: {
            compatibilityTestingAttempted: true,
            compatibilityTestingSuccessful: false,
            error: compatibilityResult.error
          }
        };
      }

      console.log('Multi-engine compatibility testing completed successfully:', {
        overallScore: compatibilityResult.compatibility.overallScore,
        grade: compatibilityResult.compatibility.grade,
        testedEngines: compatibilityResult.metadata.testedEngines,
        passedEngines: compatibilityResult.metadata.passedEngines,
        universalCompatibility: compatibilityResult.metadata.universalCompatibility
      });

      return {
        content,
        compatibility: compatibilityResult.compatibility,
        metadata: {
          compatibilityTestingAttempted: true,
          compatibilityTestingSuccessful: true,
          overallScore: compatibilityResult.compatibility.overallScore,
          grade: compatibilityResult.compatibility.grade,
          testedEngines: compatibilityResult.metadata.testedEngines,
          passedEngines: compatibilityResult.metadata.passedEngines,
          universalCompatibility: compatibilityResult.metadata.universalCompatibility,
          highestPerformingEngine: compatibilityResult.metadata.highestPerformingEngine,
          lowestPerformingEngine: compatibilityResult.metadata.lowestPerformingEngine,
          engineTestResults: compatibilityResult.compatibility.engineTestResults,
          structureCompatibility: compatibilityResult.compatibility.structureCompatibility,
          formatCompatibility: compatibilityResult.compatibility.formatCompatibility,
          semanticCompatibility: compatibilityResult.compatibility.semanticCompatibility,
          keywordCompatibility: compatibilityResult.compatibility.keywordCompatibility,
          readabilityCompatibility: compatibilityResult.compatibility.readabilityCompatibility,
          recommendations: compatibilityResult.compatibility.recommendations
        }
      };

    } catch (error) {
      console.error('Multi-engine compatibility testing error:', error);
      return {
        content,
        compatibility: null,
        metadata: {
          compatibilityTestingAttempted: true,
          compatibilityTestingSuccessful: false,
          error: error.message
        }
      };
    }
  }
}

module.exports = PrecisionContentGenerator;