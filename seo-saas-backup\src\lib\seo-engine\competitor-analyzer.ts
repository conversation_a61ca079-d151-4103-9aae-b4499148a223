// Advanced Competitor Analysis and Averaging System
// Enterprise-grade competitor benchmarking and metrics analysis

interface CompetitorData {
  url: string;
  title: string;
  metaDescription: string;
  wordCount: number;
  headingStructure: HeadingStructure;
  keywordMetrics: KeywordMetrics;
  technicalSEO: TechnicalSEOMetrics;
  contentQuality: ContentQualityMetrics;
  performanceMetrics: PerformanceMetrics;
  backlinks: BacklinkMetrics;
  socialSignals: SocialMetrics;
}

interface HeadingStructure {
  h1Count: number;
  h2Count: number;
  h3Count: number;
  h4Count: number;
  h5Count: number;
  h6Count: number;
  totalHeadings: number;
  keywordOptimizedHeadings: number;
  averageHeadingLength: number;
}

interface KeywordMetrics {
  primaryKeywordDensity: number;
  secondaryKeywordDensity: number;
  lsiKeywordCount: number;
  keywordVariations: number;
  keywordInTitle: boolean;
  keywordInMetaDescription: boolean;
  keywordInH1: boolean;
  keywordInFirstParagraph: boolean;
}

interface TechnicalSEOMetrics {
  loadTime: number;
  mobileOptimized: boolean;
  httpsEnabled: boolean;
  schemaMarkup: string[];
  internalLinks: number;
  externalLinks: number;
  imageCount: number;
  imagesWithAlt: number;
  canonicalTag: boolean;
  metaRobots: string;
}

interface ContentQualityMetrics {
  readabilityScore: number;
  sentenceLength: number;
  paragraphLength: number;
  uniqueWords: number;
  contentDepth: number;
  expertiseSignals: number;
  trustSignals: number;
  freshnessScore: number;
}

interface PerformanceMetrics {
  estimatedTraffic: number;
  estimatedRanking: number;
  clickThroughRate: number;
  bounceRate: number;
  timeOnPage: number;
  socialShares: number;
}

interface BacklinkMetrics {
  totalBacklinks: number;
  domainAuthority: number;
  referringDomains: number;
  qualityScore: number;
}

interface SocialMetrics {
  facebookShares: number;
  twitterShares: number;
  linkedinShares: number;
  totalShares: number;
}

interface CompetitorAverages {
  wordCount: CompetitorAverage;
  headingStructure: HeadingStructureAverage;
  keywordMetrics: KeywordMetricsAverage;
  technicalSEO: TechnicalSEOAverage;
  contentQuality: ContentQualityAverage;
  performance: PerformanceAverage;
}

interface CompetitorAverage {
  mean: number;
  median: number;
  min: number;
  max: number;
  standardDeviation: number;
  percentile25: number;
  percentile75: number;
  topPerformer: number;
}

interface HeadingStructureAverage {
  totalHeadings: CompetitorAverage;
  h1Count: CompetitorAverage;
  h2Count: CompetitorAverage;
  h3Count: CompetitorAverage;
  keywordOptimizedHeadings: CompetitorAverage;
  averageHeadingLength: CompetitorAverage;
}

interface KeywordMetricsAverage {
  primaryKeywordDensity: CompetitorAverage;
  secondaryKeywordDensity: CompetitorAverage;
  lsiKeywordCount: CompetitorAverage;
  keywordVariations: CompetitorAverage;
  keywordInTitlePercentage: number;
  keywordInH1Percentage: number;
}

interface TechnicalSEOAverage {
  loadTime: CompetitorAverage;
  internalLinks: CompetitorAverage;
  externalLinks: CompetitorAverage;
  imageCount: CompetitorAverage;
  imagesWithAltPercentage: number;
  mobileOptimizedPercentage: number;
  httpsEnabledPercentage: number;
}

interface ContentQualityAverage {
  readabilityScore: CompetitorAverage;
  sentenceLength: CompetitorAverage;
  paragraphLength: CompetitorAverage;
  uniqueWords: CompetitorAverage;
  contentDepth: CompetitorAverage;
}

interface PerformanceAverage {
  estimatedTraffic: CompetitorAverage;
  estimatedRanking: CompetitorAverage;
  clickThroughRate: CompetitorAverage;
  socialShares: CompetitorAverage;
}

interface CompetitorInsights {
  averages: CompetitorAverages;
  topPerformers: CompetitorData[];
  contentGaps: string[];
  opportunities: OpportunityAnalysis[];
  benchmarkTargets: BenchmarkTargets;
}

interface OpportunityAnalysis {
  type: 'content' | 'technical' | 'keyword' | 'structure';
  opportunity: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'high' | 'medium' | 'low';
  description: string;
  competitorExample: string;
}

interface BenchmarkTargets {
  wordCountTarget: number;
  headingCountTarget: number;
  keywordDensityTarget: number;
  readabilityTarget: number;
  technicalScoreTarget: number;
}

export class CompetitorAnalyzer {
  private primaryKeyword: string;
  private secondaryKeywords: string[];
  private industry: string;

  constructor(primaryKeyword: string, secondaryKeywords: string[] = [], industry: string = 'general') {
    this.primaryKeyword = primaryKeyword.toLowerCase();
    this.secondaryKeywords = secondaryKeywords.map(k => k.toLowerCase());
    this.industry = industry.toLowerCase();
  }

  /**
   * Analyze competitors and calculate comprehensive averages
   */
  analyzeCompetitors(competitorData: CompetitorData[]): CompetitorInsights {
    if (competitorData.length === 0) {
      throw new Error('No competitor data provided');
    }

    // Calculate comprehensive averages
    const averages = this.calculateComprehensiveAverages(competitorData);
    
    // Identify top performers
    const topPerformers = this.identifyTopPerformers(competitorData);
    
    // Find content gaps
    const contentGaps = this.findContentGaps(competitorData);
    
    // Identify opportunities
    const opportunities = this.identifyOpportunities(competitorData, averages);
    
    // Calculate benchmark targets
    const benchmarkTargets = this.calculateBenchmarkTargets(averages, topPerformers);

    return {
      averages,
      topPerformers,
      contentGaps,
      opportunities,
      benchmarkTargets
    };
  }

  /**
   * Calculate comprehensive statistical averages
   */
  private calculateComprehensiveAverages(competitors: CompetitorData[]): CompetitorAverages {
    return {
      wordCount: this.calculateStatistics(competitors.map(c => c.wordCount)),
      headingStructure: {
        totalHeadings: this.calculateStatistics(competitors.map(c => c.headingStructure.totalHeadings)),
        h1Count: this.calculateStatistics(competitors.map(c => c.headingStructure.h1Count)),
        h2Count: this.calculateStatistics(competitors.map(c => c.headingStructure.h2Count)),
        h3Count: this.calculateStatistics(competitors.map(c => c.headingStructure.h3Count)),
        keywordOptimizedHeadings: this.calculateStatistics(competitors.map(c => c.headingStructure.keywordOptimizedHeadings)),
        averageHeadingLength: this.calculateStatistics(competitors.map(c => c.headingStructure.averageHeadingLength))
      },
      keywordMetrics: {
        primaryKeywordDensity: this.calculateStatistics(competitors.map(c => c.keywordMetrics.primaryKeywordDensity)),
        secondaryKeywordDensity: this.calculateStatistics(competitors.map(c => c.keywordMetrics.secondaryKeywordDensity)),
        lsiKeywordCount: this.calculateStatistics(competitors.map(c => c.keywordMetrics.lsiKeywordCount)),
        keywordVariations: this.calculateStatistics(competitors.map(c => c.keywordMetrics.keywordVariations)),
        keywordInTitlePercentage: this.calculatePercentage(competitors, c => c.keywordMetrics.keywordInTitle),
        keywordInH1Percentage: this.calculatePercentage(competitors, c => c.keywordMetrics.keywordInH1)
      },
      technicalSEO: {
        loadTime: this.calculateStatistics(competitors.map(c => c.technicalSEO.loadTime)),
        internalLinks: this.calculateStatistics(competitors.map(c => c.technicalSEO.internalLinks)),
        externalLinks: this.calculateStatistics(competitors.map(c => c.technicalSEO.externalLinks)),
        imageCount: this.calculateStatistics(competitors.map(c => c.technicalSEO.imageCount)),
        imagesWithAltPercentage: this.calculateAveragePercentage(competitors.map(c => 
          c.technicalSEO.imageCount > 0 ? (c.technicalSEO.imagesWithAlt / c.technicalSEO.imageCount) * 100 : 0
        )),
        mobileOptimizedPercentage: this.calculatePercentage(competitors, c => c.technicalSEO.mobileOptimized),
        httpsEnabledPercentage: this.calculatePercentage(competitors, c => c.technicalSEO.httpsEnabled)
      },
      contentQuality: {
        readabilityScore: this.calculateStatistics(competitors.map(c => c.contentQuality.readabilityScore)),
        sentenceLength: this.calculateStatistics(competitors.map(c => c.contentQuality.sentenceLength)),
        paragraphLength: this.calculateStatistics(competitors.map(c => c.contentQuality.paragraphLength)),
        uniqueWords: this.calculateStatistics(competitors.map(c => c.contentQuality.uniqueWords)),
        contentDepth: this.calculateStatistics(competitors.map(c => c.contentQuality.contentDepth))
      },
      performance: {
        estimatedTraffic: this.calculateStatistics(competitors.map(c => c.performanceMetrics.estimatedTraffic)),
        estimatedRanking: this.calculateStatistics(competitors.map(c => c.performanceMetrics.estimatedRanking)),
        clickThroughRate: this.calculateStatistics(competitors.map(c => c.performanceMetrics.clickThroughRate)),
        socialShares: this.calculateStatistics(competitors.map(c => c.performanceMetrics.socialShares))
      }
    };
  }

  /**
   * Calculate comprehensive statistics for a dataset
   */
  private calculateStatistics(values: number[]): CompetitorAverage {
    if (values.length === 0) {
      return { mean: 0, median: 0, min: 0, max: 0, standardDeviation: 0, percentile25: 0, percentile75: 0, topPerformer: 0 };
    }

    const sorted = [...values].sort((a, b) => a - b);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const median = this.calculateMedian(sorted);
    const min = Math.min(...values);
    const max = Math.max(...values);
    const standardDeviation = this.calculateStandardDeviation(values, mean);
    const percentile25 = this.calculatePercentile(sorted, 25);
    const percentile75 = this.calculatePercentile(sorted, 75);
    const topPerformer = this.calculatePercentile(sorted, 90);

    return {
      mean: Math.round(mean * 100) / 100,
      median: Math.round(median * 100) / 100,
      min,
      max,
      standardDeviation: Math.round(standardDeviation * 100) / 100,
      percentile25: Math.round(percentile25 * 100) / 100,
      percentile75: Math.round(percentile75 * 100) / 100,
      topPerformer: Math.round(topPerformer * 100) / 100
    };
  }

  /**
   * Identify top performing competitors
   */
  private identifyTopPerformers(competitors: CompetitorData[]): CompetitorData[] {
    // Score competitors based on multiple factors
    const scoredCompetitors = competitors.map(competitor => ({
      ...competitor,
      score: this.calculateCompetitorScore(competitor)
    }));

    // Return top 3 performers
    return scoredCompetitors
      .sort((a, b) => b.score - a.score)
      .slice(0, 3)
      .map(({ score, ...competitor }) => competitor);
  }

  /**
   * Calculate competitor performance score
   */
  private calculateCompetitorScore(competitor: CompetitorData): number {
    let score = 0;

    // Content quality (30%)
    score += (competitor.contentQuality.readabilityScore / 100) * 30;
    
    // SEO optimization (25%)
    const seoScore = (
      (competitor.keywordMetrics.primaryKeywordDensity > 0 ? 20 : 0) +
      (competitor.keywordMetrics.keywordInTitle ? 20 : 0) +
      (competitor.keywordMetrics.keywordInH1 ? 20 : 0) +
      (competitor.headingStructure.keywordOptimizedHeadings > 0 ? 20 : 0) +
      (competitor.keywordMetrics.lsiKeywordCount > 5 ? 20 : 0)
    ) / 5;
    score += (seoScore / 100) * 25;
    
    // Technical performance (20%)
    const techScore = (
      (competitor.technicalSEO.loadTime < 3 ? 25 : 0) +
      (competitor.technicalSEO.mobileOptimized ? 25 : 0) +
      (competitor.technicalSEO.httpsEnabled ? 25 : 0) +
      (competitor.technicalSEO.schemaMarkup.length > 0 ? 25 : 0)
    ) / 4;
    score += (techScore / 100) * 20;
    
    // Content depth (15%)
    const depthScore = Math.min(100, (competitor.wordCount / 2000) * 100);
    score += (depthScore / 100) * 15;
    
    // Performance metrics (10%)
    const perfScore = Math.min(100, competitor.performanceMetrics.estimatedTraffic / 1000);
    score += (perfScore / 100) * 10;

    return Math.round(score * 100) / 100;
  }

  /**
   * Find content gaps and opportunities
   */
  private findContentGaps(competitors: CompetitorData[]): string[] {
    const gaps: string[] = [];
    
    // Analyze common patterns
    const avgWordCount = competitors.reduce((sum, c) => sum + c.wordCount, 0) / competitors.length;
    const avgHeadings = competitors.reduce((sum, c) => sum + c.headingStructure.totalHeadings, 0) / competitors.length;
    
    if (avgWordCount > 2000) {
      gaps.push('Long-form content (2000+ words) is standard in this niche');
    }
    
    if (avgHeadings > 10) {
      gaps.push('Extensive heading structure (10+ headings) is common among competitors');
    }
    
    // Check for technical gaps
    const mobileOptimizedPercentage = this.calculatePercentage(competitors, c => c.technicalSEO.mobileOptimized);
    if (mobileOptimizedPercentage > 80) {
      gaps.push('Mobile optimization is critical - 80%+ of competitors are mobile-optimized');
    }
    
    // Check for schema markup usage
    const schemaUsage = competitors.filter(c => c.technicalSEO.schemaMarkup.length > 0).length;
    if (schemaUsage > competitors.length * 0.6) {
      gaps.push('Schema markup is widely used by competitors for better SERP features');
    }

    return gaps;
  }

  /**
   * Identify optimization opportunities
   */
  private identifyOpportunities(competitors: CompetitorData[], averages: CompetitorAverages): OpportunityAnalysis[] {
    const opportunities: OpportunityAnalysis[] = [];

    // Content length opportunity
    if (averages.wordCount.topPerformer > averages.wordCount.mean * 1.5) {
      opportunities.push({
        type: 'content',
        opportunity: 'Content Length Optimization',
        impact: 'high',
        effort: 'medium',
        description: `Top performers use ${Math.round(averages.wordCount.topPerformer)} words vs average of ${Math.round(averages.wordCount.mean)}`,
        competitorExample: `Best performing content has ${Math.round(averages.wordCount.topPerformer)} words`
      });
    }

    // Heading structure opportunity
    if (averages.headingStructure.totalHeadings.topPerformer > averages.headingStructure.totalHeadings.mean * 1.3) {
      opportunities.push({
        type: 'structure',
        opportunity: 'Enhanced Heading Structure',
        impact: 'medium',
        effort: 'low',
        description: `Top performers use ${Math.round(averages.headingStructure.totalHeadings.topPerformer)} headings vs average of ${Math.round(averages.headingStructure.totalHeadings.mean)}`,
        competitorExample: `Leading competitors structure content with ${Math.round(averages.headingStructure.totalHeadings.topPerformer)} headings`
      });
    }

    // Technical SEO opportunity
    if (averages.technicalSEO.mobileOptimizedPercentage > 90) {
      opportunities.push({
        type: 'technical',
        opportunity: 'Mobile Optimization',
        impact: 'high',
        effort: 'medium',
        description: `${averages.technicalSEO.mobileOptimizedPercentage}% of competitors are mobile-optimized`,
        competitorExample: 'Mobile optimization is table stakes in this competitive landscape'
      });
    }

    return opportunities.sort((a, b) => {
      const impactScore = { high: 3, medium: 2, low: 1 };
      const effortScore = { low: 3, medium: 2, high: 1 };
      
      const aScore = impactScore[a.impact] * effortScore[a.effort];
      const bScore = impactScore[b.impact] * effortScore[b.effort];
      
      return bScore - aScore;
    });
  }

  /**
   * Calculate benchmark targets based on competitor analysis
   */
  private calculateBenchmarkTargets(averages: CompetitorAverages, topPerformers: CompetitorData[]): BenchmarkTargets {
    // Target to beat the 75th percentile or match top performers
    return {
      wordCountTarget: Math.max(averages.wordCount.percentile75, averages.wordCount.topPerformer * 0.9),
      headingCountTarget: Math.max(averages.headingStructure.totalHeadings.percentile75, averages.headingStructure.totalHeadings.topPerformer * 0.9),
      keywordDensityTarget: Math.min(3.0, averages.keywordMetrics.primaryKeywordDensity.percentile75),
      readabilityTarget: Math.max(70, averages.contentQuality.readabilityScore.percentile75),
      technicalScoreTarget: 90 // Aim for 90% technical optimization
    };
  }

  // Statistical helper methods
  private calculateMedian(sortedValues: number[]): number {
    const mid = Math.floor(sortedValues.length / 2);
    return sortedValues.length % 2 === 0
      ? (sortedValues[mid - 1] + sortedValues[mid]) / 2
      : sortedValues[mid];
  }

  private calculatePercentile(sortedValues: number[], percentile: number): number {
    const index = (percentile / 100) * (sortedValues.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    const weight = index - lower;
    
    return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
  }

  private calculateStandardDeviation(values: number[], mean: number): number {
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((sum, sq) => sum + sq, 0) / values.length;
    return Math.sqrt(avgSquaredDiff);
  }

  private calculatePercentage(competitors: CompetitorData[], predicate: (c: CompetitorData) => boolean): number {
    const count = competitors.filter(predicate).length;
    return Math.round((count / competitors.length) * 100);
  }

  private calculateAveragePercentage(percentages: number[]): number {
    return Math.round(percentages.reduce((sum, p) => sum + p, 0) / percentages.length);
  }
}

export const competitorAnalyzer = new CompetitorAnalyzer('', [], 'general');

// Export types for use in other modules
export type {
  CompetitorData,
  CompetitorAverages,
  CompetitorInsights,
  OpportunityAnalysis,
  BenchmarkTargets,
  HeadingStructure,
  KeywordMetrics,
  TechnicalSEOMetrics,
  ContentQualityMetrics,
  PerformanceMetrics
};
