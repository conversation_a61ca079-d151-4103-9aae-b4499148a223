<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Generator - SEO Pro</title>
    <meta name="description" content="Generate high-quality, SEO-optimized content with AI">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
    <link href="css/animations.css" rel="stylesheet">
    <link href="css/professional-fixes.css" rel="stylesheet">
    
    <style>
        /* Content Generator Layout - Professional & Wide */
        .page-container {
            min-height: 100vh;
            background: #f9fafb;
        }

        .content-generator-wrapper {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .generator-header {
            text-align: center;
            margin-bottom: 3rem;
            background: white;
            padding: 3rem 2rem;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border: 1px solid #f3f4f6;
        }

        .generator-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #111827;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .generator-subtitle {
            font-size: 1.25rem;
            color: #6b7280;
            margin: 0;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Step Wizard - Wide Layout */
        .step-wizard {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border: 1px solid #f3f4f6;
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            padding: 2rem;
            background: #f9fafb;
            border-bottom: 1px solid #f3f4f6;
        }

        .step {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            font-weight: 600;
            margin: 0 1rem;
            background: #f3f4f6;
            color: #6b7280;
            border: 2px solid #f3f4f6;
            position: relative;
            transition: all 0.3s ease;
        }

        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 2rem;
            height: 2px;
            background: #f3f4f6;
            transform: translateY(-50%);
            z-index: -1;
        }

        .step:last-child::after {
            display: none;
        }

        .step.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
            transform: scale(1.1);
        }

        .step.completed {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }

        .step.completed::after {
            background: #10b981;
        }

        /* Step Content - Wide Forms */
        .step-content {
            padding: 3rem;
            display: none;
        }

        .step-content.active {
            display: block;
        }

        .step-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #111827;
            margin-bottom: 0.5rem;
            text-align: center;
        }

        .step-description {
            font-size: 1rem;
            color: #6b7280;
            text-align: center;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Wide Form Layout */
        .step-form {
            max-width: 1000px;
            margin: 0 auto;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-group-full {
            grid-column: 1 / -1;
            margin-bottom: 1.5rem;
        }

        .form-input-full, .form-textarea-full, .form-select-full {
            width: 100%;
            padding: 1rem 1.25rem;
            font-size: 0.875rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            min-height: 3rem;
            background: white;
            transition: all 0.3s ease;
        }

        .form-input-full:focus, .form-textarea-full:focus, .form-select-full:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }

        .form-textarea-full {
            min-height: 8rem;
            resize: vertical;
        }

        .form-label-full {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        /* Navigation Buttons */
        .step-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2rem 3rem;
            background: #f9fafb;
            border-top: 1px solid #f3f4f6;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
        }

        /* Results Section */
        .results-section {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border: 1px solid #f3f4f6;
            overflow: hidden;
            margin-top: 2rem;
            display: none;
        }

        .results-section.active {
            display: block;
        }

        .results-header {
            padding: 2rem;
            background: #f9fafb;
            border-bottom: 1px solid #f3f4f6;
            text-align: center;
        }

        .results-content {
            padding: 2rem;
        }

        .generated-content {
            background: #f9fafb;
            border: 1px solid #f3f4f6;
            border-radius: 0.5rem;
            padding: 2rem;
            font-family: 'Georgia', serif;
            line-height: 1.8;
            color: #374151;
            min-height: 400px;
        }

        /* Content Actions */
        .content-actions {
            display: flex;
            justify-content: center;
            gap: 1rem;
            padding: 2rem;
            background: #f9fafb;
            border-top: 1px solid #f3f4f6;
            flex-wrap: wrap;
        }

        /* Progress Indicator */
        .generation-progress {
            text-align: center;
            padding: 3rem;
            display: none;
        }

        .generation-progress.active {
            display: block;
        }

        .progress-circle {
            width: 4rem;
            height: 4rem;
            border: 3px solid #f3f4f6;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            margin: 0 auto 1rem;
            animation: spin 1s linear infinite;
        }

        .progress-text {
            font-size: 1.125rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .progress-step {
            font-size: 0.875rem;
            color: #9ca3af;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .content-generator-wrapper {
                padding: 1rem;
            }

            .generator-header {
                padding: 2rem 1rem;
            }

            .generator-title {
                font-size: 2rem;
            }

            .step-content {
                padding: 2rem 1rem;
            }

            .step-navigation {
                padding: 1.5rem 1rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .step {
                width: 2.5rem;
                height: 2.5rem;
                margin: 0 0.5rem;
            }

            .step::after {
                width: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Include the same header and sidebar from dashboard.html -->
    <header class="header">
        <nav class="nav container-fluid px-6">
            <div class="flex items-center">
                <button class="mobile-menu-btn mr-4 lg:hidden" onclick="toggleSidebar()" aria-label="Toggle sidebar">
                    <span class="hamburger"></span>
                </button>
                <a href="index.html" class="logo">
                    <span class="logo-text">SEO Pro</span>
                </a>
            </div>
            
            <div class="flex items-center gap-4">
                <div class="hidden md:block">
                    <div class="input-group">
                        <input type="search" class="form-input" placeholder="Search..." style="width: 300px;">
                    </div>
                </div>
                
                <button class="relative p-2 text-gray-600 hover:text-gray-900">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                    </svg>
                    <span class="absolute top-0 right-0 w-2 h-2 bg-error rounded-full"></span>
                </button>
                
                <div class="relative">
                    <button class="flex items-center gap-2 p-2" onclick="toggleUserMenu()">
                        <div class="w-8 h-8 bg-gradient rounded-full flex items-center justify-center text-white font-semibold">
                            JD
                        </div>
                        <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    
                    <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 hidden">
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Profile</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Settings</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Billing</a>
                        <hr class="my-2">
                        <a href="login.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Sign Out</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <!-- Main Content - Full Width Layout -->
    <div class="page-container">
        <div class="content-generator-wrapper">
            <!-- Header -->
            <div class="generator-header">
                <h1 class="generator-title">AI Content Generator</h1>
                <p class="generator-subtitle">Create SEO-optimized content that ranks on the first page with our advanced AI technology</p>
            </div>

            <!-- Step Wizard -->
            <div class="step-wizard">
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step active" data-step="1">1</div>
                    <div class="step" data-step="2">2</div>
                    <div class="step" data-step="3">3</div>
                    <div class="step" data-step="4">4</div>
                </div>

                <!-- Step 1: Project Details -->
                <div class="step-content active" id="step-1">
                    <div class="step-title">Project Details</div>
                    <div class="step-description">Tell us about your content project and target audience</div>

                    <div class="step-form">
                        <div class="form-group-full">
                            <label class="form-label-full">Target URL *</label>
                            <input type="url" class="form-input-full" placeholder="https://example.com/your-page" required>
                        </div>

                        <div class="form-grid">
                            <div>
                                <label class="form-label-full">Industry/Niche *</label>
                                <select class="form-select-full" required>
                                    <option value="">Select your industry</option>
                                    <option value="technology">Technology</option>
                                    <option value="healthcare">Healthcare</option>
                                    <option value="finance">Finance</option>
                                    <option value="ecommerce">E-commerce</option>
                                    <option value="education">Education</option>
                                    <option value="travel">Travel</option>
                                    <option value="food">Food & Beverage</option>
                                    <option value="fitness">Fitness & Health</option>
                                    <option value="real-estate">Real Estate</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>

                            <div>
                                <label class="form-label-full">Content Type *</label>
                                <select class="form-select-full" required>
                                    <option value="">Select content type</option>
                                    <option value="blog-post">Blog Post</option>
                                    <option value="product-page">Product Page</option>
                                    <option value="landing-page">Landing Page</option>
                                    <option value="category-page">Category Page</option>
                                    <option value="how-to-guide">How-to Guide</option>
                                    <option value="listicle">Listicle</option>
                                    <option value="review">Review</option>
                                    <option value="comparison">Comparison</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group-full">
                            <label class="form-label-full">Target Audience</label>
                            <textarea class="form-textarea-full" placeholder="Describe your target audience (e.g., small business owners, tech enthusiasts, fitness beginners...)"></textarea>
                        </div>

                        <div class="form-grid">
                            <div>
                                <label class="form-label-full">Content Length</label>
                                <select class="form-select-full">
                                    <option value="short">Short (500-800 words)</option>
                                    <option value="medium" selected>Medium (800-1200 words)</option>
                                    <option value="long">Long (1200-2000 words)</option>
                                    <option value="very-long">Very Long (2000+ words)</option>
                                </select>
                            </div>

                            <div>
                                <label class="form-label-full">Writing Tone</label>
                                <select class="form-select-full">
                                    <option value="professional" selected>Professional</option>
                                    <option value="casual">Casual</option>
                                    <option value="conversational">Conversational</option>
                                    <option value="technical">Technical</option>
                                    <option value="friendly">Friendly</option>
                                    <option value="authoritative">Authoritative</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Keywords & SEO -->
                <div class="step-content" id="step-2">
                    <div class="step-title">Keywords & SEO Strategy</div>
                    <div class="step-description">Define your target keywords and SEO objectives</div>

                    <div class="step-form">
                        <div class="form-group-full">
                            <label class="form-label-full">Primary Keyword *</label>
                            <input type="text" class="form-input-full" placeholder="Enter your main target keyword" required>
                        </div>

                        <div class="form-group-full">
                            <label class="form-label-full">Secondary Keywords</label>
                            <textarea class="form-textarea-full" placeholder="Enter related keywords, separated by commas (e.g., SEO tools, content optimization, keyword research)"></textarea>
                        </div>

                        <div class="form-grid">
                            <div>
                                <label class="form-label-full">Search Intent</label>
                                <select class="form-select-full">
                                    <option value="informational" selected>Informational</option>
                                    <option value="commercial">Commercial</option>
                                    <option value="transactional">Transactional</option>
                                    <option value="navigational">Navigational</option>
                                </select>
                            </div>

                            <div>
                                <label class="form-label-full">Competition Level</label>
                                <select class="form-select-full">
                                    <option value="low">Low Competition</option>
                                    <option value="medium" selected>Medium Competition</option>
                                    <option value="high">High Competition</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group-full">
                            <label class="form-label-full">Competitor URLs (Optional)</label>
                            <textarea class="form-textarea-full" placeholder="Enter competitor URLs to analyze, one per line"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Content Brief -->
                <div class="step-content" id="step-3">
                    <div class="step-title">Content Brief & Requirements</div>
                    <div class="step-description">Provide detailed instructions for your content</div>

                    <div class="step-form">
                        <div class="form-group-full">
                            <label class="form-label-full">Content Title/Topic *</label>
                            <input type="text" class="form-input-full" placeholder="Enter your content title or main topic" required>
                        </div>

                        <div class="form-group-full">
                            <label class="form-label-full">Content Brief *</label>
                            <textarea class="form-textarea-full" placeholder="Describe what you want the content to cover. Include key points, structure preferences, and any specific requirements..." required style="min-height: 120px;"></textarea>
                        </div>

                        <div class="form-group-full">
                            <label class="form-label-full">Key Points to Include</label>
                            <textarea class="form-textarea-full" placeholder="List specific points, statistics, or information that must be included in the content"></textarea>
                        </div>

                        <div class="form-grid">
                            <div>
                                <label class="form-label-full">Include Images</label>
                                <select class="form-select-full">
                                    <option value="yes" selected>Yes, suggest image placements</option>
                                    <option value="no">No images needed</option>
                                </select>
                            </div>

                            <div>
                                <label class="form-label-full">Call-to-Action</label>
                                <select class="form-select-full">
                                    <option value="yes" selected>Include CTA</option>
                                    <option value="no">No CTA needed</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group-full">
                            <label class="form-label-full">Additional Instructions</label>
                            <textarea class="form-textarea-full" placeholder="Any other specific requirements, style preferences, or constraints..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Review & Generate -->
                <div class="step-content" id="step-4">
                    <div class="step-title">Review & Generate</div>
                    <div class="step-description">Review your settings and generate your content</div>

                    <div class="step-form">
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-semibold mb-4">Content Summary</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <strong>Target URL:</strong> <span id="review-url">-</span>
                                </div>
                                <div>
                                    <strong>Industry:</strong> <span id="review-industry">-</span>
                                </div>
                                <div>
                                    <strong>Content Type:</strong> <span id="review-type">-</span>
                                </div>
                                <div>
                                    <strong>Length:</strong> <span id="review-length">-</span>
                                </div>
                                <div>
                                    <strong>Primary Keyword:</strong> <span id="review-keyword">-</span>
                                </div>
                                <div>
                                    <strong>Tone:</strong> <span id="review-tone">-</span>
                                </div>
                            </div>
                            <div class="mt-4">
                                <strong>Content Title:</strong> <span id="review-title">-</span>
                            </div>
                        </div>

                        <div class="text-center mt-6">
                            <button class="btn btn-primary btn-lg" onclick="generateContent()">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                Generate Content
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="step-navigation">
                    <button class="btn btn-secondary" id="prev-btn" onclick="previousStep()" style="display: none;">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        Previous
                    </button>

                    <div class="nav-buttons">
                        <button class="btn btn-primary" id="next-btn" onclick="nextStep()">
                            Next
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Generation Progress -->
            <div class="generation-progress" id="generation-progress">
                <div class="progress-circle"></div>
                <div class="progress-text">Generating your content...</div>
                <div class="progress-step" id="progress-step">Analyzing keywords and competition</div>
            </div>

            <!-- Results Section -->
            <div class="results-section" id="results-section">
                <div class="results-header">
                    <h2 class="text-xl font-semibold text-gray-900 mb-2">Generated Content</h2>
                    <p class="text-gray-600">Your SEO-optimized content is ready for review and editing</p>
                </div>

                <div class="results-content">
                    <div class="generated-content" id="generated-content" contenteditable="true">
                        <!-- Generated content will appear here -->
                    </div>
                </div>

                <div class="content-actions">
                    <button class="btn btn-secondary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        Copy Content
                    </button>

                    <button class="btn btn-secondary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Export as Word
                    </button>

                    <button class="btn btn-secondary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Save as Draft
                    </button>

                    <button class="btn btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Analyze SEO
                    </button>

                    <button class="btn btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Regenerate
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        const totalSteps = 4;

        function nextStep() {
            if (currentStep < totalSteps) {
                // Validate current step
                if (validateStep(currentStep)) {
                    // Hide current step
                    document.getElementById(`step-${currentStep}`).classList.remove('active');
                    document.querySelector(`[data-step="${currentStep}"]`).classList.remove('active');
                    document.querySelector(`[data-step="${currentStep}"]`).classList.add('completed');

                    // Show next step
                    currentStep++;
                    document.getElementById(`step-${currentStep}`).classList.add('active');
                    document.querySelector(`[data-step="${currentStep}"]`).classList.add('active');

                    // Update navigation buttons
                    updateNavigation();

                    // Update review if on step 4
                    if (currentStep === 4) {
                        updateReview();
                    }
                }
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                // Hide current step
                document.getElementById(`step-${currentStep}`).classList.remove('active');
                document.querySelector(`[data-step="${currentStep}"]`).classList.remove('active');

                // Show previous step
                currentStep--;
                document.getElementById(`step-${currentStep}`).classList.add('active');
                document.querySelector(`[data-step="${currentStep}"]`).classList.add('active');
                document.querySelector(`[data-step="${currentStep}"]`).classList.remove('completed');

                // Update navigation buttons
                updateNavigation();
            }
        }

        function updateNavigation() {
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');

            // Show/hide previous button
            if (currentStep === 1) {
                prevBtn.style.display = 'none';
            } else {
                prevBtn.style.display = 'block';
            }

            // Update next button text
            if (currentStep === totalSteps) {
                nextBtn.style.display = 'none';
            } else {
                nextBtn.style.display = 'block';
                nextBtn.innerHTML = `
                    Next
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                `;
            }
        }

        function validateStep(step) {
            const stepElement = document.getElementById(`step-${step}`);
            const requiredFields = stepElement.querySelectorAll('[required]');

            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    field.focus();
                    field.style.borderColor = '#ef4444';
                    setTimeout(() => {
                        field.style.borderColor = '';
                    }, 3000);
                    return false;
                }
            }
            return true;
        }

        function updateReview() {
            // Get form values
            const targetUrl = document.querySelector('#step-1 input[type="url"]').value;
            const industry = document.querySelector('#step-1 select').value;
            const contentType = document.querySelector('#step-1 select:nth-of-type(2)').value;
            const length = document.querySelector('#step-1 select:nth-of-type(3)').value;
            const tone = document.querySelector('#step-1 select:nth-of-type(4)').value;
            const keyword = document.querySelector('#step-2 input[type="text"]').value;
            const title = document.querySelector('#step-3 input[type="text"]').value;

            // Update review elements
            document.getElementById('review-url').textContent = targetUrl || '-';
            document.getElementById('review-industry').textContent = industry || '-';
            document.getElementById('review-type').textContent = contentType || '-';
            document.getElementById('review-length').textContent = length || '-';
            document.getElementById('review-tone').textContent = tone || '-';
            document.getElementById('review-keyword').textContent = keyword || '-';
            document.getElementById('review-title').textContent = title || '-';
        }

        function generateContent() {
            // Hide wizard and show progress
            document.querySelector('.step-wizard').style.display = 'none';
            document.getElementById('generation-progress').classList.add('active');

            // Simulate generation process
            const steps = [
                'Analyzing keywords and competition...',
                'Researching target audience...',
                'Creating content outline...',
                'Generating SEO-optimized content...',
                'Optimizing for readability...',
                'Finalizing content structure...'
            ];

            let stepIndex = 0;
            const progressInterval = setInterval(() => {
                if (stepIndex < steps.length) {
                    document.getElementById('progress-step').textContent = steps[stepIndex];
                    stepIndex++;
                } else {
                    clearInterval(progressInterval);
                    showResults();
                }
            }, 1500);
        }

        function showResults() {
            // Hide progress and show results
            document.getElementById('generation-progress').classList.remove('active');
            document.getElementById('results-section').classList.add('active');

            // Sample generated content
            const sampleContent = `
                <h1>The Ultimate Guide to SEO Content Marketing in 2024</h1>

                <p>In today's digital landscape, creating content that both engages your audience and ranks well in search engines is more crucial than ever. This comprehensive guide will walk you through the essential strategies for successful SEO content marketing.</p>

                <h2>What is SEO Content Marketing?</h2>

                <p>SEO content marketing is the practice of creating valuable, relevant content that is optimized for search engines while providing genuine value to your target audience. It combines the art of storytelling with the science of search engine optimization.</p>

                <h2>Key Benefits of SEO Content Marketing</h2>

                <ul>
                    <li><strong>Increased Organic Traffic:</strong> Well-optimized content helps your website rank higher in search results</li>
                    <li><strong>Better User Engagement:</strong> Quality content keeps visitors on your site longer</li>
                    <li><strong>Brand Authority:</strong> Consistent, valuable content establishes your expertise</li>
                    <li><strong>Cost-Effective Marketing:</strong> Organic traffic provides long-term value without ongoing ad spend</li>
                </ul>

                <h2>Essential SEO Content Marketing Strategies</h2>

                <h3>1. Keyword Research and Planning</h3>
                <p>Start with comprehensive keyword research to understand what your audience is searching for. Use tools like Google Keyword Planner, SEMrush, or Ahrefs to identify high-value keywords with good search volume and manageable competition.</p>

                <h3>2. Create High-Quality, Original Content</h3>
                <p>Focus on creating content that provides real value to your readers. This means going beyond surface-level information and providing actionable insights, detailed explanations, and unique perspectives.</p>

                <h3>3. Optimize for User Intent</h3>
                <p>Understanding search intent is crucial. Are users looking for information, trying to make a purchase, or seeking a specific website? Align your content with the intent behind your target keywords.</p>

                <h2>Measuring Success</h2>

                <p>Track key metrics such as organic traffic growth, keyword rankings, time on page, and conversion rates to measure the effectiveness of your SEO content marketing efforts.</p>

                <h2>Conclusion</h2>

                <p>SEO content marketing is a powerful strategy that can drive sustainable growth for your business. By focusing on creating valuable content that serves your audience while following SEO best practices, you can build a strong online presence that generates consistent organic traffic and leads.</p>
            `;

            document.getElementById('generated-content').innerHTML = sampleContent;
        }

        function toggleUserMenu() {
            const userMenu = document.getElementById('userMenu');
            userMenu.classList.toggle('hidden');
        }

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('userMenu');
            const userMenuButton = event.target.closest('button[onclick="toggleUserMenu()"]');

            if (!userMenu.contains(event.target) && !userMenuButton) {
                userMenu.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
                        <span class="form-step-label">Project Setup</span>
                    </div>
                    <div class="form-step" id="step2">
                        <div class="form-step-number">2</div>
                        <span class="form-step-label">Keywords</span>
                    </div>
                    <div class="form-step" id="step3">
                        <div class="form-step-number">3</div>
                        <span class="form-step-label">Preferences</span>
                    </div>
                    <div class="form-step" id="step4">
                        <div class="form-step-number">4</div>
                        <span class="form-step-label">Generate</span>
                    </div>
                </div>
                
                <!-- Form Container -->
                <div class="max-w-4xl mx-auto">
                    <form id="contentGeneratorForm" onsubmit="handleFormSubmit(event)">
                        <!-- Step 1: Project Setup -->
                        <div class="form-step-content active" id="stepContent1">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Project Setup</h3>
                                    <p class="card-description">Set up your content project details</p>
                                </div>
                                <div class="card-content">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div class="form-group">
                                            <label for="projectName" class="form-label form-label-required">Project Name</label>
                                            <input 
                                                type="text" 
                                                id="projectName" 
                                                name="projectName" 
                                                class="form-input" 
                                                placeholder="e.g., SEO Blog Posts Q1 2025"
                                                required
                                            >
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="industry" class="form-label form-label-required">Industry</label>
                                            <select id="industry" name="industry" class="form-select" required>
                                                <option value="">Select your industry</option>
                                                <option value="technology">Technology</option>
                                                <option value="healthcare">Healthcare</option>
                                                <option value="finance">Finance</option>
                                                <option value="ecommerce">E-commerce</option>
                                                <option value="marketing">Marketing</option>
                                                <option value="education">Education</option>
                                                <option value="real-estate">Real Estate</option>
                                                <option value="travel">Travel</option>
                                                <option value="food">Food & Beverage</option>
                                                <option value="fitness">Fitness</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group md:col-span-2">
                                            <label for="contentType" class="form-label form-label-required">Content Type</label>
                                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                                <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                                    <input type="radio" name="contentType" value="blog-post" class="form-radio" required>
                                                    <span class="ml-2 font-medium">Blog Post</span>
                                                </label>
                                                <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                                    <input type="radio" name="contentType" value="landing-page" class="form-radio">
                                                    <span class="ml-2 font-medium">Landing Page</span>
                                                </label>
                                                <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                                    <input type="radio" name="contentType" value="product-description" class="form-radio">
                                                    <span class="ml-2 font-medium">Product Description</span>
                                                </label>
                                                <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                                    <input type="radio" name="contentType" value="article" class="form-radio">
                                                    <span class="ml-2 font-medium">Article</span>
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group md:col-span-2">
                                            <label for="targetAudience" class="form-label">Target Audience</label>
                                            <input 
                                                type="text" 
                                                id="targetAudience" 
                                                name="targetAudience" 
                                                class="form-input" 
                                                placeholder="e.g., Small business owners, Marketing professionals"
                                            >
                                            <p class="form-help">Describe your ideal reader for better content personalization</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <button type="button" class="btn btn-primary" onclick="nextStep()">
                                        Next: Keywords
                                        <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 2: Keywords -->
                        <div class="form-step-content" id="stepContent2">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Keyword Research</h3>
                                    <p class="card-description">Define your target keywords for SEO optimization</p>
                                </div>
                                <div class="card-content">
                                    <div class="form-group">
                                        <label for="primaryKeyword" class="form-label form-label-required">Primary Keyword</label>
                                        <input 
                                            type="text" 
                                            id="primaryKeyword" 
                                            name="primaryKeyword" 
                                            class="form-input" 
                                            placeholder="e.g., SEO content marketing"
                                            required
                                        >
                                        <p class="form-help">This is your main target keyword with highest search volume</p>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="secondaryKeywords" class="form-label">Secondary Keywords</label>
                                        <textarea 
                                            id="secondaryKeywords" 
                                            name="secondaryKeywords" 
                                            class="form-textarea" 
                                            placeholder="e.g., content marketing strategy, SEO best practices, digital marketing tips"
                                            rows="4"
                                        ></textarea>
                                        <p class="form-help">Enter related keywords separated by commas</p>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="competitorUrls" class="form-label">Competitor URLs (Optional)</label>
                                        <textarea 
                                            id="competitorUrls" 
                                            name="competitorUrls" 
                                            class="form-textarea" 
                                            placeholder="https://example.com/article-1&#10;https://example.com/article-2"
                                            rows="3"
                                        ></textarea>
                                        <p class="form-help">URLs of competing content to analyze and outrank</p>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <div class="form-group">
                                            <label for="location" class="form-label">Geographic Target</label>
                                            <select id="location" name="location" class="form-select">
                                                <option value="United States">United States</option>
                                                <option value="United Kingdom">United Kingdom</option>
                                                <option value="Canada">Canada</option>
                                                <option value="Australia">Australia</option>
                                                <option value="Germany">Germany</option>
                                                <option value="France">France</option>
                                                <option value="Spain">Spain</option>
                                                <option value="Italy">Italy</option>
                                                <option value="Netherlands">Netherlands</option>
                                                <option value="India">India</option>
                                                <option value="Japan">Japan</option>
                                                <option value="Brazil">Brazil</option>
                                                <option value="Mexico">Mexico</option>
                                                <option value="Global">Global</option>
                                            </select>
                                            <p class="form-help">Target location for localized content</p>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="searchEngine" class="form-label">Search Engine</label>
                                            <select id="searchEngine" name="searchEngine" class="form-select">
                                                <option value="google">Google</option>
                                                <option value="bing">Bing</option>
                                                <option value="yahoo">Yahoo</option>
                                                <option value="baidu">Baidu</option>
                                                <option value="yandex">Yandex</option>
                                            </select>
                                            <p class="form-help">Optimize for specific search engine</p>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="contentIntent" class="form-label">Content Intent</label>
                                            <select id="contentIntent" name="contentIntent" class="form-select">
                                                <option value="informational">Informational</option>
                                                <option value="commercial">Commercial</option>
                                                <option value="transactional">Transactional</option>
                                                <option value="navigational">Navigational</option>
                                            </select>
                                            <p class="form-help">Primary search intent to target</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="flex gap-3">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep()">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                            </svg>
                                            Previous
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep()">
                                            Next: Preferences
                                            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 3: Preferences -->
                        <div class="form-step-content" id="stepContent3">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Content Preferences</h3>
                                    <p class="card-description">Customize your content style and requirements</p>
                                </div>
                                <div class="card-content">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div class="form-group">
                                            <label for="toneOfVoice" class="form-label">Tone of Voice</label>
                                            <select id="toneOfVoice" name="toneOfVoice" class="form-select">
                                                <option value="professional">Professional</option>
                                                <option value="casual">Casual</option>
                                                <option value="friendly">Friendly</option>
                                                <option value="authoritative">Authoritative</option>
                                                <option value="conversational">Conversational</option>
                                                <option value="technical">Technical</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="writingStyle" class="form-label">Writing Style</label>
                                            <select id="writingStyle" name="writingStyle" class="form-select">
                                                <option value="informative">Informative</option>
                                                <option value="persuasive">Persuasive</option>
                                                <option value="educational">Educational</option>
                                                <option value="entertaining">Entertaining</option>
                                                <option value="narrative">Narrative</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="contentLanguage" class="form-label">Content Language</label>
                                            <select id="contentLanguage" name="contentLanguage" class="form-select">
                                                <option value="en" selected>English</option>
                                                <option value="es">Español (Spanish)</option>
                                                <option value="fr">Français (French)</option>
                                                <option value="de">Deutsch (German)</option>
                                                <option value="it">Italiano (Italian)</option>
                                                <option value="pt">Português (Portuguese)</option>
                                            </select>
                                            <p class="form-help">Language for content generation and SEO optimization</p>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="contentDepth" class="form-label">Content Depth</label>
                                            <select id="contentDepth" name="contentDepth" class="form-select">
                                                <option value="beginner">Beginner Level</option>
                                                <option value="intermediate">Intermediate</option>
                                                <option value="advanced">Advanced</option>
                                                <option value="expert">Expert Level</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="wordCount" class="form-label">Target Word Count</label>
                                            <input type="range" id="wordCount" name="wordCount" min="500" max="3000" value="1500" class="w-full">
                                            <div class="flex justify-between text-sm text-gray-600 mt-2">
                                                <span>500 words</span>
                                                <span id="wordCountDisplay">1500 words</span>
                                                <span>3000 words</span>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group md:col-span-2">
                                            <label for="specialRequirements" class="form-label">Special Requirements</label>
                                            <textarea 
                                                id="specialRequirements" 
                                                name="specialRequirements" 
                                                class="form-textarea" 
                                                placeholder="Any specific requirements, guidelines, or elements to include..."
                                                rows="3"
                                            ></textarea>
                                        </div>
                                        
                                        <div class="form-group md:col-span-2">
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox" name="includeImages" class="form-checkbox">
                                                <span class="ml-2">Include image suggestions and alt text</span>
                                            </label>
                                        </div>
                                        
                                        <div class="form-group md:col-span-2">
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox" name="includeSchema" class="form-checkbox">
                                                <span class="ml-2">Generate schema markup suggestions</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="flex gap-3">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep()">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                            </svg>
                                            Previous
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep()">
                                            Next: Generate
                                            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 4: Generate -->
                        <div class="form-step-content" id="stepContent4">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Review & Generate</h3>
                                    <p class="card-description">Review your settings and generate content</p>
                                </div>
                                <div class="card-content">
                                    <div id="reviewSummary" class="bg-gray-50 rounded-lg p-6 mb-6">
                                        <!-- Summary will be populated by JavaScript -->
                                    </div>
                                    
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-primary btn-large">
                                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                            </svg>
                                            Generate SEO Content
                                        </button>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <button type="button" class="btn btn-secondary" onclick="previousStep()">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                        </svg>
                                        Previous
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <!-- Generation Progress -->
                    <div class="generation-progress" id="generationProgress">
                        <div class="progress-steps">
                            <div class="progress-step" id="progressStep1">
                                <div class="progress-step-icon">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <span>Analyzing keywords and competitors</span>
                            </div>
                            
                            <div class="progress-step" id="progressStep2">
                                <div class="progress-step-icon">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <span>Generating content structure</span>
                            </div>
                            
                            <div class="progress-step" id="progressStep3">
                                <div class="progress-step-icon">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </div>
                                <span>Writing SEO-optimized content</span>
                            </div>
                            
                            <div class="progress-step" id="progressStep4">
                                <div class="progress-step-icon">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <span>Finalizing and optimizing</span>
                            </div>
                        </div>
                        
                        <div class="spinner mx-auto mb-4"></div>
                        <p class="text-gray-600">This usually takes 30-60 seconds...</p>
                    </div>
                    
                    <!-- Generated Content -->
                    <div class="generated-content" id="generatedContent" style="display: none;">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h3 class="text-xl font-semibold">Generated Content</h3>
                                <div class="flex items-center gap-4 mt-2">
                                    <span class="seo-score excellent">SEO Score: 94/100</span>
                                    <span class="word-count">1,456 words</span>
                                </div>
                            </div>
                            
                            <div class="content-toolbar">
                                <button class="btn btn-secondary btn-sm" onclick="copyContent()">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    Copy
                                </button>
                                
                                <button class="btn btn-secondary btn-sm" onclick="exportContent()">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Export
                                </button>
                                
                                <button class="btn btn-primary btn-sm" onclick="regenerateContent()">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Regenerate
                                </button>
                            </div>
                        </div>
                        
                        <div class="content-editor" contenteditable="true" id="contentEditor">
                            <!-- Generated content will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Mobile Sidebar Overlay -->
    <div id="sidebarOverlay" class="fixed inset-0 bg-black bg-opacity-50 hidden lg:hidden z-30" onclick="toggleSidebar()"></div>
    
    <script>
        let currentStep = 1;
        const totalSteps = 4;
        
        // Sidebar toggle function
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            sidebar.classList.toggle('active');
            overlay.classList.toggle('hidden');
            menuBtn.classList.toggle('active');
        }
        
        function toggleUserMenu() {
            const userMenu = document.getElementById('userMenu');
            userMenu.classList.toggle('hidden');
        }
        
        function nextStep() {
            if (currentStep < totalSteps) {
                // Hide current step
                document.getElementById(`stepContent${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');
                
                // Show next step
                currentStep++;
                document.getElementById(`stepContent${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.add('active');
                
                // Mark previous steps as completed
                for (let i = 1; i < currentStep; i++) {
                    document.getElementById(`step${i}`).classList.add('completed');
                }
                
                // Update review summary if on step 4
                if (currentStep === 4) {
                    updateReviewSummary();
                }
            }
        }
        
        function previousStep() {
            if (currentStep > 1) {
                // Hide current step
                document.getElementById(`stepContent${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');
                
                // Show previous step
                currentStep--;
                document.getElementById(`stepContent${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.remove('completed');
            }
        }
        
        function updateReviewSummary() {
            const form = document.getElementById('contentGeneratorForm');
            const formData = new FormData(form);
            
            const summary = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold mb-2">Project Details</h4>
                        <p><strong>Project:</strong> ${formData.get('projectName') || 'Not specified'}</p>
                        <p><strong>Industry:</strong> ${formData.get('industry') || 'Not specified'}</p>
                        <p><strong>Content Type:</strong> ${formData.get('contentType') || 'Not specified'}</p>
                        <p><strong>Target Audience:</strong> ${formData.get('targetAudience') || 'Not specified'}</p>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">SEO Settings</h4>
                        <p><strong>Primary Keyword:</strong> ${formData.get('primaryKeyword') || 'Not specified'}</p>
                        <p><strong>Tone:</strong> ${formData.get('toneOfVoice') || 'Professional'}</p>
                        <p><strong>Style:</strong> ${formData.get('writingStyle') || 'Informative'}</p>
                        <p><strong>Word Count:</strong> ${formData.get('wordCount') || '1500'} words</p>
                    </div>
                </div>
            `;
            
            document.getElementById('reviewSummary').innerHTML = summary;
        }
        
        function handleFormSubmit(event) {
            event.preventDefault();
            
            // Hide form and show progress
            document.getElementById('stepContent4').style.display = 'none';
            document.getElementById('generationProgress').classList.add('active');
            
            // Simulate generation process
            simulateGeneration();
        }
        
        function simulateGeneration() {
            const steps = ['progressStep1', 'progressStep2', 'progressStep3', 'progressStep4'];
            let currentProgressStep = 0;
            
            function activateStep() {
                if (currentProgressStep > 0) {
                    document.getElementById(steps[currentProgressStep - 1]).classList.remove('active');
                    document.getElementById(steps[currentProgressStep - 1]).classList.add('completed');
                }
                
                if (currentProgressStep < steps.length) {
                    document.getElementById(steps[currentProgressStep]).classList.add('active');
                    currentProgressStep++;
                    setTimeout(activateStep, 2000);
                } else {
                    // Generation complete
                    setTimeout(() => {
                        document.getElementById('generationProgress').classList.remove('active');
                        showGeneratedContent();
                    }, 1000);
                }
            }
            
            activateStep();
        }
        
        function showGeneratedContent() {
            const sampleContent = `
                <h1>The Complete Guide to SEO Content Marketing in 2025</h1>
                
                <p>In today's digital landscape, SEO content marketing has become the cornerstone of successful online businesses. This comprehensive guide will walk you through everything you need to know about creating content that not only engages your audience but also ranks high in search engine results.</p>
                
                <h2>What is SEO Content Marketing?</h2>
                
                <p>SEO content marketing is the strategic approach of creating, publishing, and promoting content that is specifically designed to attract organic search traffic. It combines the art of content creation with the science of search engine optimization to deliver maximum visibility and engagement.</p>
                
                <h2>Why SEO Content Marketing Matters in 2025</h2>
                
                <p>As search engines continue to evolve, the importance of high-quality, user-focused content has never been greater. Here are the key reasons why SEO content marketing should be at the center of your digital strategy:</p>
                
                <ul>
                    <li><strong>Increased Organic Visibility:</strong> Quality content helps your website rank higher in search results</li>
                    <li><strong>Cost-Effective Lead Generation:</strong> Organic traffic converts better than paid advertising</li>
                    <li><strong>Authority Building:</strong> Consistent, valuable content establishes your brand as an industry leader</li>
                    <li><strong>Long-term ROI:</strong> Content continues to generate traffic and leads long after publication</li>
                </ul>
                
                <h2>Essential SEO Content Marketing Strategies</h2>
                
                <h3>1. Keyword Research and Planning</h3>
                
                <p>Effective SEO content marketing begins with thorough keyword research. Identify the terms your target audience is searching for and create content that addresses their needs and pain points.</p>
                
                <h3>2. Content Quality and User Experience</h3>
                
                <p>Search engines prioritize content that provides genuine value to users. Focus on creating comprehensive, well-researched content that answers questions and solves problems.</p>
                
                <h3>3. Technical SEO Optimization</h3>
                
                <p>Ensure your content is technically optimized with proper heading structure, meta descriptions, image alt text, and internal linking strategies.</p>
                
                <h2>Measuring Success</h2>
                
                <p>Track your SEO content marketing performance using key metrics such as organic traffic growth, keyword rankings, engagement rates, and conversion metrics to continuously improve your strategy.</p>
                
                <h2>Conclusion</h2>
                
                <p>SEO content marketing remains one of the most effective ways to build sustainable organic growth. By focusing on creating valuable, optimized content that serves your audience's needs, you'll be well-positioned for success in 2025 and beyond.</p>
            `;
            
            document.getElementById('contentEditor').innerHTML = sampleContent;
            document.getElementById('generatedContent').style.display = 'block';
        }
        
        function copyContent() {
            const content = document.getElementById('contentEditor').innerText;
            navigator.clipboard.writeText(content).then(() => {
                alert('Content copied to clipboard!');
            });
        }
        
        function exportContent() {
            const content = document.getElementById('contentEditor').innerHTML;
            const blob = new Blob([content], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'generated-content.html';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function regenerateContent() {
            document.getElementById('generatedContent').style.display = 'none';
            document.getElementById('generationProgress').classList.add('active');
            simulateGeneration();
        }
        
        // Word count slider update
        document.getElementById('wordCount').addEventListener('input', function() {
            document.getElementById('wordCountDisplay').textContent = this.value + ' words';
        });
        
        // Update word count in content editor
        document.getElementById('contentEditor').addEventListener('input', function() {
            const wordCount = this.innerText.split(/\s+/).filter(word => word.length > 0).length;
            document.querySelector('.word-count').textContent = wordCount + ' words';
        });
    </script>

    <!-- Core JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/supabase.js"></script>
    <script src="js/api.js"></script>
    <script src="js/content-generator.js"></script>
    <script src="js/app-init.js"></script>
    <script src="js/main.js"></script>
</body>
</html>