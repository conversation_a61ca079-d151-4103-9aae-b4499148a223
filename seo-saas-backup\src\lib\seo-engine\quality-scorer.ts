// Advanced Content Quality Scoring with E-E-A-T Compliance
// Enterprise-grade content quality assessment and optimization

interface QualityScore {
  overall: number; // 0-100
  experience: number; // E-E-A-T: Experience
  expertise: number; // E-E-A-T: Expertise  
  authoritativeness: number; // E-E-A-T: Authoritativeness
  trustworthiness: number; // E-E-A-T: Trustworthiness
  readability: number;
  seoOptimization: number;
  contentDepth: number;
  userValue: number;
  technicalQuality: number;
}

interface QualityAnalysis {
  score: QualityScore;
  strengths: string[];
  weaknesses: string[];
  recommendations: QualityRecommendation[];
  eeatCompliance: EEATCompliance;
  competitorComparison: CompetitorQualityComparison;
}

interface QualityRecommendation {
  category: 'experience' | 'expertise' | 'authoritativeness' | 'trustworthiness' | 'readability' | 'seo' | 'content' | 'technical';
  priority: 'critical' | 'high' | 'medium' | 'low';
  title: string;
  description: string;
  implementation: string;
  expectedImpact: number; // 1-10
  effort: number; // 1-10
}

interface EEATCompliance {
  experience: {
    score: number;
    indicators: string[];
    missing: string[];
  };
  expertise: {
    score: number;
    indicators: string[];
    missing: string[];
  };
  authoritativeness: {
    score: number;
    indicators: string[];
    missing: string[];
  };
  trustworthiness: {
    score: number;
    indicators: string[];
    missing: string[];
  };
}

interface CompetitorQualityComparison {
  ranking: number; // 1-5 (position among competitors)
  averageScore: number;
  topPerformerScore: number;
  gaps: string[];
  advantages: string[];
}

export class QualityScorer {
  private primaryKeyword: string;
  private industry: string;
  private contentType: string;
  private targetAudience: string;

  // E-E-A-T signal patterns
  private experienceSignals = [
    'years of experience', 'worked with', 'helped clients', 'case study', 'real-world',
    'hands-on', 'practical', 'first-hand', 'personal experience', 'worked on'
  ];

  private expertiseSignals = [
    'certified', 'degree', 'qualification', 'expert', 'specialist', 'professional',
    'trained', 'licensed', 'accredited', 'award', 'recognition', 'published'
  ];

  private authoritativenessSignals = [
    'industry leader', 'thought leader', 'keynote speaker', 'published author',
    'quoted by', 'featured in', 'interviewed by', 'recognized by', 'member of'
  ];

  private trustworthinessSignals = [
    'privacy policy', 'terms of service', 'contact information', 'about us',
    'testimonials', 'reviews', 'guarantee', 'secure', 'verified', 'transparent'
  ];

  constructor(
    primaryKeyword: string,
    industry: string = 'general',
    contentType: string = 'general',
    targetAudience: string = 'general'
  ) {
    this.primaryKeyword = primaryKeyword.toLowerCase();
    this.industry = industry.toLowerCase();
    this.contentType = contentType.toLowerCase();
    this.targetAudience = targetAudience.toLowerCase();
  }

  /**
   * Analyze content quality with comprehensive E-E-A-T assessment
   */
  analyzeQuality(content: string, authorInfo?: any, competitorScores?: number[]): QualityAnalysis {
    // Calculate individual quality scores
    const experience = this.calculateExperienceScore(content, authorInfo);
    const expertise = this.calculateExpertiseScore(content, authorInfo);
    const authoritativeness = this.calculateAuthoritativenessScore(content, authorInfo);
    const trustworthiness = this.calculateTrustworthinessScore(content);
    const readability = this.calculateReadabilityScore(content);
    const seoOptimization = this.calculateSEOScore(content);
    const contentDepth = this.calculateContentDepthScore(content);
    const userValue = this.calculateUserValueScore(content);
    const technicalQuality = this.calculateTechnicalQualityScore(content);

    // Calculate overall score with weighted average
    const overall = this.calculateOverallScore({
      experience,
      expertise,
      authoritativeness,
      trustworthiness,
      readability,
      seoOptimization,
      contentDepth,
      userValue,
      technicalQuality
    });

    const score: QualityScore = {
      overall,
      experience,
      expertise,
      authoritativeness,
      trustworthiness,
      readability,
      seoOptimization,
      contentDepth,
      userValue,
      technicalQuality
    };

    // Generate analysis components
    const strengths = this.identifyStrengths(score);
    const weaknesses = this.identifyWeaknesses(score);
    const recommendations = this.generateRecommendations(score, content);
    const eeatCompliance = this.assessEEATCompliance(content, authorInfo);
    const competitorComparison = this.compareWithCompetitors(score, competitorScores);

    return {
      score,
      strengths,
      weaknesses,
      recommendations,
      eeatCompliance,
      competitorComparison
    };
  }

  /**
   * Calculate Experience score (first E in E-E-A-T)
   */
  private calculateExperienceScore(content: string, authorInfo?: any): number {
    let score = 0;
    const lowerContent = content.toLowerCase();

    // Check for experience signals in content
    const experienceCount = this.experienceSignals.filter(signal => 
      lowerContent.includes(signal)
    ).length;
    score += Math.min(40, experienceCount * 8);

    // Check for specific examples and case studies
    const hasExamples = /example|case study|for instance|in practice/i.test(content);
    if (hasExamples) score += 20;

    // Check for personal anecdotes or first-hand accounts
    const hasPersonalExperience = /i have|we have|in my experience|from my work/i.test(content);
    if (hasPersonalExperience) score += 20;

    // Check author information
    if (authorInfo?.yearsOfExperience) {
      score += Math.min(20, authorInfo.yearsOfExperience * 2);
    }

    return Math.min(100, score);
  }

  /**
   * Calculate Expertise score (second E in E-E-A-T)
   */
  private calculateExpertiseScore(content: string, authorInfo?: any): number {
    let score = 0;
    const lowerContent = content.toLowerCase();

    // Check for expertise signals
    const expertiseCount = this.expertiseSignals.filter(signal => 
      lowerContent.includes(signal)
    ).length;
    score += Math.min(30, expertiseCount * 6);

    // Check for technical depth and accuracy
    const hasTechnicalTerms = this.countTechnicalTerms(content);
    score += Math.min(25, hasTechnicalTerms * 2);

    // Check for citations and references
    const hasCitations = /source:|reference:|study:|research:|according to/i.test(content);
    if (hasCitations) score += 20;

    // Check for detailed explanations
    const hasDetailedExplanations = content.length > 1500 && /because|therefore|however|furthermore/i.test(content);
    if (hasDetailedExplanations) score += 15;

    // Check author credentials
    if (authorInfo?.credentials) {
      score += Math.min(10, authorInfo.credentials.length * 2);
    }

    return Math.min(100, score);
  }

  /**
   * Calculate Authoritativeness score (A in E-E-A-T)
   */
  private calculateAuthoritativenessScore(content: string, authorInfo?: any): number {
    let score = 0;
    const lowerContent = content.toLowerCase();

    // Check for authority signals
    const authorityCount = this.authoritativenessSignals.filter(signal => 
      lowerContent.includes(signal)
    ).length;
    score += Math.min(40, authorityCount * 10);

    // Check for external recognition
    const hasRecognition = /featured in|quoted by|interviewed|award|recognition/i.test(content);
    if (hasRecognition) score += 25;

    // Check for industry involvement
    const hasIndustryInvolvement = /conference|speaking|published|member of/i.test(content);
    if (hasIndustryInvolvement) score += 20;

    // Check author authority indicators
    if (authorInfo?.publications) score += 10;
    if (authorInfo?.speakingEngagements) score += 5;

    return Math.min(100, score);
  }

  /**
   * Calculate Trustworthiness score (T in E-E-A-T)
   */
  private calculateTrustworthinessScore(content: string): number {
    let score = 0;
    const lowerContent = content.toLowerCase();

    // Check for trust signals
    const trustCount = this.trustworthinessSignals.filter(signal => 
      lowerContent.includes(signal)
    ).length;
    score += Math.min(30, trustCount * 6);

    // Check for transparency indicators
    const hasTransparency = /disclaimer|disclosure|affiliate|sponsored/i.test(content);
    if (hasTransparency) score += 20;

    // Check for contact information
    const hasContact = /contact|email|phone|address/i.test(content);
    if (hasContact) score += 15;

    // Check for social proof
    const hasSocialProof = /testimonial|review|client|customer|rating/i.test(content);
    if (hasSocialProof) score += 20;

    // Check for security indicators
    const hasSecurityInfo = /secure|privacy|protected|encrypted/i.test(content);
    if (hasSecurityInfo) score += 15;

    return Math.min(100, score);
  }

  /**
   * Calculate readability score
   */
  private calculateReadabilityScore(content: string): number {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = content.split(/\s+/).filter(w => w.length > 0);
    
    if (sentences.length === 0 || words.length === 0) return 0;

    const avgSentenceLength = words.length / sentences.length;
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;

    // Flesch Reading Ease calculation
    const fleschScore = 206.835 - (1.015 * avgSentenceLength) - (84.6 * (avgWordLength / 4.7));
    
    // Convert to 0-100 scale where higher is better
    return Math.max(0, Math.min(100, fleschScore));
  }

  /**
   * Calculate SEO optimization score
   */
  private calculateSEOScore(content: string): number {
    let score = 0;
    const lowerContent = content.toLowerCase();

    // Keyword presence in content
    const keywordCount = (lowerContent.match(new RegExp(this.primaryKeyword, 'g')) || []).length;
    const keywordDensity = (keywordCount / content.split(/\s+/).length) * 100;
    
    if (keywordDensity >= 0.5 && keywordDensity <= 3) score += 25;
    else if (keywordDensity > 0) score += 15;

    // Heading optimization
    const headings = content.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi) || [];
    const optimizedHeadings = headings.filter(h => 
      h.toLowerCase().includes(this.primaryKeyword)
    ).length;
    score += Math.min(25, optimizedHeadings * 8);

    // Meta elements (if present)
    const hasTitle = /<title>/i.test(content);
    const hasMetaDescription = /<meta[^>]*description/i.test(content);
    if (hasTitle) score += 15;
    if (hasMetaDescription) score += 15;

    // Internal linking
    const internalLinks = (content.match(/<a[^>]*href/gi) || []).length;
    score += Math.min(20, internalLinks * 4);

    return Math.min(100, score);
  }

  /**
   * Calculate content depth score
   */
  private calculateContentDepthScore(content: string): number {
    let score = 0;
    const wordCount = content.split(/\s+/).length;

    // Word count scoring
    if (wordCount >= 2000) score += 30;
    else if (wordCount >= 1500) score += 25;
    else if (wordCount >= 1000) score += 20;
    else if (wordCount >= 500) score += 15;
    else score += 10;

    // Topic coverage
    const subtopics = this.identifySubtopics(content);
    score += Math.min(25, subtopics * 5);

    // Use of examples and explanations
    const hasExamples = /example|for instance|such as|including/gi.test(content);
    if (hasExamples) score += 15;

    // Use of lists and structure
    const hasLists = /<[uo]l>/i.test(content);
    if (hasLists) score += 15;

    // Comprehensive coverage indicators
    const hasConclusion = /conclusion|summary|in summary|to conclude/i.test(content);
    if (hasConclusion) score += 15;

    return Math.min(100, score);
  }

  /**
   * Calculate user value score
   */
  private calculateUserValueScore(content: string): number {
    let score = 0;

    // Actionable content
    const hasActionableContent = /how to|step|guide|tip|method|strategy/i.test(content);
    if (hasActionableContent) score += 25;

    // Problem-solving focus
    const addressesProblems = /problem|solution|challenge|issue|fix|resolve/i.test(content);
    if (addressesProblems) score += 20;

    // Clear structure
    const hasGoodStructure = content.includes('<h2>') || content.includes('<h3>');
    if (hasGoodStructure) score += 15;

    // Call-to-action presence
    const hasCTA = /contact|call|click|download|subscribe|learn more/i.test(content);
    if (hasCTA) score += 15;

    // Comprehensive information
    const isComprehensive = content.length > 1500 && /what|why|how|when|where/i.test(content);
    if (isComprehensive) score += 25;

    return Math.min(100, score);
  }

  /**
   * Calculate technical quality score
   */
  private calculateTechnicalQualityScore(content: string): number {
    let score = 0;

    // HTML structure
    const hasProperHeadings = /<h1>/i.test(content) && /<h2>/i.test(content);
    if (hasProperHeadings) score += 20;

    // Image optimization
    const images = content.match(/<img[^>]*>/gi) || [];
    const imagesWithAlt = images.filter(img => /alt=/i.test(img));
    if (images.length > 0) {
      score += (imagesWithAlt.length / images.length) * 20;
    }

    // Schema markup
    const hasSchema = /schema\.org|@type|@context/i.test(content);
    if (hasSchema) score += 20;

    // Clean markup
    const hasCleanMarkup = !/<font|<center|<table/i.test(content);
    if (hasCleanMarkup) score += 20;

    // Mobile-friendly indicators
    const isMobileFriendly = /viewport|responsive|mobile/i.test(content);
    if (isMobileFriendly) score += 20;

    return Math.min(100, score);
  }

  /**
   * Calculate overall weighted score
   */
  private calculateOverallScore(scores: Omit<QualityScore, 'overall'>): number {
    const weights = {
      experience: 0.15,
      expertise: 0.15,
      authoritativeness: 0.15,
      trustworthiness: 0.15,
      readability: 0.12,
      seoOptimization: 0.10,
      contentDepth: 0.08,
      userValue: 0.05,
      technicalQuality: 0.05
    };

    let weightedSum = 0;
    let totalWeight = 0;

    for (const [key, weight] of Object.entries(weights)) {
      const score = scores[key as keyof typeof scores];
      weightedSum += score * weight;
      totalWeight += weight;
    }

    return Math.round(weightedSum / totalWeight);
  }

  /**
   * Identify content strengths
   */
  private identifyStrengths(score: QualityScore): string[] {
    const strengths: string[] = [];

    if (score.experience >= 80) strengths.push('Strong demonstration of real-world experience');
    if (score.expertise >= 80) strengths.push('High level of subject matter expertise');
    if (score.authoritativeness >= 80) strengths.push('Clear authority and industry recognition');
    if (score.trustworthiness >= 80) strengths.push('Excellent trustworthiness signals');
    if (score.readability >= 80) strengths.push('Highly readable and accessible content');
    if (score.seoOptimization >= 80) strengths.push('Well-optimized for search engines');
    if (score.contentDepth >= 80) strengths.push('Comprehensive and in-depth coverage');
    if (score.userValue >= 80) strengths.push('High value and actionable insights for users');

    return strengths;
  }

  /**
   * Identify content weaknesses
   */
  private identifyWeaknesses(score: QualityScore): string[] {
    const weaknesses: string[] = [];

    if (score.experience < 60) weaknesses.push('Limited demonstration of practical experience');
    if (score.expertise < 60) weaknesses.push('Insufficient depth of expertise shown');
    if (score.authoritativeness < 60) weaknesses.push('Lacks clear authority indicators');
    if (score.trustworthiness < 60) weaknesses.push('Missing important trust signals');
    if (score.readability < 60) weaknesses.push('Content could be more readable and accessible');
    if (score.seoOptimization < 60) weaknesses.push('SEO optimization needs improvement');
    if (score.contentDepth < 60) weaknesses.push('Content lacks sufficient depth and detail');
    if (score.userValue < 60) weaknesses.push('Limited actionable value for users');

    return weaknesses;
  }

  /**
   * Generate quality improvement recommendations
   */
  private generateRecommendations(score: QualityScore, content: string): QualityRecommendation[] {
    const recommendations: QualityRecommendation[] = [];

    // E-E-A-T recommendations
    if (score.experience < 70) {
      recommendations.push({
        category: 'experience',
        priority: 'high',
        title: 'Add Real-World Experience Examples',
        description: 'Include specific examples, case studies, or personal experiences to demonstrate practical knowledge',
        implementation: 'Add 2-3 concrete examples or case studies showing real-world application',
        expectedImpact: 8,
        effort: 5
      });
    }

    if (score.expertise < 70) {
      recommendations.push({
        category: 'expertise',
        priority: 'high',
        title: 'Enhance Technical Depth',
        description: 'Add more detailed explanations, technical terms, and authoritative sources',
        implementation: 'Include citations, technical details, and demonstrate deep subject knowledge',
        expectedImpact: 9,
        effort: 6
      });
    }

    if (score.trustworthiness < 70) {
      recommendations.push({
        category: 'trustworthiness',
        priority: 'critical',
        title: 'Add Trust Signals',
        description: 'Include contact information, testimonials, guarantees, and transparency elements',
        implementation: 'Add author bio, contact details, privacy policy links, and customer testimonials',
        expectedImpact: 9,
        effort: 4
      });
    }

    // Content quality recommendations
    if (score.readability < 70) {
      recommendations.push({
        category: 'readability',
        priority: 'medium',
        title: 'Improve Content Readability',
        description: 'Simplify sentences, use shorter paragraphs, and improve content structure',
        implementation: 'Break up long sentences, use bullet points, add subheadings',
        expectedImpact: 7,
        effort: 3
      });
    }

    if (score.seoOptimization < 70) {
      recommendations.push({
        category: 'seo',
        priority: 'high',
        title: 'Optimize for Target Keywords',
        description: 'Improve keyword usage, heading optimization, and meta elements',
        implementation: 'Add target keywords to headings, optimize keyword density, improve meta tags',
        expectedImpact: 8,
        effort: 4
      });
    }

    return recommendations.sort((a, b) => {
      const priorityScore = { critical: 4, high: 3, medium: 2, low: 1 };
      return priorityScore[b.priority] - priorityScore[a.priority];
    });
  }

  /**
   * Assess E-E-A-T compliance
   */
  private assessEEATCompliance(content: string, authorInfo?: any): EEATCompliance {
    const lowerContent = content.toLowerCase();

    return {
      experience: {
        score: this.calculateExperienceScore(content, authorInfo),
        indicators: this.experienceSignals.filter(signal => lowerContent.includes(signal)),
        missing: this.experienceSignals.filter(signal => !lowerContent.includes(signal))
      },
      expertise: {
        score: this.calculateExpertiseScore(content, authorInfo),
        indicators: this.expertiseSignals.filter(signal => lowerContent.includes(signal)),
        missing: this.expertiseSignals.filter(signal => !lowerContent.includes(signal))
      },
      authoritativeness: {
        score: this.calculateAuthoritativenessScore(content, authorInfo),
        indicators: this.authoritativenessSignals.filter(signal => lowerContent.includes(signal)),
        missing: this.authoritativenessSignals.filter(signal => !lowerContent.includes(signal))
      },
      trustworthiness: {
        score: this.calculateTrustworthinessScore(content),
        indicators: this.trustworthinessSignals.filter(signal => lowerContent.includes(signal)),
        missing: this.trustworthinessSignals.filter(signal => !lowerContent.includes(signal))
      }
    };
  }

  /**
   * Compare with competitor scores
   */
  private compareWithCompetitors(score: QualityScore, competitorScores?: number[]): CompetitorQualityComparison {
    if (!competitorScores || competitorScores.length === 0) {
      return {
        ranking: 1,
        averageScore: score.overall,
        topPerformerScore: score.overall,
        gaps: [],
        advantages: ['No competitor data available for comparison']
      };
    }

    const allScores = [...competitorScores, score.overall].sort((a, b) => b - a);
    const ranking = allScores.indexOf(score.overall) + 1;
    const averageScore = competitorScores.reduce((sum, s) => sum + s, 0) / competitorScores.length;
    const topPerformerScore = Math.max(...competitorScores);

    const gaps: string[] = [];
    const advantages: string[] = [];

    if (score.overall < averageScore) {
      gaps.push(`Overall score is ${Math.round(averageScore - score.overall)} points below average`);
    } else {
      advantages.push(`Overall score is ${Math.round(score.overall - averageScore)} points above average`);
    }

    if (score.overall < topPerformerScore) {
      gaps.push(`${Math.round(topPerformerScore - score.overall)} points behind top performer`);
    }

    return {
      ranking,
      averageScore: Math.round(averageScore),
      topPerformerScore: Math.round(topPerformerScore),
      gaps,
      advantages
    };
  }

  // Helper methods
  private countTechnicalTerms(content: string): number {
    // Industry-specific technical terms
    const industryTerms: Record<string, string[]> = {
      technology: ['algorithm', 'api', 'database', 'framework', 'architecture', 'scalability'],
      healthcare: ['diagnosis', 'treatment', 'clinical', 'therapeutic', 'pathology', 'pharmacology'],
      finance: ['portfolio', 'diversification', 'liquidity', 'volatility', 'derivatives', 'compliance'],
      legal: ['jurisdiction', 'litigation', 'precedent', 'statute', 'regulation', 'compliance']
    };

    const terms = industryTerms[this.industry] || [];
    const lowerContent = content.toLowerCase();
    
    return terms.filter(term => lowerContent.includes(term)).length;
  }

  private identifySubtopics(content: string): number {
    const headings = content.match(/<h[2-6][^>]*>(.*?)<\/h[2-6]>/gi) || [];
    return headings.length;
  }
}

export const qualityScorer = new QualityScorer('', 'general', 'general', 'general');

// Export types for use in other modules
export type {
  QualityScore,
  QualityAnalysis,
  QualityRecommendation,
  EEATCompliance,
  CompetitorQualityComparison
};
