const natural = require('natural');
const stopword = require('stopword');

class AdvancedKeywordDensityCalculator {
  constructor() {
    this.stemmer = natural.PorterStemmer;
    this.tokenizer = new natural.WordTokenizer();
  }

  // Main method for surgical keyword density analysis
  calculateSurgicalDensity(content, targetKeyword, competitorBenchmarks = null) {
    try {
      const analysis = {
        targetKeyword,
        content: {
          totalWords: 0,
          totalCharacters: content.length,
          sentences: 0,
          paragraphs: 0
        },
        mainKeyword: {},
        componentKeywords: {},
        variations: {},
        semanticKeywords: {},
        distribution: {},
        benchmarkComparison: {},
        recommendations: [],
        optimization: {}
      };

      // Prepare content for analysis
      const cleanContent = this.preprocessContent(content);
      const words = this.tokenizeContent(cleanContent);
      const sentences = this.extractSentences(cleanContent);
      const paragraphs = this.extractParagraphs(cleanContent);

      // Basic content metrics
      analysis.content.totalWords = words.length;
      analysis.content.sentences = sentences.length;
      analysis.content.paragraphs = paragraphs.length;

      // Main keyword analysis
      analysis.mainKeyword = this.analyzeMainKeyword(cleanContent, words, targetKeyword);

      // Component keyword analysis
      analysis.componentKeywords = this.analyzeComponentKeywords(cleanContent, words, targetKeyword);

      // Keyword variations analysis
      analysis.variations = this.analyzeKeywordVariations(cleanContent, words, targetKeyword);

      // Semantic keyword analysis
      analysis.semanticKeywords = this.analyzeSemanticKeywords(cleanContent, words, targetKeyword);

      // Distribution analysis
      analysis.distribution = this.analyzeKeywordDistribution(cleanContent, targetKeyword);

      // Benchmark comparison
      if (competitorBenchmarks) {
        analysis.benchmarkComparison = this.compareWithBenchmarks(analysis, competitorBenchmarks);
        analysis.recommendations = this.generateOptimizationRecommendations(analysis, competitorBenchmarks);
        analysis.optimization = this.calculateOptimizationTargets(analysis, competitorBenchmarks);
      }

      return {
        success: true,
        analysis,
        generatedAt: new Date().toISOString()
      };

    } catch (error) {
      console.error('Keyword density calculation error:', error);
      return {
        success: false,
        error: error.message,
        analysis: null
      };
    }
  }

  // Preprocess content for analysis
  preprocessContent(content) {
    return content
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Replace punctuation with spaces
      .replace(/\s+/g, ' ')      // Normalize whitespace
      .trim();
  }

  // Tokenize content into words
  tokenizeContent(content) {
    return this.tokenizer.tokenize(content) || [];
  }

  // Extract sentences from content
  extractSentences(content) {
    return content.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
  }

  // Extract paragraphs from content
  extractParagraphs(content) {
    return content.split(/\n\s*\n/).filter(paragraph => paragraph.trim().length > 0);
  }

  // Analyze main keyword density with surgical precision
  analyzeMainKeyword(content, words, keyword) {
    const keywordLower = keyword.toLowerCase();
    const keywordWords = keywordLower.split(/\s+/);
    const keywordLength = keywordWords.length;

    // Exact match analysis
    const exactMatches = this.findExactMatches(content, keywordLower);
    const exactDensity = (exactMatches.length / words.length) * 100;

    // Partial match analysis (individual words)
    const partialMatches = this.findPartialMatches(words, keywordWords);
    const partialDensity = (partialMatches.total / words.length) * 100;

    // Stemmed match analysis
    const stemmedMatches = this.findStemmedMatches(words, keywordWords);
    const stemmedDensity = (stemmedMatches.total / words.length) * 100;

    // Position analysis
    const positions = this.analyzeKeywordPositions(content, keywordLower);

    // Proximity analysis (how close keyword components appear together)
    const proximityAnalysis = this.analyzeKeywordProximity(words, keywordWords);

    return {
      keyword: keyword,
      exact: {
        matches: exactMatches.length,
        density: parseFloat(exactDensity.toFixed(3)),
        positions: exactMatches
      },
      partial: {
        matches: partialMatches.total,
        density: parseFloat(partialDensity.toFixed(3)),
        breakdown: partialMatches.breakdown
      },
      stemmed: {
        matches: stemmedMatches.total,
        density: parseFloat(stemmedDensity.toFixed(3)),
        breakdown: stemmedMatches.breakdown
      },
      positions,
      proximity: proximityAnalysis,
      quality: this.assessKeywordQuality(exactMatches.length, words.length, positions)
    };
  }

  // Analyze component keywords separately
  analyzeComponentKeywords(content, words, keyword) {
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    const componentAnalysis = {};

    keywordComponents.forEach(component => {
      if (component.length > 2) { // Skip very short words
        const exactMatches = this.findExactMatches(content, component);
        const density = (exactMatches.length / words.length) * 100;
        const positions = this.analyzeKeywordPositions(content, component);

        // Find related words (plurals, variations)
        const variations = this.findComponentVariations(words, component);
        const totalMatches = exactMatches.length + variations.total;
        const totalDensity = (totalMatches / words.length) * 100;

        componentAnalysis[component] = {
          exact: {
            matches: exactMatches.length,
            density: parseFloat(density.toFixed(3)),
            positions: exactMatches
          },
          variations: variations,
          total: {
            matches: totalMatches,
            density: parseFloat(totalDensity.toFixed(3))
          },
          positions,
          dominance: this.calculateComponentDominance(exactMatches.length, words.length)
        };
      }
    });

    return componentAnalysis;
  }

  // Analyze keyword variations and related terms
  analyzeKeywordVariations(content, words, keyword) {
    const variations = {
      plurals: this.findPluralVariations(words, keyword),
      synonyms: this.findSynonymVariations(words, keyword),
      stemmed: this.findStemmedVariations(words, keyword),
      misspellings: this.findCommonMisspellings(words, keyword),
      related: this.findRelatedTerms(words, keyword)
    };

    // Calculate total variation density
    const totalVariationMatches = Object.values(variations).reduce((sum, variation) => 
      sum + (variation.matches || 0), 0
    );
    const variationDensity = (totalVariationMatches / words.length) * 100;

    return {
      ...variations,
      total: {
        matches: totalVariationMatches,
        density: parseFloat(variationDensity.toFixed(3))
      }
    };
  }

  // Analyze semantic keywords and LSI terms
  analyzeSemanticKeywords(content, words, keyword) {
    const semanticTerms = this.extractSemanticTerms(content, keyword);
    const lsiTerms = this.extractLSITerms(content, keyword);
    const topicalKeywords = this.extractTopicalKeywords(content, keyword);

    const semanticDensity = this.calculateSemanticDensity(words, semanticTerms);
    const lsiDensity = this.calculateSemanticDensity(words, lsiTerms);
    const topicalDensity = this.calculateSemanticDensity(words, topicalKeywords);

    return {
      semantic: {
        terms: semanticTerms,
        density: semanticDensity,
        coverage: this.calculateSemanticCoverage(semanticTerms, keyword)
      },
      lsi: {
        terms: lsiTerms,
        density: lsiDensity,
        relevance: this.calculateLSIRelevance(lsiTerms, keyword)
      },
      topical: {
        terms: topicalKeywords,
        density: topicalDensity,
        authority: this.calculateTopicalAuthority(topicalKeywords, keyword)
      }
    };
  }

  // Analyze keyword distribution throughout content
  analyzeKeywordDistribution(content, keyword) {
    const sections = this.dividContentIntoSections(content);
    const keywordLower = keyword.toLowerCase();
    
    const distribution = sections.map((section, index) => {
      const sectionMatches = this.findExactMatches(section, keywordLower);
      const sectionWords = this.tokenizeContent(section);
      const sectionDensity = sectionWords.length > 0 ? (sectionMatches.length / sectionWords.length) * 100 : 0;

      return {
        section: index + 1,
        position: this.getSectionPosition(index, sections.length),
        wordCount: sectionWords.length,
        keywordMatches: sectionMatches.length,
        density: parseFloat(sectionDensity.toFixed(3)),
        hasKeyword: sectionMatches.length > 0
      };
    });

    return {
      sections: distribution,
      evenness: this.calculateDistributionEvenness(distribution),
      coverage: this.calculateDistributionCoverage(distribution),
      frontLoading: this.calculateFrontLoading(distribution),
      recommendations: this.generateDistributionRecommendations(distribution)
    };
  }

  // Find exact keyword matches
  findExactMatches(content, keyword) {
    const regex = new RegExp(`\\b${keyword.replace(/\s+/g, '\\s+')}\\b`, 'gi');
    const matches = [];
    let match;

    while ((match = regex.exec(content)) !== null) {
      matches.push({
        match: match[0],
        position: match.index,
        context: this.extractContext(content, match.index, keyword.length)
      });
    }

    return matches;
  }

  // Find partial matches (individual keyword components)
  findPartialMatches(words, keywordComponents) {
    const breakdown = {};
    let total = 0;

    keywordComponents.forEach(component => {
      const matches = words.filter(word => word === component).length;
      breakdown[component] = matches;
      total += matches;
    });

    return { total, breakdown };
  }

  // Find stemmed matches
  findStemmedMatches(words, keywordComponents) {
    const breakdown = {};
    let total = 0;

    keywordComponents.forEach(component => {
      const componentStem = this.stemmer.stem(component);
      const matches = words.filter(word => this.stemmer.stem(word) === componentStem).length;
      breakdown[component] = matches;
      total += matches;
    });

    return { total, breakdown };
  }

  // Analyze keyword positions (beginning, middle, end)
  analyzeKeywordPositions(content, keyword) {
    const matches = this.findExactMatches(content, keyword);
    const contentLength = content.length;
    
    const positions = {
      beginning: 0,   // First 25%
      middle: 0,      // Middle 50%
      end: 0          // Last 25%
    };

    matches.forEach(match => {
      const relativePosition = match.position / contentLength;
      
      if (relativePosition < 0.25) {
        positions.beginning++;
      } else if (relativePosition > 0.75) {
        positions.end++;
      } else {
        positions.middle++;
      }
    });

    return {
      ...positions,
      total: matches.length,
      distribution: {
        beginning: positions.beginning / Math.max(matches.length, 1),
        middle: positions.middle / Math.max(matches.length, 1),
        end: positions.end / Math.max(matches.length, 1)
      }
    };
  }

  // Analyze keyword proximity (how close components appear)
  analyzeKeywordProximity(words, keywordComponents) {
    const proximityScores = [];
    
    for (let i = 0; i < words.length; i++) {
      const window = words.slice(i, i + keywordComponents.length + 5); // 5-word buffer
      const componentMatches = keywordComponents.filter(component => 
        window.includes(component)
      ).length;
      
      if (componentMatches > 1) {
        proximityScores.push({
          position: i,
          components: componentMatches,
          proximity: componentMatches / keywordComponents.length
        });
      }
    }

    const avgProximity = proximityScores.length > 0 ? 
      proximityScores.reduce((sum, score) => sum + score.proximity, 0) / proximityScores.length : 0;

    return {
      scores: proximityScores,
      average: parseFloat(avgProximity.toFixed(3)),
      quality: this.assessProximityQuality(avgProximity)
    };
  }

  // Find component variations (plurals, etc.)
  findComponentVariations(words, component) {
    const variations = {
      plurals: [],
      stems: [],
      morphological: []
    };

    const componentStem = this.stemmer.stem(component);
    
    words.forEach(word => {
      if (word !== component) {
        // Plural detection
        if (word === component + 's' || word === component + 'es') {
          variations.plurals.push(word);
        }
        
        // Stem matching
        if (this.stemmer.stem(word) === componentStem) {
          variations.stems.push(word);
        }
        
        // Morphological variations
        if (this.isMorphologicalVariation(word, component)) {
          variations.morphological.push(word);
        }
      }
    });

    const total = variations.plurals.length + variations.stems.length + variations.morphological.length;

    return {
      ...variations,
      total,
      density: total
    };
  }

  // Extract semantic terms related to keyword
  extractSemanticTerms(content, keyword) {
    const semanticTerms = new Set();
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    
    // Simple semantic extraction (in production, would use word embeddings)
    const sentences = content.split(/[.!?]+/);
    
    sentences.forEach(sentence => {
      const hasKeywordComponent = keywordComponents.some(component => 
        sentence.includes(component)
      );
      
      if (hasKeywordComponent) {
        const words = this.tokenizeContent(sentence);
        words.forEach(word => {
          if (word.length > 3 && 
              !this.isStopWord(word) && 
              !keywordComponents.includes(word) &&
              this.isSemanticMatch(word, keyword)) {
            semanticTerms.add(word);
          }
        });
      }
    });

    return Array.from(semanticTerms).slice(0, 20);
  }

  // Extract LSI terms
  extractLSITerms(content, keyword) {
    const lsiTerms = new Set();
    const words = this.tokenizeContent(content);
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    
    // Co-occurrence analysis
    const coOccurrenceWindow = 10; // Words that appear within 10 words of keyword
    
    for (let i = 0; i < words.length; i++) {
      const currentWord = words[i];
      
      if (keywordComponents.includes(currentWord)) {
        const windowStart = Math.max(0, i - coOccurrenceWindow);
        const windowEnd = Math.min(words.length, i + coOccurrenceWindow);
        
        for (let j = windowStart; j < windowEnd; j++) {
          const nearbyWord = words[j];
          
          if (nearbyWord.length > 3 && 
              !this.isStopWord(nearbyWord) && 
              !keywordComponents.includes(nearbyWord)) {
            lsiTerms.add(nearbyWord);
          }
        }
      }
    }

    return Array.from(lsiTerms).slice(0, 15);
  }

  // Extract topical keywords
  extractTopicalKeywords(content, keyword) {
    const topicalKeywords = new Set();
    
    // Industry-specific term detection based on keyword
    const industryTerms = this.getIndustryTerms(keyword);
    const words = this.tokenizeContent(content);
    
    words.forEach(word => {
      if (industryTerms.includes(word)) {
        topicalKeywords.add(word);
      }
    });

    return Array.from(topicalKeywords);
  }

  // Compare analysis with competitor benchmarks
  compareWithBenchmarks(analysis, benchmarks) {
    const comparison = {
      mainKeyword: {},
      components: {},
      overall: {}
    };

    // Main keyword comparison
    if (benchmarks.keywordDensity && benchmarks.keywordDensity.main) {
      const targetDensity = benchmarks.keywordDensity.main;
      const currentDensity = analysis.mainKeyword.exact.density;
      
      comparison.mainKeyword = {
        target: targetDensity,
        current: currentDensity,
        difference: parseFloat((currentDensity - targetDensity).toFixed(3)),
        status: this.getDensityStatus(currentDensity, targetDensity),
        recommendation: this.getDensityRecommendation(currentDensity, targetDensity)
      };
    }

    // Component comparison
    if (benchmarks.keywordDensity && benchmarks.keywordDensity.components) {
      Object.keys(benchmarks.keywordDensity.components).forEach(component => {
        const targetDensity = benchmarks.keywordDensity.components[component];
        const currentDensity = analysis.componentKeywords[component]?.total.density || 0;
        
        comparison.components[component] = {
          target: targetDensity,
          current: currentDensity,
          difference: parseFloat((currentDensity - targetDensity).toFixed(3)),
          status: this.getDensityStatus(currentDensity, targetDensity)
        };
      });
    }

    return comparison;
  }

  // Generate optimization recommendations
  generateOptimizationRecommendations(analysis, benchmarks) {
    const recommendations = [];

    // Main keyword recommendations
    if (benchmarks.keywordDensity && benchmarks.keywordDensity.main) {
      const targetDensity = benchmarks.keywordDensity.main;
      const currentDensity = analysis.mainKeyword.exact.density;
      
      if (currentDensity < targetDensity * 0.8) {
        recommendations.push({
          type: 'increase_main_keyword',
          priority: 'high',
          message: `Increase main keyword density from ${currentDensity}% to ${targetDensity}%`,
          target: targetDensity,
          current: currentDensity,
          action: 'Add more instances of the main keyword naturally throughout the content'
        });
      } else if (currentDensity > targetDensity * 1.2) {
        recommendations.push({
          type: 'decrease_main_keyword',
          priority: 'medium',
          message: `Reduce main keyword density from ${currentDensity}% to ${targetDensity}%`,
          target: targetDensity,
          current: currentDensity,
          action: 'Replace some keyword instances with synonyms or related terms'
        });
      }
    }

    // Distribution recommendations
    if (analysis.distribution.evenness < 0.6) {
      recommendations.push({
        type: 'improve_distribution',
        priority: 'medium',
        message: 'Improve keyword distribution throughout content',
        action: 'Distribute keywords more evenly across all content sections'
      });
    }

    // Semantic recommendations
    if (analysis.semanticKeywords.semantic.terms.length < 10) {
      recommendations.push({
        type: 'add_semantic_keywords',
        priority: 'medium',
        message: 'Add more semantic keywords to improve topical relevance',
        action: 'Include related terms and synonyms to increase semantic richness'
      });
    }

    return recommendations;
  }

  // Calculate optimization targets
  calculateOptimizationTargets(analysis, benchmarks) {
    const targets = {};

    if (benchmarks.keywordDensity) {
      // Main keyword target
      targets.mainKeyword = {
        targetDensity: benchmarks.keywordDensity.main,
        currentDensity: analysis.mainKeyword.exact.density,
        targetCount: Math.round((benchmarks.keywordDensity.main / 100) * analysis.content.totalWords),
        currentCount: analysis.mainKeyword.exact.matches,
        adjustment: Math.round(((benchmarks.keywordDensity.main / 100) * analysis.content.totalWords) - analysis.mainKeyword.exact.matches)
      };

      // Component targets
      targets.components = {};
      Object.keys(benchmarks.keywordDensity.components || {}).forEach(component => {
        const targetDensity = benchmarks.keywordDensity.components[component];
        const currentDensity = analysis.componentKeywords[component]?.total.density || 0;
        const currentCount = analysis.componentKeywords[component]?.total.matches || 0;
        
        targets.components[component] = {
          targetDensity,
          currentDensity,
          targetCount: Math.round((targetDensity / 100) * analysis.content.totalWords),
          currentCount,
          adjustment: Math.round(((targetDensity / 100) * analysis.content.totalWords) - currentCount)
        };
      });
    }

    return targets;
  }

  // Helper methods
  extractContext(content, position, keywordLength) {
    const contextLength = 50;
    const start = Math.max(0, position - contextLength);
    const end = Math.min(content.length, position + keywordLength + contextLength);
    return content.substring(start, end);
  }

  dividContentIntoSections(content, sectionCount = 4) {
    const sectionLength = Math.ceil(content.length / sectionCount);
    const sections = [];
    
    for (let i = 0; i < sectionCount; i++) {
      const start = i * sectionLength;
      const end = Math.min(content.length, start + sectionLength);
      sections.push(content.substring(start, end));
    }
    
    return sections;
  }

  getSectionPosition(index, totalSections) {
    if (index === 0) return 'beginning';
    if (index === totalSections - 1) return 'end';
    return 'middle';
  }

  calculateDistributionEvenness(distribution) {
    const densities = distribution.map(section => section.density);
    const avgDensity = densities.reduce((sum, density) => sum + density, 0) / densities.length;
    const variance = densities.reduce((sum, density) => sum + Math.pow(density - avgDensity, 2), 0) / densities.length;
    return 1 / (1 + variance); // Convert variance to evenness score (0-1)
  }

  calculateDistributionCoverage(distribution) {
    const sectionsWithKeywords = distribution.filter(section => section.hasKeyword).length;
    return sectionsWithKeywords / distribution.length;
  }

  calculateFrontLoading(distribution) {
    const firstHalf = distribution.slice(0, Math.ceil(distribution.length / 2));
    const secondHalf = distribution.slice(Math.ceil(distribution.length / 2));
    
    const firstHalfDensity = firstHalf.reduce((sum, section) => sum + section.density, 0) / firstHalf.length;
    const secondHalfDensity = secondHalf.reduce((sum, section) => sum + section.density, 0) / secondHalf.length;
    
    return firstHalfDensity / (firstHalfDensity + secondHalfDensity);
  }

  // Assessment methods
  assessKeywordQuality(matches, totalWords, positions) {
    let quality = 0;
    
    // Density score
    const density = (matches / totalWords) * 100;
    if (density >= 1 && density <= 3) quality += 0.4;
    else if (density >= 0.5 && density <= 4) quality += 0.2;
    
    // Position score
    if (positions.beginning > 0) quality += 0.3;
    if (positions.middle > 0) quality += 0.2;
    if (positions.end > 0) quality += 0.1;
    
    return Math.min(quality, 1.0);
  }

  assessProximityQuality(avgProximity) {
    if (avgProximity >= 0.8) return 'excellent';
    if (avgProximity >= 0.6) return 'good';
    if (avgProximity >= 0.4) return 'fair';
    return 'poor';
  }

  getDensityStatus(current, target) {
    const ratio = current / target;
    if (ratio >= 0.8 && ratio <= 1.2) return 'optimal';
    if (ratio >= 0.6 && ratio <= 1.4) return 'acceptable';
    if (ratio < 0.6) return 'too_low';
    return 'too_high';
  }

  getDensityRecommendation(current, target) {
    if (current < target * 0.8) {
      return `Increase keyword usage by ${Math.round(((target / current) - 1) * 100)}%`;
    } else if (current > target * 1.2) {
      return `Reduce keyword usage by ${Math.round((1 - (target / current)) * 100)}%`;
    }
    return 'Keyword density is optimal';
  }

  // Utility methods
  calculateSemanticDensity(words, semanticTerms) {
    const totalSemanticMatches = semanticTerms.reduce((sum, term) => 
      sum + words.filter(word => word === term).length, 0
    );
    return (totalSemanticMatches / words.length) * 100;
  }

  calculateSemanticCoverage(semanticTerms, keyword) {
    // Simple coverage calculation - in production would use word embeddings
    return Math.min(semanticTerms.length / 15, 1.0);
  }

  calculateLSIRelevance(lsiTerms, keyword) {
    // Simple relevance calculation
    return Math.min(lsiTerms.length / 10, 1.0);
  }

  calculateTopicalAuthority(topicalKeywords, keyword) {
    return Math.min(topicalKeywords.length / 8, 1.0);
  }

  calculateComponentDominance(matches, totalWords) {
    const density = (matches / totalWords) * 100;
    if (density > 2) return 'high';
    if (density > 1) return 'medium';
    if (density > 0.5) return 'low';
    return 'minimal';
  }

  isMorphologicalVariation(word, component) {
    // Simple morphological variation detection
    if (word.length < component.length - 2 || word.length > component.length + 3) {
      return false;
    }
    
    const commonSuffixes = ['ing', 'ed', 'er', 'est', 'ly', 'tion', 'sion'];
    const commonPrefixes = ['un', 're', 'pre', 'dis'];
    
    for (const suffix of commonSuffixes) {
      if (word.endsWith(suffix) && word.slice(0, -suffix.length) === component) {
        return true;
      }
    }
    
    for (const prefix of commonPrefixes) {
      if (word.startsWith(prefix) && word.slice(prefix.length) === component) {
        return true;
      }
    }
    
    return false;
  }

  isSemanticMatch(word, keyword) {
    // Simple semantic matching - in production would use more sophisticated methods
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    return keywordComponents.some(component => 
      word.includes(component) || component.includes(word)
    );
  }

  getIndustryTerms(keyword) {
    // Industry-specific terms based on keyword context
    const industryMaps = {
      'gym': ['fitness', 'workout', 'exercise', 'training', 'muscle', 'cardio', 'strength'],
      'flooring': ['floor', 'surface', 'material', 'installation', 'tile', 'carpet', 'wood'],
      'dubai': ['uae', 'emirates', 'middle east', 'arab', 'gulf', 'region']
    };
    
    const terms = new Set();
    const keywordLower = keyword.toLowerCase();
    
    Object.keys(industryMaps).forEach(key => {
      if (keywordLower.includes(key)) {
        industryMaps[key].forEach(term => terms.add(term));
      }
    });
    
    return Array.from(terms);
  }

  findPluralVariations(words, keyword) {
    const variations = new Set();
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    
    keywordComponents.forEach(component => {
      const plural = component + 's';
      const esPlural = component + 'es';
      
      if (words.includes(plural)) variations.add(plural);
      if (words.includes(esPlural)) variations.add(esPlural);
    });
    
    return { matches: variations.size, terms: Array.from(variations) };
  }

  findSynonymVariations(words, keyword) {
    // Simple synonym detection - in production would use synonym database
    const synonymMap = {
      'gym': ['fitness center', 'health club', 'workout facility'],
      'flooring': ['floor covering', 'surface material', 'ground covering']
    };
    
    const variations = new Set();
    const keywordLower = keyword.toLowerCase();
    
    Object.keys(synonymMap).forEach(key => {
      if (keywordLower.includes(key)) {
        synonymMap[key].forEach(synonym => {
          const synonymWords = synonym.split(/\s+/);
          if (synonymWords.every(word => words.includes(word))) {
            variations.add(synonym);
          }
        });
      }
    });
    
    return { matches: variations.size, terms: Array.from(variations) };
  }

  findStemmedVariations(words, keyword) {
    const variations = new Set();
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    
    keywordComponents.forEach(component => {
      const componentStem = this.stemmer.stem(component);
      words.forEach(word => {
        if (word !== component && this.stemmer.stem(word) === componentStem) {
          variations.add(word);
        }
      });
    });
    
    return { matches: variations.size, terms: Array.from(variations) };
  }

  findCommonMisspellings(words, keyword) {
    // Simple misspelling detection
    const variations = new Set();
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    
    keywordComponents.forEach(component => {
      words.forEach(word => {
        if (this.isLikelyMisspelling(word, component)) {
          variations.add(word);
        }
      });
    });
    
    return { matches: variations.size, terms: Array.from(variations) };
  }

  findRelatedTerms(words, keyword) {
    const relatedTerms = this.getIndustryTerms(keyword);
    const foundTerms = words.filter(word => relatedTerms.includes(word));
    
    return { matches: foundTerms.length, terms: foundTerms };
  }

  isLikelyMisspelling(word, correct) {
    if (Math.abs(word.length - correct.length) > 2) return false;
    
    // Simple edit distance calculation
    let differences = 0;
    const minLength = Math.min(word.length, correct.length);
    
    for (let i = 0; i < minLength; i++) {
      if (word[i] !== correct[i]) differences++;
    }
    
    differences += Math.abs(word.length - correct.length);
    
    return differences <= 2 && differences > 0;
  }

  generateDistributionRecommendations(distribution) {
    const recommendations = [];
    
    if (distribution.some(section => !section.hasKeyword)) {
      recommendations.push('Add keywords to sections that currently lack them');
    }
    
    const densities = distribution.map(section => section.density);
    const maxDensity = Math.max(...densities);
    const minDensity = Math.min(...densities);
    
    if (maxDensity > minDensity * 3) {
      recommendations.push('Balance keyword distribution more evenly across sections');
    }
    
    return recommendations;
  }

  isStopWord(word) {
    return stopword.removeStopwords([word]).length === 0;
  }
}

module.exports = AdvancedKeywordDensityCalculator;