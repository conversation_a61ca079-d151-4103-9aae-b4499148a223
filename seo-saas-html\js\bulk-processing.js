// Bulk Processing JavaScript
class BulkProcessingManager {
    constructor() {
        this.keywords = [];
        this.currentOperation = null;
        this.operationPollingInterval = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadOperations();
        this.setupDragAndDrop();
    }

    setupEventListeners() {
        // File input change
        document.getElementById('file-input').addEventListener('change', (e) => {
            this.handleFileUpload(e);
        });

        // Keyword textarea change
        document.getElementById('keyword-textarea').addEventListener('input', (e) => {
            this.handleTextInput(e);
        });

        // Auto-refresh operations every 30 seconds
        setInterval(() => {
            if (document.getElementById('operations-tab').classList.contains('active')) {
                this.loadOperations();
            }
        }, 30000);
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('file-upload-area');

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.processFile(files[0]);
            }
        });
    }

    async handleFileUpload(event) {
        const file = event.target.files[0];
        if (file) {
            await this.processFile(file);
        }
    }

    async processFile(file) {
        // Validate file type
        const allowedTypes = ['.csv', '.xlsx', '.xls'];
        const fileExtension = file.name.toLowerCase().substr(file.name.lastIndexOf('.'));
        
        if (!allowedTypes.includes(fileExtension)) {
            this.showAlert('Invalid file type. Please use CSV or Excel files.', 'error');
            return;
        }

        // Validate file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
            this.showAlert('File size too large. Maximum 10MB allowed.', 'error');
            return;
        }

        this.showLoading('Uploading and processing file...');

        try {
            const result = await apiClient.uploadKeywordFile(file);
            
            if (result.success) {
                this.keywords = result.keywords;
                this.displayKeywordsPreview();
                this.showAlert(`Successfully loaded ${result.totalKeywords} keywords!`, 'success');
            } else {
                this.showAlert(result.error || 'Failed to process file', 'error');
            }
        } catch (error) {
            this.showAlert('Error uploading file: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async handleTextInput(event) {
        const text = event.target.value.trim();
        
        if (text.length === 0) {
            this.keywords = [];
            this.hideKeywordsPreview();
            return;
        }

        // Debounce the parsing
        clearTimeout(this.parseTimeout);
        this.parseTimeout = setTimeout(async () => {
            try {
                const defaultSettings = this.getDefaultSettings();
                const result = await apiClient.parseKeywordText(text, defaultSettings);
                
                if (result.success) {
                    this.keywords = result.keywords;
                    this.displayKeywordsPreview();
                } else {
                    this.showAlert(result.error || 'Failed to parse keywords', 'error');
                }
            } catch (error) {
                this.showAlert('Error parsing keywords: ' + error.message, 'error');
            }
        }, 500);
    }

    getDefaultSettings() {
        return {
            industry: document.getElementById('industry').value,
            contentType: document.getElementById('content-type').value,
            tone: document.getElementById('tone').value,
            wordCount: parseInt(document.getElementById('word-count').value),
            language: document.getElementById('language').value,
            location: document.getElementById('location').value
        };
    }

    displayKeywordsPreview() {
        const previewDiv = document.getElementById('keywords-preview');
        const countSpan = document.getElementById('keyword-count');
        const listDiv = document.getElementById('keyword-list');

        countSpan.textContent = this.keywords.length;

        // Show first 10 keywords as preview
        const previewKeywords = this.keywords.slice(0, 10);
        listDiv.innerHTML = `
            <div class="keyword-preview">
                ${previewKeywords.map(k => `<span class="keyword-tag">${k.keyword}</span>`).join('')}
                ${this.keywords.length > 10 ? `<span class="keyword-tag">... and ${this.keywords.length - 10} more</span>` : ''}
            </div>
            <style>
                .keyword-preview { margin: 10px 0; }
                .keyword-tag { 
                    display: inline-block; 
                    background: #e3f2fd; 
                    color: #1976d2; 
                    padding: 4px 8px; 
                    margin: 2px; 
                    border-radius: 4px; 
                    font-size: 12px; 
                }
            </style>
        `;

        previewDiv.style.display = 'block';
        document.getElementById('bulk-settings').style.display = 'block';
    }

    hideKeywordsPreview() {
        document.getElementById('keywords-preview').style.display = 'none';
        document.getElementById('bulk-settings').style.display = 'none';
    }

    async startBulkOperation() {
        if (this.keywords.length === 0) {
            this.showAlert('Please upload keywords first', 'error');
            return;
        }

        const operationType = document.getElementById('operationType').value;
        const settings = this.getDefaultSettings();

        this.showLoading('Starting bulk operation...');

        try {
            let result;
            
            switch (operationType) {
                case 'content_generation':
                    result = await apiClient.startBulkContentGeneration(this.keywords, settings);
                    break;
                case 'meta_tags':
                    result = await apiClient.startBulkMetaGeneration(this.keywords, settings);
                    break;
                case 'competitor_analysis':
                    result = await apiClient.startBulkCompetitorAnalysis(this.keywords, settings);
                    break;
                default:
                    throw new Error('Invalid operation type');
            }

            if (result.success) {
                this.showAlert(`Bulk operation started! Processing ${result.totalKeywords} keywords.`, 'success');
                this.currentOperation = result.operationId;
                
                // Switch to operations tab and start monitoring
                this.switchTab('operations');
                this.startOperationMonitoring(result.operationId);
                
                // Reset form
                this.resetForm();
            } else {
                this.showAlert(result.error || 'Failed to start bulk operation', 'error');
            }
        } catch (error) {
            this.showAlert('Error starting operation: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    resetForm() {
        this.keywords = [];
        document.getElementById('file-input').value = '';
        document.getElementById('keyword-textarea').value = '';
        this.hideKeywordsPreview();
    }

    async loadOperations() {
        const loadingDiv = document.getElementById('operations-loading');
        const listDiv = document.getElementById('operations-list');

        loadingDiv.style.display = 'block';
        listDiv.innerHTML = '';

        try {
            const result = await apiClient.getBulkOperations();
            
            if (result.success) {
                this.displayOperations(result.operations);
            } else {
                listDiv.innerHTML = `<p class="text-center">Failed to load operations: ${result.error}</p>`;
            }
        } catch (error) {
            listDiv.innerHTML = `<p class="text-center">Error loading operations: ${error.message}</p>`;
        } finally {
            loadingDiv.style.display = 'none';
        }
    }

    displayOperations(operations) {
        const listDiv = document.getElementById('operations-list');

        if (operations.length === 0) {
            listDiv.innerHTML = '<p class="text-center">No bulk operations found. Start your first bulk operation!</p>';
            return;
        }

        listDiv.innerHTML = operations.map(op => `
            <div class="operation-card">
                <div class="operation-header">
                    <div>
                        <h4>${this.formatOperationType(op.operation_type)}</h4>
                        <span class="operation-status status-${op.status}">${op.status}</span>
                    </div>
                    <div class="operation-actions">
                        ${op.status === 'processing' || op.status === 'queued' ? 
                            `<button class="btn btn-sm btn-secondary" onclick="bulkManager.cancelOperation('${op.id}')">Cancel</button>` : ''}
                        ${op.status === 'completed' ? 
                            `<button class="btn btn-sm btn-success" onclick="bulkManager.createExport('${op.id}')">Export</button>` : ''}
                        <button class="btn btn-sm btn-primary" onclick="bulkManager.viewOperationDetails('${op.id}')">Details</button>
                    </div>
                </div>
                
                <div class="operation-info">
                    <p><strong>Keywords:</strong> ${op.total_keywords}</p>
                    <p><strong>Progress:</strong> ${op.processed_keywords}/${op.total_keywords} (${Math.round(op.progress_percentage)}%)</p>
                    ${op.successful_keywords ? `<p><strong>Successful:</strong> ${op.successful_keywords}</p>` : ''}
                    ${op.failed_keywords ? `<p><strong>Failed:</strong> ${op.failed_keywords}</p>` : ''}
                    <p><strong>Created:</strong> ${new Date(op.created_at).toLocaleString()}</p>
                    ${op.estimated_completion ? `<p><strong>Estimated Completion:</strong> ${new Date(op.estimated_completion).toLocaleString()}</p>` : ''}
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${op.progress_percentage}%"></div>
                </div>
            </div>
        `).join('');
    }

    formatOperationType(type) {
        const types = {
            'content_generation': 'Content Generation',
            'meta_tags': 'Meta Tags',
            'competitor_analysis': 'Competitor Analysis'
        };
        return types[type] || type;
    }

    startOperationMonitoring(operationId) {
        // Clear any existing polling
        if (this.operationPollingInterval) {
            clearInterval(this.operationPollingInterval);
        }

        // Poll for updates every 10 seconds
        this.operationPollingInterval = setInterval(async () => {
            try {
                const result = await apiClient.getBulkOperationStatus(operationId);
                
                if (result.success) {
                    const operation = result.operation;
                    
                    // Stop polling if operation is completed or failed
                    if (operation.status === 'completed' || operation.status === 'failed') {
                        clearInterval(this.operationPollingInterval);
                        this.operationPollingInterval = null;
                        
                        // Refresh the operations list
                        this.loadOperations();
                        
                        // Show completion notification
                        if (operation.status === 'completed') {
                            this.showAlert(`Bulk operation completed! ${operation.successful_keywords} keywords processed successfully.`, 'success');
                        } else {
                            this.showAlert('Bulk operation failed. Please check the details.', 'error');
                        }
                    } else {
                        // Refresh the operations list to show updated progress
                        this.loadOperations();
                    }
                }
            } catch (error) {
                console.error('Error polling operation status:', error);
            }
        }, 10000);
    }

    async cancelOperation(operationId) {
        if (confirm('Are you sure you want to cancel this operation?')) {
            try {
                const result = await apiClient.cancelBulkOperation(operationId);
                
                if (result.success) {
                    this.showAlert('Operation cancelled successfully', 'success');
                    this.loadOperations();
                } else {
                    this.showAlert(result.error || 'Failed to cancel operation', 'error');
                }
            } catch (error) {
                this.showAlert('Error cancelling operation: ' + error.message, 'error');
            }
        }
    }

    async createExport(operationId, format = 'zip') {
        this.showLoading('Creating export...');

        try {
            const result = await apiClient.createBulkExport(operationId, format);
            
            if (result.success) {
                this.showAlert('Export created successfully! Check the Downloads tab.', 'success');
                this.loadExports();
            } else {
                this.showAlert(result.error || 'Failed to create export', 'error');
            }
        } catch (error) {
            this.showAlert('Error creating export: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async loadExports() {
        const loadingDiv = document.getElementById('exports-loading');
        const listDiv = document.getElementById('exports-list');

        loadingDiv.style.display = 'block';
        listDiv.innerHTML = '';

        try {
            const result = await apiClient.getBulkExports();
            
            if (result.success) {
                this.displayExports(result.exports);
            } else {
                listDiv.innerHTML = `<p class="text-center">Failed to load exports: ${result.error}</p>`;
            }
        } catch (error) {
            listDiv.innerHTML = `<p class="text-center">Error loading exports: ${error.message}</p>`;
        } finally {
            loadingDiv.style.display = 'none';
        }
    }

    displayExports(exports) {
        const listDiv = document.getElementById('exports-list');

        if (exports.length === 0) {
            listDiv.innerHTML = '<p class="text-center">No exports found. Complete a bulk operation to create exports!</p>';
            return;
        }

        listDiv.innerHTML = exports.map(exp => `
            <div class="operation-card">
                <div class="operation-header">
                    <div>
                        <h4>${this.formatOperationType(exp.bulk_operations.operation_type)} Export</h4>
                        <span class="export-format">${exp.export_format.toUpperCase()}</span>
                    </div>
                    <div class="operation-actions">
                        <button class="btn btn-sm btn-primary" onclick="bulkManager.downloadExport('${exp.id}')">
                            <i class="fas fa-download"></i> Download
                        </button>
                    </div>
                </div>
                
                <div class="operation-info">
                    <p><strong>Keywords:</strong> ${exp.bulk_operations.total_keywords}</p>
                    <p><strong>File Size:</strong> ${this.formatFileSize(exp.file_size)}</p>
                    <p><strong>Downloads:</strong> ${exp.download_count}</p>
                    <p><strong>Created:</strong> ${new Date(exp.created_at).toLocaleString()}</p>
                    <p><strong>Expires:</strong> ${new Date(exp.expires_at).toLocaleString()}</p>
                </div>
            </div>
        `).join('');
    }

    async downloadExport(exportId) {
        try {
            const result = await apiClient.downloadBulkExport(exportId);
            
            if (result.success) {
                this.showAlert(`Download started: ${result.filename}`, 'success');
                this.loadExports(); // Refresh to update download count
            } else {
                this.showAlert(result.error || 'Failed to download export', 'error');
            }
        } catch (error) {
            this.showAlert('Error downloading export: ' + error.message, 'error');
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async viewOperationDetails(operationId) {
        // This could open a modal or redirect to a detailed view
        // For now, let's show a simple alert with more info
        try {
            const result = await apiClient.getBulkOperationStatus(operationId);
            
            if (result.success) {
                const op = result.operation;
                alert(`Operation Details:
                
Type: ${this.formatOperationType(op.operation_type)}
Status: ${op.status}
Total Keywords: ${op.total_keywords}
Processed: ${op.processed_keywords}
Successful: ${op.successful_keywords}
Failed: ${op.failed_keywords}
Progress: ${Math.round(op.progress_percentage)}%
Created: ${new Date(op.created_at).toLocaleString()}
${op.estimated_completion ? `Estimated Completion: ${new Date(op.estimated_completion).toLocaleString()}` : ''}`);
            } else {
                this.showAlert(result.error || 'Failed to load operation details', 'error');
            }
        } catch (error) {
            this.showAlert('Error loading operation details: ' + error.message, 'error');
        }
    }

    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container');
        const alertId = 'alert-' + Date.now();
        
        const alertDiv = document.createElement('div');
        alertDiv.id = alertId;
        alertDiv.className = `alert alert-${type}`;
        alertDiv.innerHTML = `
            ${message}
            <button onclick="document.getElementById('${alertId}').remove()" style="float: right; background: none; border: none; font-size: 16px; cursor: pointer;">&times;</button>
        `;
        
        alertContainer.appendChild(alertDiv);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            const alertElement = document.getElementById(alertId);
            if (alertElement) {
                alertElement.remove();
            }
        }, 5000);
    }

    showLoading(message = 'Loading...') {
        // Create and show loading overlay
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'loading-overlay';
        loadingOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
            font-size: 18px;
        `;
        loadingOverlay.innerHTML = `
            <div style="text-align: center;">
                <div class="spinner" style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 40px; height: 40px; animation: spin 2s linear infinite; margin: 0 auto 20px;"></div>
                <p>${message}</p>
            </div>
        `;
        
        document.body.appendChild(loadingOverlay);
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.remove();
        }
    }
}

// Global functions for HTML onclick handlers
function switchTab(tabName) {
    // Remove active class from all tabs and tab contents
    document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    
    // Add active class to selected tab and content
    document.querySelector(`.tab:nth-child(${['upload', 'operations', 'exports'].indexOf(tabName) + 1})`).classList.add('active');
    document.getElementById(`${tabName}-tab`).classList.add('active');
    
    // Load data for specific tabs
    if (tabName === 'operations') {
        bulkManager.loadOperations();
    } else if (tabName === 'exports') {
        bulkManager.loadExports();
    }
}

function toggleInputMethod() {
    const inputMethod = document.querySelector('input[name="inputMethod"]:checked').value;
    const fileArea = document.getElementById('file-upload-area');
    const textArea = document.getElementById('text-input-area');
    
    if (inputMethod === 'file') {
        fileArea.style.display = 'block';
        textArea.style.display = 'none';
    } else {
        fileArea.style.display = 'none';
        textArea.style.display = 'block';
    }
    
    // Reset form
    bulkManager.resetForm();
}

function logout() {
    if (apiClient) {
        apiClient.logout();
    }
}

// Initialize bulk processing manager when page loads
let bulkManager;
document.addEventListener('DOMContentLoaded', function() {
    bulkManager = new BulkProcessingManager();
});