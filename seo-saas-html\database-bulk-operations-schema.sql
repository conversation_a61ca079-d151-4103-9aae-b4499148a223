-- Bulk Operations Schema Extensions for SEO Pro
-- Execute this in Supabase SQL Editor after the main schema to add bulk processing capabilities

-- Create bulk_operations table to track batch jobs
CREATE TABLE IF NOT EXISTS bulk_operations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) NOT NULL,
    project_id UUID REFERENCES projects(id),
    operation_type TEXT NOT NULL CHECK (operation_type IN ('content_generation', 'seo_analysis', 'meta_tags', 'competitor_analysis')),
    status TEXT DEFAULT 'queued' CHECK (status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),
    total_keywords INTEGER NOT NULL,
    processed_keywords INTEGER DEFAULT 0,
    successful_keywords INTEGER DEFAULT 0,
    failed_keywords INTEGER DEFAULT 0,
    progress_percentage DECIMAL(5,2) DEFAULT 0.0,
    settings JSONB DEFAULT '{}', -- Common settings for all keywords in the batch
    error_message TEXT,
    estimated_completion TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create bulk_keywords table for individual keywords within batch operations
CREATE TABLE IF NOT EXISTS bulk_keywords (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    bulk_operation_id UUID REFERENCES bulk_operations(id) ON DELETE CASCADE NOT NULL,
    keyword TEXT NOT NULL,
    status TEXT DEFAULT 'queued' CHECK (status IN ('queued', 'processing', 'completed', 'failed')),
    processing_order INTEGER NOT NULL, -- Order in which to process keywords
    input_data JSONB DEFAULT '{}', -- Keyword-specific settings (industry, tone, etc.)
    output_data JSONB DEFAULT '{}', -- Generated results
    error_message TEXT,
    processing_started_at TIMESTAMP WITH TIME ZONE,
    processing_completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create bulk_results table for storing processed results
CREATE TABLE IF NOT EXISTS bulk_results (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    bulk_operation_id UUID REFERENCES bulk_operations(id) ON DELETE CASCADE NOT NULL,
    bulk_keyword_id UUID REFERENCES bulk_keywords(id) ON DELETE CASCADE NOT NULL,
    result_type TEXT NOT NULL CHECK (result_type IN ('content', 'analysis', 'meta_tags', 'competitor_data')),
    result_data JSONB NOT NULL, -- The actual generated content/analysis
    quality_score DECIMAL(5,2),
    word_count INTEGER,
    seo_score DECIMAL(5,2),
    export_formats JSONB DEFAULT '[]', -- Available export formats for this result
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create bulk_exports table for tracking export/download operations
CREATE TABLE IF NOT EXISTS bulk_exports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    bulk_operation_id UUID REFERENCES bulk_operations(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES profiles(id) NOT NULL,
    export_format TEXT NOT NULL CHECK (export_format IN ('zip', 'csv', 'excel', 'json')),
    file_path TEXT, -- Path to generated export file
    file_size BIGINT, -- File size in bytes
    download_count INTEGER DEFAULT 0,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bulk_operations_user_id ON bulk_operations(user_id);
CREATE INDEX IF NOT EXISTS idx_bulk_operations_status ON bulk_operations(status);
CREATE INDEX IF NOT EXISTS idx_bulk_operations_created_at ON bulk_operations(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_bulk_keywords_operation_id ON bulk_keywords(bulk_operation_id);
CREATE INDEX IF NOT EXISTS idx_bulk_keywords_status ON bulk_keywords(status);
CREATE INDEX IF NOT EXISTS idx_bulk_keywords_processing_order ON bulk_keywords(bulk_operation_id, processing_order);
CREATE INDEX IF NOT EXISTS idx_bulk_results_operation_id ON bulk_results(bulk_operation_id);
CREATE INDEX IF NOT EXISTS idx_bulk_results_keyword_id ON bulk_results(bulk_keyword_id);
CREATE INDEX IF NOT EXISTS idx_bulk_exports_user_id ON bulk_exports(user_id);
CREATE INDEX IF NOT EXISTS idx_bulk_exports_expires_at ON bulk_exports(expires_at);

-- Enable Row Level Security (RLS)
ALTER TABLE bulk_operations ENABLE ROW LEVEL SECURITY;
ALTER TABLE bulk_keywords ENABLE ROW LEVEL SECURITY;
ALTER TABLE bulk_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE bulk_exports ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for bulk_operations
CREATE POLICY "Users can view own bulk operations" ON bulk_operations FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create bulk operations" ON bulk_operations FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own bulk operations" ON bulk_operations FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own bulk operations" ON bulk_operations FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for bulk_keywords
CREATE POLICY "Users can view keywords from own operations" ON bulk_keywords FOR SELECT 
USING (EXISTS (SELECT 1 FROM bulk_operations WHERE id = bulk_operation_id AND user_id = auth.uid()));
CREATE POLICY "Users can create keywords for own operations" ON bulk_keywords FOR INSERT 
WITH CHECK (EXISTS (SELECT 1 FROM bulk_operations WHERE id = bulk_operation_id AND user_id = auth.uid()));
CREATE POLICY "Users can update keywords from own operations" ON bulk_keywords FOR UPDATE 
USING (EXISTS (SELECT 1 FROM bulk_operations WHERE id = bulk_operation_id AND user_id = auth.uid()));
CREATE POLICY "Users can delete keywords from own operations" ON bulk_keywords FOR DELETE 
USING (EXISTS (SELECT 1 FROM bulk_operations WHERE id = bulk_operation_id AND user_id = auth.uid()));

-- Create RLS policies for bulk_results
CREATE POLICY "Users can view results from own operations" ON bulk_results FOR SELECT 
USING (EXISTS (SELECT 1 FROM bulk_operations WHERE id = bulk_operation_id AND user_id = auth.uid()));
CREATE POLICY "Users can create results for own operations" ON bulk_results FOR INSERT 
WITH CHECK (EXISTS (SELECT 1 FROM bulk_operations WHERE id = bulk_operation_id AND user_id = auth.uid()));
CREATE POLICY "Users can update results from own operations" ON bulk_results FOR UPDATE 
USING (EXISTS (SELECT 1 FROM bulk_operations WHERE id = bulk_operation_id AND user_id = auth.uid()));
CREATE POLICY "Users can delete results from own operations" ON bulk_results FOR DELETE 
USING (EXISTS (SELECT 1 FROM bulk_operations WHERE id = bulk_operation_id AND user_id = auth.uid()));

-- Create RLS policies for bulk_exports
CREATE POLICY "Users can view own bulk exports" ON bulk_exports FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create bulk exports" ON bulk_exports FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own bulk exports" ON bulk_exports FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own bulk exports" ON bulk_exports FOR DELETE USING (auth.uid() = user_id);

-- Create triggers for updated_at
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON bulk_operations FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

-- Create function to automatically update progress when keywords are processed
CREATE OR REPLACE FUNCTION update_bulk_operation_progress()
RETURNS TRIGGER AS $$
BEGIN
    -- Update progress statistics when a keyword status changes
    IF TG_OP = 'UPDATE' AND (OLD.status != NEW.status) THEN
        UPDATE bulk_operations SET
            processed_keywords = (
                SELECT COUNT(*) FROM bulk_keywords 
                WHERE bulk_operation_id = NEW.bulk_operation_id 
                AND status IN ('completed', 'failed')
            ),
            successful_keywords = (
                SELECT COUNT(*) FROM bulk_keywords 
                WHERE bulk_operation_id = NEW.bulk_operation_id 
                AND status = 'completed'
            ),
            failed_keywords = (
                SELECT COUNT(*) FROM bulk_keywords 
                WHERE bulk_operation_id = NEW.bulk_operation_id 
                AND status = 'failed'
            ),
            progress_percentage = ROUND(
                (SELECT COUNT(*) FROM bulk_keywords 
                 WHERE bulk_operation_id = NEW.bulk_operation_id 
                 AND status IN ('completed', 'failed')) * 100.0 / 
                (SELECT total_keywords FROM bulk_operations WHERE id = NEW.bulk_operation_id), 2
            ),
            updated_at = NOW()
        WHERE id = NEW.bulk_operation_id;
        
        -- Update operation status if all keywords are processed
        UPDATE bulk_operations SET
            status = CASE 
                WHEN processed_keywords = total_keywords THEN 
                    CASE WHEN failed_keywords = 0 THEN 'completed' ELSE 'completed' END
                ELSE status
            END,
            completed_at = CASE 
                WHEN processed_keywords = total_keywords THEN NOW()
                ELSE completed_at
            END
        WHERE id = NEW.bulk_operation_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update progress automatically
CREATE TRIGGER update_progress_on_keyword_status_change
    AFTER UPDATE ON bulk_keywords
    FOR EACH ROW
    EXECUTE FUNCTION update_bulk_operation_progress();

-- Create function to clean up expired bulk exports
CREATE OR REPLACE FUNCTION cleanup_expired_bulk_exports()
RETURNS void AS $$
BEGIN
    DELETE FROM bulk_exports 
    WHERE expires_at < NOW() - INTERVAL '1 day';
END;
$$ LANGUAGE plpgsql;

-- Create function to estimate completion time for bulk operations
CREATE OR REPLACE FUNCTION estimate_bulk_completion_time(operation_id UUID)
RETURNS TIMESTAMP WITH TIME ZONE AS $$
DECLARE
    avg_processing_time INTERVAL;
    remaining_keywords INTEGER;
    estimated_completion TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Calculate average processing time per keyword from recent operations
    SELECT AVG(processing_completed_at - processing_started_at) INTO avg_processing_time
    FROM bulk_keywords 
    WHERE status = 'completed' 
    AND processing_started_at > NOW() - INTERVAL '24 hours'
    LIMIT 100;
    
    -- Default to 30 seconds per keyword if no recent data
    IF avg_processing_time IS NULL THEN
        avg_processing_time := INTERVAL '30 seconds';
    END IF;
    
    -- Get remaining keywords count
    SELECT total_keywords - processed_keywords INTO remaining_keywords
    FROM bulk_operations
    WHERE id = operation_id;
    
    -- Calculate estimated completion
    estimated_completion := NOW() + (avg_processing_time * remaining_keywords);
    
    RETURN estimated_completion;
END;
$$ LANGUAGE plpgsql;

-- Add helpful views for bulk operation monitoring
CREATE OR REPLACE VIEW bulk_operation_summary AS
SELECT 
    bo.id,
    bo.user_id,
    bo.operation_type,
    bo.status,
    bo.total_keywords,
    bo.processed_keywords,
    bo.successful_keywords,
    bo.failed_keywords,
    bo.progress_percentage,
    bo.created_at,
    bo.started_at,
    bo.completed_at,
    CASE 
        WHEN bo.status = 'processing' THEN estimate_bulk_completion_time(bo.id)
        ELSE bo.completed_at
    END as estimated_completion,
    COUNT(br.id) as total_results
FROM bulk_operations bo
LEFT JOIN bulk_results br ON bo.id = br.bulk_operation_id
GROUP BY bo.id, bo.user_id, bo.operation_type, bo.status, bo.total_keywords, 
         bo.processed_keywords, bo.successful_keywords, bo.failed_keywords, 
         bo.progress_percentage, bo.created_at, bo.started_at, bo.completed_at;

-- Add comment to document the schema extension
COMMENT ON TABLE bulk_operations IS 'Tracks batch processing jobs for multiple keywords';
COMMENT ON TABLE bulk_keywords IS 'Individual keywords within a bulk operation';
COMMENT ON TABLE bulk_results IS 'Generated results for each processed keyword';
COMMENT ON TABLE bulk_exports IS 'Export files and download tracking for bulk operations';