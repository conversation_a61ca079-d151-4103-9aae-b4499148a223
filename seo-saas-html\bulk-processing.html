<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Processing - SEO Pro</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <style>
        .bulk-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .bulk-section {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .upload-area {
            border: 2px dashed #e0e0e0;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #4CAF50;
            background-color: #f9f9f9;
        }

        .upload-area.dragover {
            border-color: #4CAF50;
            background-color: #e8f5e9;
        }

        .upload-icon {
            font-size: 48px;
            color: #999;
            margin-bottom: 20px;
        }

        .keyword-textarea {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-family: monospace;
            resize: vertical;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group select,
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .operation-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }

        .operation-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .operation-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-completed { background: #d4edda; color: #155724; }
        .status-processing { background: #fff3cd; color: #856404; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .status-queued { background: #d1ecf1; color: #0c5460; }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            transition: width 0.3s ease;
        }

        .keyword-count {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid #e0e0e0;
            margin-bottom: 20px;
        }

        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }

        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .results-table th,
        .results-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .results-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="nav-container">
                <div class="logo">
                    <h1>SEO Pro</h1>
                </div>
                <nav class="nav">
                    <a href="index.html">Dashboard</a>
                    <a href="content-generator.html">Content Generator</a>
                    <a href="bulk-processing.html" class="active">Bulk Processing</a>
                    <a href="projects.html">Projects</a>
                </nav>
                <div class="user-menu">
                    <button class="btn btn-secondary" onclick="logout()">Logout</button>
                </div>
            </div>
        </header>

        <main class="bulk-container">
            <div class="bulk-section">
                <h2><i class="fas fa-tasks"></i> Bulk Processing</h2>
                <p>Process hundreds of keywords simultaneously for content generation, meta tags, and competitor analysis.</p>

                <!-- Tabs -->
                <div class="tabs">
                    <div class="tab active" onclick="switchTab('upload')">Upload Keywords</div>
                    <div class="tab" onclick="switchTab('operations')">My Operations</div>
                    <div class="tab" onclick="switchTab('exports')">Downloads</div>
                </div>

                <!-- Upload Tab -->
                <div id="upload-tab" class="tab-content active">
                    <div class="settings-grid">
                        <div>
                            <h3>Input Method</h3>
                            <div class="form-group">
                                <label>
                                    <input type="radio" name="inputMethod" value="file" checked onchange="toggleInputMethod()"> 
                                    Upload File (CSV/Excel)
                                </label>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="radio" name="inputMethod" value="text" onchange="toggleInputMethod()"> 
                                    Paste Keywords
                                </label>
                            </div>
                        </div>

                        <div>
                            <h3>Operation Type</h3>
                            <div class="form-group">
                                <select id="operationType">
                                    <option value="content_generation">Content Generation</option>
                                    <option value="meta_tags">Meta Tags</option>
                                    <option value="competitor_analysis">Competitor Analysis</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- File Upload Area -->
                    <div id="file-upload-area" class="upload-area" onclick="document.getElementById('file-input').click()">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h3>Drop your CSV or Excel file here</h3>
                        <p>Or click to browse files</p>
                        <p><small>Max file size: 10MB | Formats: .csv, .xlsx, .xls</small></p>
                        <input type="file" id="file-input" accept=".csv,.xlsx,.xls" style="display: none;" onchange="handleFileUpload(event)">
                    </div>

                    <!-- Text Input Area -->
                    <div id="text-input-area" style="display: none;">
                        <h3>Paste Keywords (one per line)</h3>
                        <textarea id="keyword-textarea" class="keyword-textarea" placeholder="seo tools&#10;content marketing&#10;digital marketing&#10;keyword research&#10;link building"></textarea>
                    </div>

                    <!-- Keywords Preview -->
                    <div id="keywords-preview" style="display: none;">
                        <h3>Keywords Preview</h3>
                        <div class="keyword-count">
                            <strong>Total Keywords: <span id="keyword-count">0</span></strong>
                        </div>
                        <div id="keyword-list"></div>
                    </div>

                    <!-- Settings -->
                    <div id="bulk-settings" style="display: none;">
                        <h3>Bulk Settings</h3>
                        <div class="settings-grid">
                            <div class="form-group">
                                <label for="industry">Industry</label>
                                <select id="industry">
                                    <option value="">Select Industry</option>
                                    <option value="technology">Technology</option>
                                    <option value="healthcare">Healthcare</option>
                                    <option value="finance">Finance</option>
                                    <option value="ecommerce">E-commerce</option>
                                    <option value="realestate">Real Estate</option>
                                    <option value="legal">Legal</option>
                                    <option value="education">Education</option>
                                    <option value="travel">Travel</option>
                                    <option value="food">Food & Restaurant</option>
                                    <option value="fitness">Fitness & Health</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="content-type">Content Type</label>
                                <select id="content-type">
                                    <option value="article">Article</option>
                                    <option value="blog_post">Blog Post</option>
                                    <option value="product_description">Product Description</option>
                                    <option value="landing_page">Landing Page</option>
                                    <option value="social_media_post">Social Media Post</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="tone">Tone</label>
                                <select id="tone">
                                    <option value="professional">Professional</option>
                                    <option value="casual">Casual</option>
                                    <option value="friendly">Friendly</option>
                                    <option value="authoritative">Authoritative</option>
                                    <option value="conversational">Conversational</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="word-count">Word Count</label>
                                <input type="number" id="word-count" min="300" max="3000" value="1000">
                            </div>

                            <div class="form-group">
                                <label for="language">Language</label>
                                <select id="language">
                                    <option value="en">English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                    <option value="de">German</option>
                                    <option value="it">Italian</option>
                                    <option value="pt">Portuguese</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="location">Location</label>
                                <select id="location">
                                    <option value="United States">United States</option>
                                    <option value="United Kingdom">United Kingdom</option>
                                    <option value="Canada">Canada</option>
                                    <option value="Australia">Australia</option>
                                    <option value="Germany">Germany</option>
                                    <option value="France">France</option>
                                    <option value="Spain">Spain</option>
                                    <option value="Italy">Italy</option>
                                </select>
                            </div>
                        </div>

                        <div style="margin-top: 30px;">
                            <button class="btn btn-primary" onclick="startBulkOperation()">
                                <i class="fas fa-play"></i> Start Bulk Processing
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Operations Tab -->
                <div id="operations-tab" class="tab-content">
                    <div class="loading" id="operations-loading">
                        <div class="spinner"></div>
                        <p>Loading operations...</p>
                    </div>
                    
                    <div id="operations-list">
                        <!-- Operations will be loaded here -->
                    </div>
                </div>

                <!-- Exports Tab -->
                <div id="exports-tab" class="tab-content">
                    <div class="loading" id="exports-loading">
                        <div class="spinner"></div>
                        <p>Loading exports...</p>
                    </div>
                    
                    <div id="exports-list">
                        <!-- Exports will be loaded here -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Alert Container -->
    <div id="alert-container" style="position: fixed; top: 20px; right: 20px; z-index: 1000; width: 300px;"></div>

    <script src="js/config.js"></script>
    <script src="js/api.js"></script>
    <script src="js/bulk-processing.js"></script>
</body>
</html>