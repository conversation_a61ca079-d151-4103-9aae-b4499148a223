import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { z } from 'zod';
import { logUserActivity } from '@/lib/supabase';

const signInSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const { email, password } = signInSchema.parse(body);
    
    const supabase = createSupabaseServerClient();
    
    // Attempt sign in
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      // Log failed sign-in attempt
      await logUserActivity({
        action_type: 'signin_failed',
        action_details: {
          email,
          error: error.message,
        },
        ip_address: request.ip,
        user_agent: request.headers.get('user-agent') || undefined,
      });
      
      return NextResponse.json(
        { error: error.message },
        { status: 401 }
      );
    }
    
    // Log successful sign-in
    await logUserActivity({
      user_id: data.user?.id,
      action_type: 'signin_success',
      action_details: {
        email,
        method: 'email_password',
      },
      ip_address: request.ip,
      user_agent: request.headers.get('user-agent') || undefined,
    });
    
    return NextResponse.json({
      success: true,
      user: data.user,
      session: data.session,
    });
    
  } catch (error) {
    console.error('Sign-in error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}