'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { createSupabaseComponentClient } from '@/lib/supabase';
import { Profile, UserSubscription } from '@/types/database';
import { ClientOnly } from '@/components/client-only';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: Profile | null;
  subscription: UserSubscription | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, fullName?: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithGitHub: () => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: (updates: Partial<Profile>) => Promise<void>;
  refreshProfile: () => Promise<void>;
  refreshSubscription: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  // Safe Supabase client creation
  const [supabase, setSupabase] = useState<ReturnType<typeof createSupabaseComponentClient> | null>(null);

  useEffect(() => {
    try {
      const client = createSupabaseComponentClient();
      setSupabase(client);
      setMounted(true);
    } catch (err) {
      console.error('Failed to initialize Supabase client:', err);
      setError('Failed to initialize authentication. Please check your configuration.');
      setLoading(false);
      setMounted(true);
    }
  }, []);

  // Fetch user profile
  const fetchProfile = async (userId: string) => {
    if (!supabase) {
      console.error('Supabase client not initialized');
      return null;
    }

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      return null;
    }
  };

  // Fetch user subscription
  const fetchSubscription = async (userId: string) => {
    if (!supabase) {
      console.error('Supabase client not initialized');
      return null;
    }

    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching subscription:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching subscription:', error);
      return null;
    }
  };

  // Initialize auth state
  useEffect(() => {
    if (!supabase) return;

    const initializeAuth = async () => {
      try {
        // Get initial session
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
          setLoading(false);
          return;
        }

        if (session?.user) {
          setSession(session);
          setUser(session.user);
          
          // Fetch profile and subscription in parallel
          const [profileData, subscriptionData] = await Promise.all([
            fetchProfile(session.user.id),
            fetchSubscription(session.user.id),
          ]);
          
          setProfile(profileData);
          setSubscription(subscriptionData);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription: authSubscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);
        
        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          // Fetch profile and subscription for new session
          const [profileData, subscriptionData] = await Promise.all([
            fetchProfile(session.user.id),
            fetchSubscription(session.user.id),
          ]);
          
          setProfile(profileData);
          setSubscription(subscriptionData);
        } else {
          // Clear profile and subscription on sign out
          setProfile(null);
          setSubscription(null);
        }
        
        setLoading(false);
      }
    );

    return () => {
      authSubscription.unsubscribe();
    };
  }, [supabase]);

  // Sign in
  const signIn = async (email: string, password: string) => {
    if (!supabase) {
      throw new Error('Authentication not initialized');
    }

    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      throw new Error(error.message);
    }
  };

  // Sign up
  const signUp = async (email: string, password: string, fullName?: string) => {
    if (!supabase) {
      throw new Error('Authentication not initialized');
    }

    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    });

    if (error) {
      throw new Error(error.message);
    }
  };

  // Sign out
  const signOut = async () => {
    if (!supabase) {
      throw new Error('Authentication not initialized');
    }

    const { error } = await supabase.auth.signOut();

    if (error) {
      throw new Error(error.message);
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    if (!supabase) {
      throw new Error('Authentication not initialized');
    }

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });

    if (error) {
      throw new Error(error.message);
    }
  };

  // Update profile
  const updateProfile = async (updates: Partial<Profile>) => {
    if (!supabase) {
      throw new Error('Authentication not initialized');
    }

    if (!user) {
      throw new Error('No user logged in');
    }

    const { data, error } = await supabase
      .from('profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', user.id)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    setProfile(data);
  };

  // Refresh profile
  const refreshProfile = async () => {
    if (!user) return;
    
    const profileData = await fetchProfile(user.id);
    setProfile(profileData);
  };

  // Refresh subscription
  const refreshSubscription = async () => {
    if (!user) return;
    
    const subscriptionData = await fetchSubscription(user.id);
    setSubscription(subscriptionData);
  };

  // OAuth sign in functions
  const signInWithGoogle = async () => {
    if (!supabase) {
      throw new Error('Authentication not initialized');
    }

    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });

    if (error) {
      throw new Error(error.message);
    }
  };

  const signInWithGitHub = async () => {
    if (!supabase) {
      throw new Error('Authentication not initialized');
    }

    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'github',
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    if (error) {
      throw new Error(error.message);
    }
  };

  const value = {
    user,
    session,
    profile,
    subscription,
    loading,
    signIn,
    signUp,
    signInWithGoogle,
    signInWithGitHub,
    signOut,
    resetPassword,
    updateProfile,
    refreshProfile,
    refreshSubscription,
  };

  // Prevent hydration mismatches by only rendering after mount
  if (!mounted) {
    return (
      <AuthContext.Provider value={value}>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AuthContext.Provider>
    );
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Helper hook for checking subscription status
export function useSubscription() {
  const { subscription } = useAuth();
  
  const hasActiveSubscription = subscription?.status === 'active';
  const isOnTrial = subscription?.status === 'trialing';
  const isPastDue = subscription?.status === 'past_due';
  const isCancelled = subscription?.status === 'cancelled';
  
  const canAccessFeature = (feature: 'pro' | 'enterprise') => {
    if (!subscription) return false;
    
    if (feature === 'pro') {
      return ['pro', 'enterprise'].includes(subscription.plan_type) && hasActiveSubscription;
    }
    
    if (feature === 'enterprise') {
      return subscription.plan_type === 'enterprise' && hasActiveSubscription;
    }
    
    return false;
  };

  return {
    subscription,
    hasActiveSubscription,
    isOnTrial,
    isPastDue,
    isCancelled,
    canAccessFeature,
  };
}

// Helper hook for authentication guards
export function useAuthGuard() {
  const { user, loading } = useAuth();
  
  const isAuthenticated = !!user;
  const isLoading = loading;
  
  return {
    isAuthenticated,
    isLoading,
    user,
  };
}