// Environment Variables Test Page
'use client'

export default function TestEnvPage() {
  const envVars = {
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set (hidden)' : 'Not set',
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-2xl w-full">
        <h1 className="text-2xl font-bold mb-4">Environment Variables Test</h1>
        
        <div className="space-y-3">
          {Object.entries(envVars).map(([key, value]) => (
            <div key={key} className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span className="font-mono text-sm">{key}:</span>
              <span className={`text-sm ${value ? 'text-green-600' : 'text-red-600'}`}>
                {value || 'Not set'}
              </span>
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-blue-50 rounded">
          <h3 className="font-semibold text-blue-800">Instructions:</h3>
          <p className="text-blue-700 text-sm mt-1">
            If any variables show "Not set", restart the development server with: npm run dev
          </p>
        </div>

        <div className="mt-4">
          <a 
            href="/" 
            className="text-blue-600 hover:text-blue-500 text-sm"
          >
            ← Back to Home
          </a>
        </div>
      </div>
    </div>
  );
}
