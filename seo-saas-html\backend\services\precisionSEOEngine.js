const PrecisionCompetitorAnalyzer = require('./competitorAnalyzer');
const AdvancedKeywordDensityCalculator = require('./keywordDensityCalculator');
const HeadingStructureAnalyzer = require('./headingStructureAnalyzer');
const PrecisionContentGenerator = require('./precisionContentGenerator');

class PrecisionSEOEngine {
  constructor() {
    this.competitorAnalyzer = new PrecisionCompetitorAnalyzer();
    this.keywordDensityCalculator = new AdvancedKeywordDensityCalculator();
    this.headingAnalyzer = new HeadingStructureAnalyzer();
    this.contentGenerator = new PrecisionContentGenerator();
  }

  // Main method to generate surgical precision SEO content
  async generatePrecisionSEOContent(keyword, options = {}) {
    try {
      console.log(`Starting precision SEO content generation for: ${keyword}`);
      
      const startTime = Date.now();
      const location = options.location || 'United States';
      const searchEngine = options.searchEngine || 'google';
      const industry = options.industry || 'general';
      const contentType = options.contentType || 'comprehensive guide';

      // Step 1: Analyze top 5 competitors with surgical precision
      console.log('Step 1: Analyzing competitors with surgical precision...');
      const competitorAnalysis = await this.competitorAnalyzer.analyzePrecisionCompetitors(keyword, {
        location,
        searchEngine,
        includeLocal: false
      });

      if (!competitorAnalysis.success) {
        throw new Error(`Competitor analysis failed: ${competitorAnalysis.error}`);
      }

      // Step 2: Calculate precision benchmarks from top 5 competitors
      console.log('Step 2: Calculating precision benchmarks...');
      const precisionBenchmarks = this.calculatePrecisionBenchmarks(competitorAnalysis.competitorMetrics, keyword);

      // Step 3: Analyze heading structures and extract semantic data
      console.log('Step 3: Analyzing heading structures and semantic data...');
      const headingAnalysis = await this.headingAnalyzer.analyzeCompetitorHeadingStructures(
        competitorAnalysis.competitorMetrics, 
        keyword
      );

      if (!headingAnalysis.success) {
        throw new Error(`Heading analysis failed: ${headingAnalysis.error}`);
      }

      // Step 4: Generate surgical precision content
      console.log('Step 4: Generating precision content...');
      const contentResult = await this.contentGenerator.generatePrecisionContent(
        keyword,
        precisionBenchmarks,
        headingAnalysis.semanticData,
        {
          industry,
          contentType,
          targetAudience: options.targetAudience || 'professionals',
          tone: options.tone || 'professional'
        }
      );

      if (!contentResult.success) {
        throw new Error(`Content generation failed: ${contentResult.error}`);
      }

      // Step 5: Final validation and optimization
      console.log('Step 5: Performing final validation...');
      const finalValidation = await this.performFinalValidation(
        contentResult,
        precisionBenchmarks,
        headingAnalysis.semanticData,
        keyword
      );

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      return {
        success: true,
        keyword,
        content: contentResult.content,
        metadata: {
          ...contentResult.metadata,
          processingTimeMs: processingTime,
          competitorAnalysis: {
            competitorsAnalyzed: competitorAnalysis.competitorMetrics.length,
            benchmarks: precisionBenchmarks
          },
          headingAnalysis: {
            top5Averages: headingAnalysis.top5Analysis,
            semanticData: headingAnalysis.semanticData
          },
          finalValidation,
          generatedAt: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('Precision SEO Engine error:', error);
      return {
        success: false,
        error: error.message,
        keyword,
        content: null
      };
    }
  }

  // Calculate precision benchmarks from competitor metrics
  calculatePrecisionBenchmarks(competitorMetrics, keyword) {
    if (!competitorMetrics || competitorMetrics.length === 0) {
      throw new Error('No competitor metrics available for benchmark calculation');
    }

    // Use top 5 competitors for precision benchmarks
    const top5Competitors = competitorMetrics.slice(0, 5);
    
    console.log(`Calculating benchmarks from ${top5Competitors.length} top competitors`);

    // Word count benchmarks (surgical precision)
    const wordCounts = top5Competitors
      .map(c => c.wordCount)
      .filter(wc => wc > 0);
    
    const avgWordCount = Math.round(
      wordCounts.reduce((sum, count) => sum + count, 0) / wordCounts.length
    );

    // Keyword density benchmarks (surgical precision)
    const mainDensities = top5Competitors
      .map(c => c.keywordDensity?.main)
      .filter(d => d > 0);
    
    const avgMainDensity = mainDensities.length > 0 
      ? parseFloat((mainDensities.reduce((sum, d) => sum + d, 0) / mainDensities.length).toFixed(2))
      : 2.0;

    // Component keyword density benchmarks
    const componentBenchmarks = {};
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    
    keywordComponents.forEach(component => {
      const componentDensities = top5Competitors
        .map(c => c.keywordDensity?.components?.[component])
        .filter(d => d !== undefined && d > 0);
      
      if (componentDensities.length > 0) {
        componentBenchmarks[component] = parseFloat(
          (componentDensities.reduce((sum, d) => sum + d, 0) / componentDensities.length).toFixed(2)
        );
      }
    });

    // Heading structure benchmarks
    const headingStats = top5Competitors.map(c => c.headingStructure?.statistics).filter(Boolean);
    const avgHeadingStructure = {
      totalHeadings: Math.round(
        headingStats.reduce((sum, h) => sum + (h.total || 0), 0) / headingStats.length
      ),
      h2Count: Math.round(
        headingStats.reduce((sum, h) => sum + (h.h2Count || 0), 0) / headingStats.length
      ),
      h3Count: Math.round(
        headingStats.reduce((sum, h) => sum + (h.h3Count || 0), 0) / headingStats.length
      )
    };

    // Content structure benchmarks
    const qualityMetrics = top5Competitors.map(c => c.contentQuality).filter(Boolean);
    const avgContentStructure = {
      sentences: Math.round(
        qualityMetrics.reduce((sum, q) => sum + (q.sentenceCount || 0), 0) / qualityMetrics.length
      ),
      paragraphs: Math.round(
        qualityMetrics.reduce((sum, q) => sum + (q.paragraphCount || 0), 0) / qualityMetrics.length
      )
    };

    // Keyword optimization benchmarks
    const keywordOptimization = {
      targetRatio: Math.round(
        headingStats.reduce((sum, h) => sum + (h.keywordOptimizationRatio || 0), 0) / headingStats.length
      )
    };

    const benchmarks = {
      wordCount: {
        target: avgWordCount,
        min: Math.min(...wordCounts),
        max: Math.max(...wordCounts),
        tolerance: 0 // Zero tolerance for word count
      },
      keywordDensity: {
        main: avgMainDensity,
        components: componentBenchmarks
      },
      headingStructure: {
        ...avgHeadingStructure,
        keywordOptimization
      },
      contentStructure: avgContentStructure,
      competitorCount: top5Competitors.length,
      precision: 'surgical', // Indicates surgical precision benchmarks
      calculatedAt: new Date().toISOString()
    };

    console.log('Precision benchmarks calculated:', {
      wordCount: benchmarks.wordCount.target,
      mainKeywordDensity: benchmarks.keywordDensity.main,
      headings: `H2: ${benchmarks.headingStructure.h2Count}, H3: ${benchmarks.headingStructure.h3Count}`,
      competitorCount: benchmarks.competitorCount
    });

    return benchmarks;
  }

  // Perform final validation of generated content
  async performFinalValidation(contentResult, benchmarks, semanticData, keyword) {
    console.log('Performing final validation checks...');

    const validation = {
      precisionCompliance: {},
      qualityAssurance: {},
      eeatCompliance: {},
      semanticIntegration: {},
      overallScore: 0,
      recommendations: []
    };

    // Validate precision compliance
    validation.precisionCompliance = this.validatePrecisionCompliance(
      contentResult.metadata,
      benchmarks,
      keyword
    );

    // Validate quality metrics
    validation.qualityAssurance = this.validateQualityMetrics(contentResult.content);

    // Validate E-E-A-T compliance
    validation.eeatCompliance = this.validateEEATCompliance(contentResult.content);

    // Validate semantic integration
    if (semanticData) {
      validation.semanticIntegration = this.validateSemanticIntegration(
        contentResult.content,
        semanticData
      );
    }

    // Calculate overall validation score
    validation.overallScore = this.calculateOverallValidationScore(validation);

    // Generate improvement recommendations
    validation.recommendations = this.generateImprovementRecommendations(
      validation,
      benchmarks,
      semanticData
    );

    return validation;
  }

  // Validate precision compliance
  validatePrecisionCompliance(metadata, benchmarks, keyword) {
    const compliance = {
      wordCount: {},
      keywordDensity: {},
      headingStructure: {},
      overallCompliance: 0
    };

    // Word count compliance
    const wordCountDiff = Math.abs(metadata.actualWordCount - benchmarks.wordCount.target);
    compliance.wordCount = {
      target: benchmarks.wordCount.target,
      actual: metadata.actualWordCount,
      difference: wordCountDiff,
      compliant: wordCountDiff <= 5, // Allow 5 word tolerance
      accuracy: Math.max(0, 1 - (wordCountDiff / benchmarks.wordCount.target))
    };

    // Keyword density compliance
    const targetMainDensity = benchmarks.keywordDensity.main;
    const actualMainDensity = metadata.keywordDensity?.main?.density || 0;
    const densityDiff = Math.abs(actualMainDensity - targetMainDensity);
    
    compliance.keywordDensity = {
      main: {
        target: targetMainDensity,
        actual: actualMainDensity,
        difference: densityDiff,
        compliant: densityDiff <= 0.2, // Allow 0.2% tolerance
        accuracy: Math.max(0, 1 - (densityDiff / targetMainDensity))
      },
      components: {}
    };

    // Component keyword density compliance
    Object.entries(benchmarks.keywordDensity.components).forEach(([component, targetDensity]) => {
      const actualDensity = metadata.keywordDensity?.components?.[component]?.density || 0;
      const componentDiff = Math.abs(actualDensity - targetDensity);
      
      compliance.keywordDensity.components[component] = {
        target: targetDensity,
        actual: actualDensity,
        difference: componentDiff,
        compliant: componentDiff <= 0.2,
        accuracy: Math.max(0, 1 - (componentDiff / targetDensity))
      };
    });

    // Heading structure compliance
    const headingTarget = benchmarks.headingStructure;
    const headingActual = metadata.headingStructure;
    
    compliance.headingStructure = {
      h1: {
        target: 1,
        actual: headingActual?.h1Count || 0,
        compliant: (headingActual?.h1Count || 0) === 1
      },
      h2: {
        target: headingTarget.h2Count,
        actual: headingActual?.h2Count || 0,
        compliant: (headingActual?.h2Count || 0) === headingTarget.h2Count
      },
      h3: {
        target: headingTarget.h3Count,
        actual: headingActual?.h3Count || 0,
        compliant: (headingActual?.h3Count || 0) === headingTarget.h3Count
      }
    };

    // Calculate overall compliance score
    const wordCountScore = compliance.wordCount.accuracy * 30;
    const keywordScore = compliance.keywordDensity.main.accuracy * 30;
    const headingScore = (
      (compliance.headingStructure.h1.compliant ? 1 : 0) +
      (compliance.headingStructure.h2.compliant ? 1 : 0) +
      (compliance.headingStructure.h3.compliant ? 1 : 0)
    ) / 3 * 40;

    compliance.overallCompliance = Math.round(wordCountScore + keywordScore + headingScore);

    return compliance;
  }

  // Validate quality metrics
  validateQualityMetrics(content) {
    const quality = {
      readability: 0,
      naturalness: 0,
      completeness: 0,
      overallQuality: 0
    };

    // Simple readability assessment
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = content.split(/\s+/).filter(w => w.length > 0);
    const avgSentenceLength = sentences.length > 0 ? words.length / sentences.length : 0;
    
    // Readability score (based on sentence length)
    if (avgSentenceLength >= 15 && avgSentenceLength <= 25) {
      quality.readability = 90;
    } else if (avgSentenceLength >= 10 && avgSentenceLength <= 30) {
      quality.readability = 75;
    } else {
      quality.readability = 60;
    }

    // Naturalness assessment (simplified)
    const hasVariedSentenceStarts = this.assessSentenceVariety(sentences);
    const hasTransitions = this.assessTransitions(content);
    quality.naturalness = (hasVariedSentenceStarts + hasTransitions) / 2 * 100;

    // Completeness assessment
    const hasIntroduction = content.toLowerCase().includes('introduction') || 
                          content.toLowerCase().includes('what is');
    const hasConclusion = content.toLowerCase().includes('conclusion') || 
                         content.toLowerCase().includes('summary');
    const hasExamples = content.toLowerCase().includes('example') || 
                       content.toLowerCase().includes('for instance');
    
    quality.completeness = (
      (hasIntroduction ? 1 : 0) +
      (hasConclusion ? 1 : 0) +
      (hasExamples ? 1 : 0)
    ) / 3 * 100;

    quality.overallQuality = Math.round(
      (quality.readability * 0.4 + quality.naturalness * 0.3 + quality.completeness * 0.3)
    );

    return quality;
  }

  // Validate E-E-A-T compliance
  validateEEATCompliance(content) {
    const contentLower = content.toLowerCase();
    
    const eeat = {
      experience: 0,
      expertise: 0,
      authoritativeness: 0,
      trustworthiness: 0,
      overallEEAT: 0
    };

    // Experience signals
    const experienceSignals = [
      'experience', 'worked with', 'case study', 'real-world', 'practical'
    ];
    eeat.experience = Math.min(100, experienceSignals.filter(signal => 
      contentLower.includes(signal)
    ).length * 20);

    // Expertise signals
    const expertiseSignals = [
      'expert', 'specialist', 'professional', 'research', 'study'
    ];
    eeat.expertise = Math.min(100, expertiseSignals.filter(signal => 
      contentLower.includes(signal)
    ).length * 20);

    // Authority signals
    const authoritySignals = [
      'according to', 'industry standard', 'best practice', 'recognized'
    ];
    eeat.authoritativeness = Math.min(100, authoritySignals.filter(signal => 
      contentLower.includes(signal)
    ).length * 25);

    // Trust signals
    const trustSignals = [
      'guarantee', 'secure', 'verified', 'reliable', 'proven'
    ];
    eeat.trustworthiness = Math.min(100, trustSignals.filter(signal => 
      contentLower.includes(signal)
    ).length * 20);

    eeat.overallEEAT = Math.round(
      (eeat.experience + eeat.expertise + eeat.authoritativeness + eeat.trustworthiness) / 4
    );

    return eeat;
  }

  // Validate semantic integration
  validateSemanticIntegration(content, semanticData) {
    const contentLower = content.toLowerCase();
    
    const integration = {
      lsiKeywords: {},
      entities: {},
      variations: {},
      overallIntegration: 0
    };

    // LSI keyword integration
    let lsiScore = 0;
    semanticData.headingOptimizedTerms.forEach(term => {
      const matches = (contentLower.match(new RegExp(`\\b${term.toLowerCase()}\\b`, 'g')) || []).length;
      integration.lsiKeywords[term] = matches;
      if (matches > 0) lsiScore += 20;
    });
    integration.lsiKeywords.score = Math.min(100, lsiScore);

    // Entity integration
    let entityScore = 0;
    semanticData.prioritizedEntities.slice(0, 5).forEach(entity => {
      const matches = (contentLower.match(new RegExp(`\\b${entity.term.toLowerCase()}\\b`, 'g')) || []).length;
      integration.entities[entity.term] = matches;
      if (matches >= 2) entityScore += 20;
    });
    integration.entities.score = Math.min(100, entityScore);

    // Variation integration
    let variationScore = 0;
    semanticData.prioritizedVariations.slice(0, 5).forEach(variation => {
      const matches = (contentLower.match(new RegExp(`\\b${variation.term.toLowerCase()}\\b`, 'g')) || []).length;
      integration.variations[variation.term] = matches;
      if (matches > 0) variationScore += 20;
    });
    integration.variations.score = Math.min(100, variationScore);

    integration.overallIntegration = Math.round(
      (integration.lsiKeywords.score * 0.5 + 
       integration.entities.score * 0.3 + 
       integration.variations.score * 0.2)
    );

    return integration;
  }

  // Calculate overall validation score
  calculateOverallValidationScore(validation) {
    const scores = [
      validation.precisionCompliance.overallCompliance || 0,
      validation.qualityAssurance.overallQuality || 0,
      validation.eeatCompliance.overallEEAT || 0,
      validation.semanticIntegration.overallIntegration || 0
    ];

    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  }

  // Generate improvement recommendations
  generateImprovementRecommendations(validation, benchmarks, semanticData) {
    const recommendations = [];

    // Precision compliance recommendations
    if (validation.precisionCompliance.overallCompliance < 90) {
      if (!validation.precisionCompliance.wordCount.compliant) {
        recommendations.push({
          type: 'word_count',
          priority: 'critical',
          message: `Adjust word count to exactly ${benchmarks.wordCount.target} words (current: ${validation.precisionCompliance.wordCount.actual})`,
          target: benchmarks.wordCount.target
        });
      }

      if (!validation.precisionCompliance.keywordDensity.main.compliant) {
        recommendations.push({
          type: 'keyword_density',
          priority: 'critical',
          message: `Adjust main keyword density to ${benchmarks.keywordDensity.main}% (current: ${validation.precisionCompliance.keywordDensity.main.actual}%)`,
          target: benchmarks.keywordDensity.main
        });
      }
    }

    // Quality recommendations
    if (validation.qualityAssurance.overallQuality < 80) {
      recommendations.push({
        type: 'content_quality',
        priority: 'high',
        message: 'Improve content readability and naturalness',
        suggestions: [
          'Vary sentence structure and length',
          'Add transition words between paragraphs',
          'Include more examples and practical applications'
        ]
      });
    }

    // E-E-A-T recommendations
    if (validation.eeatCompliance.overallEEAT < 70) {
      recommendations.push({
        type: 'eeat_improvement',
        priority: 'high',
        message: 'Enhance E-E-A-T compliance',
        suggestions: [
          'Add more experience-based examples',
          'Include expert opinions and research citations',
          'Demonstrate industry authority',
          'Add trust signals and verification elements'
        ]
      });
    }

    // Semantic integration recommendations
    if (semanticData && validation.semanticIntegration.overallIntegration < 80) {
      recommendations.push({
        type: 'semantic_integration',
        priority: 'medium',
        message: 'Improve semantic keyword integration',
        suggestions: [
          'Include more LSI keywords naturally throughout content',
          'Mention entities multiple times in relevant contexts',
          'Use keyword variations to capture long-tail searches'
        ]
      });
    }

    return recommendations;
  }

  // Helper methods
  assessSentenceVariety(sentences) {
    const starters = sentences.map(s => s.trim().split(' ')[0]).filter(Boolean);
    const uniqueStarters = new Set(starters);
    return uniqueStarters.size / starters.length;
  }

  assessTransitions(content) {
    const transitions = [
      'however', 'therefore', 'furthermore', 'additionally', 'moreover',
      'consequently', 'meanwhile', 'similarly', 'in contrast', 'for example'
    ];
    const contentLower = content.toLowerCase();
    const foundTransitions = transitions.filter(t => contentLower.includes(t));
    return Math.min(1, foundTransitions.length / 5);
  }
}

module.exports = PrecisionSEOEngine;