import "./globals.css";
import { Inter } from 'next/font/google'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
  fallback: ['system-ui', 'sans-serif'],
})

export const metadata = {
  title: 'RankBoost AI - SEO Content Generation Platform',
  description: 'Create high-ranking, SEO-optimized content with advanced AI technology. Boost your search rankings with AI-powered content creation.',
  keywords: 'SEO, content generation, AI writing, search optimization, digital marketing',
  authors: [{ name: 'RankBoost AI' }],
  creator: 'RankBoost AI',
  publisher: 'RankBoost AI',
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 5,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/manifest.json',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://rankboostai.com',
    siteName: 'RankBoost AI',
    title: 'RankBoost AI - SEO Content Generation Platform',
    description: 'Create high-ranking, SEO-optimized content with advanced AI technology',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'RankBoost AI - SEO Content Generation',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'RankBoost AI - SEO Content Generation Platform',
    description: 'Create high-ranking, SEO-optimized content with advanced AI technology',
    images: ['/og-image.jpg'],
    creator: '@rankboostai',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={inter.variable} suppressHydrationWarning>
      <body 
        className={`${inter.className} antialiased bg-white text-gray-900 selection:bg-blue-200 selection:text-blue-900`}
        suppressHydrationWarning
      >
        {children}
      </body>
    </html>
  );
}