{"name": "seo-saas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "build": "next build", "build:analyze": "ANALYZE=true npm run build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "clean": "rm -rf .next dist", "clean:all": "rm -rf .next dist node_modules package-lock.json", "postinstall": "npm run type-check", "prepare": "husky install", "db:generate-types": "supabase gen types typescript --project-id=$SUPABASE_PROJECT_ID > src/types/database.ts", "preview": "npm run build && npm run start"}, "dependencies": {"@headlessui/react": "^2.0.0", "@heroicons/react": "^2.0.18", "@radix-ui/react-slot": "^1.1.0", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.39.0", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "groq-sdk": "^0.3.3", "lucide-react": "^0.468.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.8.0", "tailwind-merge": "^2.1.0", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^16.0.0", "@types/jest": "^29.5.0", "@types/node": "^20", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.0.0", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.6", "prettier": "^3.1.0", "tailwindcss": "^3.4.0", "typescript": "^5"}}