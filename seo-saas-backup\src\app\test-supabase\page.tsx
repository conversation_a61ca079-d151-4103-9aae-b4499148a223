// Test Supabase Connection
// Simple page to verify Supabase is working correctly

'use client'

import * as React from "react"
import { supabase } from "@/lib/supabase"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function TestSupabasePage() {
  const [connectionStatus, setConnectionStatus] = React.useState<'testing' | 'connected' | 'error'>('testing')
  const [error, setError] = React.useState<string | null>(null)
  const [userCount, setUserCount] = React.useState<number | null>(null)

  const testConnection = async () => {
    setConnectionStatus('testing')
    setError(null)
    
    try {
      if (!supabase) {
        throw new Error('Supabase client not initialized')
      }

      // Test basic connection
      const { data, error } = await supabase
        .from('profiles')
        .select('count', { count: 'exact', head: true })
      
      if (error) {
        throw error
      }
      
      setUserCount(data?.length || 0)
      setConnectionStatus('connected')
    } catch (err: any) {
      setError(err.message || 'Unknown error')
      setConnectionStatus('error')
    }
  }

  React.useEffect(() => {
    testConnection()
  }, [])

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'success'
      case 'error': return 'destructive'
      default: return 'secondary'
    }
  }

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Connected'
      case 'error': return 'Error'
      default: return 'Testing...'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle>Supabase Connection Test</CardTitle>
          <CardDescription>
            Testing the connection to your Supabase database
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <Badge variant={getStatusColor() as any} className="text-lg px-4 py-2">
              {getStatusText()}
            </Badge>
          </div>

          {connectionStatus === 'connected' && (
            <div className="text-center space-y-2">
              <p className="text-green-600 font-medium">✅ Successfully connected to Supabase!</p>
              <p className="text-sm text-gray-600">
                Database is accessible and ready for use.
              </p>
            </div>
          )}

          {connectionStatus === 'error' && (
            <div className="text-center space-y-2">
              <p className="text-red-600 font-medium">❌ Connection failed</p>
              <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
                {error}
              </p>
            </div>
          )}

          {connectionStatus === 'testing' && (
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="text-sm text-gray-600 mt-2">Testing connection...</p>
            </div>
          )}

          <div className="space-y-2">
            <h4 className="font-medium">Environment Variables:</h4>
            <div className="text-sm space-y-1">
              <div className="flex justify-between">
                <span>Supabase URL:</span>
                <Badge variant="outline">
                  {process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Anon Key:</span>
                <Badge variant="outline">
                  {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing'}
                </Badge>
              </div>
            </div>
          </div>

          <Button 
            onClick={testConnection} 
            className="w-full"
            disabled={connectionStatus === 'testing'}
          >
            {connectionStatus === 'testing' ? 'Testing...' : 'Test Again'}
          </Button>

          <div className="text-center">
            <a 
              href="/" 
              className="text-blue-600 hover:text-blue-500 text-sm"
            >
              ← Back to Home
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
