// Simple test page to isolate issues
export default function TestSimplePage() {
  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Simple Test Page
        </h1>
        <p className="text-gray-600 mb-8">
          If you can see this, the basic Next.js setup is working.
        </p>
        <div className="space-y-4">
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="font-semibold text-green-800">✅ Next.js Working</h3>
            <p className="text-green-700 text-sm">Page rendering successfully</p>
          </div>
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-800">✅ Tailwind CSS Working</h3>
            <p className="text-blue-700 text-sm">Styles are being applied</p>
          </div>
          <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <h3 className="font-semibold text-purple-800">✅ TypeScript Working</h3>
            <p className="text-purple-700 text-sm">No compilation errors</p>
          </div>
        </div>
        <div className="mt-8">
          <a 
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            ← Back to Home
          </a>
        </div>
      </div>
    </div>
  );
}
