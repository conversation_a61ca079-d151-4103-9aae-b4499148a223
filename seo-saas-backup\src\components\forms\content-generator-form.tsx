// Professional Content Generator Form
// Enterprise-grade form for SEO content generation

'use client'

import * as React from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, type SelectOption } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
// Badge import removed as it's not used in this component
import { cn } from "@/lib/utils"

export interface ContentGenerationRequest {
  keyword: string
  location: string
  industry: string
  contentType: 'service' | 'blog' | 'product' | 'landing'
  tone: 'professional' | 'conversational' | 'authoritative' | 'friendly'
  intent: 'informational' | 'commercial' | 'transactional' | 'navigational'
  targetWordCount: number
  includeImages: boolean
  includeSchema: boolean
  competitorUrls?: string[]
}

interface ContentGeneratorFormProps {
  onSubmit: (data: ContentGenerationRequest) => void
  loading?: boolean
  className?: string
}

const industries: SelectOption[] = [
  { value: 'technology', label: 'Technology' },
  { value: 'healthcare', label: 'Healthcare' },
  { value: 'finance', label: 'Finance' },
  { value: 'education', label: 'Education' },
  { value: 'retail', label: 'Retail' },
  { value: 'real-estate', label: 'Real Estate' },
  { value: 'automotive', label: 'Automotive' },
  { value: 'travel', label: 'Travel & Tourism' },
  { value: 'legal', label: 'Legal Services' },
  { value: 'marketing', label: 'Marketing & Advertising' },
]

const countries: SelectOption[] = [
  { value: 'US', label: 'United States' },
  { value: 'UK', label: 'United Kingdom' },
  { value: 'CA', label: 'Canada' },
  { value: 'AU', label: 'Australia' },
  { value: 'DE', label: 'Germany' },
  { value: 'FR', label: 'France' },
  { value: 'ES', label: 'Spain' },
  { value: 'IT', label: 'Italy' },
  { value: 'NL', label: 'Netherlands' },
  { value: 'SE', label: 'Sweden' },
]

const contentTypes: SelectOption[] = [
  { value: 'service', label: 'Service Page' },
  { value: 'blog', label: 'Blog Article' },
  { value: 'product', label: 'Product Page' },
  { value: 'landing', label: 'Landing Page' },
]

const tones: SelectOption[] = [
  { value: 'professional', label: 'Professional' },
  { value: 'conversational', label: 'Conversational' },
  { value: 'authoritative', label: 'Authoritative' },
  { value: 'friendly', label: 'Friendly' },
]

const intents: SelectOption[] = [
  { value: 'informational', label: 'Informational' },
  { value: 'commercial', label: 'Commercial' },
  { value: 'transactional', label: 'Transactional' },
  { value: 'navigational', label: 'Navigational' },
]

const wordCountOptions: SelectOption[] = [
  { value: '500', label: '500 words' },
  { value: '1000', label: '1,000 words' },
  { value: '1500', label: '1,500 words' },
  { value: '2000', label: '2,000 words' },
  { value: '2500', label: '2,500 words' },
  { value: '3000', label: '3,000 words' },
]

export function ContentGeneratorForm({ onSubmit, loading = false, className }: ContentGeneratorFormProps) {
  const [formData, setFormData] = React.useState<ContentGenerationRequest>({
    keyword: '',
    location: 'US',
    industry: 'technology',
    contentType: 'service',
    tone: 'professional',
    intent: 'commercial',
    targetWordCount: 1000,
    includeImages: false,
    includeSchema: false,
    competitorUrls: []
  })

  const [errors, setErrors] = React.useState<Record<string, string>>({})
  const [competitorUrl, setCompetitorUrl] = React.useState('')

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.keyword.trim()) {
      newErrors.keyword = 'Keyword is required'
    } else if (formData.keyword.length < 2) {
      newErrors.keyword = 'Keyword must be at least 2 characters'
    } else if (formData.keyword.length > 100) {
      newErrors.keyword = 'Keyword must be less than 100 characters'
    }

    if (!formData.location) {
      newErrors.location = 'Location is required'
    }

    if (!formData.industry) {
      newErrors.industry = 'Industry is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      onSubmit(formData)
    }
  }

  const handleInputChange = (field: keyof ContentGenerationRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const addCompetitorUrl = () => {
    if (competitorUrl.trim() && formData.competitorUrls!.length < 5) {
      try {
        new URL(competitorUrl) // Validate URL
        setFormData(prev => ({
          ...prev,
          competitorUrls: [...(prev.competitorUrls || []), competitorUrl.trim()]
        }))
        setCompetitorUrl('')
      } catch {
        setErrors(prev => ({ ...prev, competitorUrl: 'Please enter a valid URL' }))
      }
    }
  }

  const removeCompetitorUrl = (index: number) => {
    setFormData(prev => ({
      ...prev,
      competitorUrls: prev.competitorUrls?.filter((_, i) => i !== index) || []
    }))
  }

  return (
    <Card className={cn("w-full max-w-4xl mx-auto", className)}>
      <CardHeader>
        <CardTitle>Generate SEO-Optimized Content</CardTitle>
        <CardDescription>
          Create high-quality, SEO-optimized content with our advanced AI engine
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Primary Keyword */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Primary Keyword"
              placeholder="e.g., SEO services"
              value={formData.keyword}
              onChange={(e) => handleInputChange('keyword', e.target.value)}
              error={errors.keyword}
              required
              helperText="The main keyword you want to rank for"
            />

            <Select
              label="Target Location"
              options={countries}
              value={formData.location}
              onChange={(value) => handleInputChange('location', value)}
              error={errors.location}
              required
            />
          </div>

          {/* Industry and Content Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Select
              label="Industry"
              options={industries}
              value={formData.industry}
              onChange={(value) => handleInputChange('industry', value)}
              error={errors.industry}
              required
            />

            <Select
              label="Content Type"
              options={contentTypes}
              value={formData.contentType}
              onChange={(value) => handleInputChange('contentType', value)}
              required
            />
          </div>

          {/* Tone and Intent */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Select
              label="Tone"
              options={tones}
              value={formData.tone}
              onChange={(value) => handleInputChange('tone', value)}
              required
            />

            <Select
              label="Search Intent"
              options={intents}
              value={formData.intent}
              onChange={(value) => handleInputChange('intent', value)}
              required
            />
          </div>

          {/* Word Count */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Select
              label="Target Word Count"
              options={wordCountOptions}
              value={formData.targetWordCount.toString()}
              onChange={(value) => handleInputChange('targetWordCount', parseInt(value))}
              required
            />

            <div className="space-y-4">
              <label className="text-sm font-medium">Additional Options</label>
              <div className="space-y-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.includeImages}
                    onChange={(e) => handleInputChange('includeImages', e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Include image suggestions</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.includeSchema}
                    onChange={(e) => handleInputChange('includeSchema', e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Include schema markup</span>
                </label>
              </div>
            </div>
          </div>

          {/* Competitor URLs */}
          <div className="space-y-4">
            <label className="text-sm font-medium">
              Competitor URLs (Optional)
              <span className="text-muted-foreground ml-1">- Up to 5 URLs for analysis</span>
            </label>
            
            <div className="flex space-x-2">
              <Input
                placeholder="https://competitor.com/page"
                value={competitorUrl}
                onChange={(e) => setCompetitorUrl(e.target.value)}
                error={errors.competitorUrl}
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                onClick={addCompetitorUrl}
                disabled={!competitorUrl.trim() || (formData.competitorUrls?.length || 0) >= 5}
              >
                Add
              </Button>
            </div>

            {formData.competitorUrls && formData.competitorUrls.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Competitor URLs to analyze:</p>
                <div className="space-y-2">
                  {formData.competitorUrls.map((url, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <span className="text-sm truncate flex-1">{url}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCompetitorUrl(index)}
                        className="ml-2"
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setFormData({
                  keyword: '',
                  location: 'US',
                  industry: 'technology',
                  contentType: 'service',
                  tone: 'professional',
                  intent: 'commercial',
                  targetWordCount: 1000,
                  includeImages: false,
                  includeSchema: false,
                  competitorUrls: []
                })
                setErrors({})
              }}
            >
              Reset
            </Button>
            <Button
              type="submit"
              loading={loading}
              disabled={loading}
              className="min-w-[120px]"
            >
              {loading ? 'Generating...' : 'Generate Content'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
