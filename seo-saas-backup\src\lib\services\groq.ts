// Groq API Integration for Content Generation
// Enterprise-grade content generation with multiple models and optimization

import Groq from 'groq-sdk';
import { config } from '../config';
import { withErrorHandling } from '../error-handler';
import { logApiUsage } from '../supabase';

interface GroqConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
}

interface ContentGenerationRequest {
  keyword: string;
  location: string;
  industry: string;
  contentType: 'service' | 'blog' | 'product' | 'landing' | 'category' | 'faq';
  tone: 'professional' | 'conversational' | 'authoritative' | 'friendly' | 'technical' | 'casual';
  intent: 'informational' | 'commercial' | 'transactional' | 'navigational';
  targetWordCount: number;
  includeImages: boolean;
  includeSchema: boolean;
  includeFaq: boolean;
  competitorData?: any;
  templateStructure?: any;
  brandVoice?: string;
  targetAudience?: string;
}

interface ContentGenerationResult {
  title: string;
  metaDescription: string;
  content: string;
  outline: ContentOutline;
  seoMetrics: SeoMetrics;
  suggestions: string[];
  schemaMarkup?: string;
  imagePrompts?: ImagePrompt[];
  faqSection?: FaqItem[];
  internalLinks?: LinkSuggestion[];
  externalLinks?: LinkSuggestion[];
}

interface ContentOutline {
  title: string;
  metaDescription: string;
  headings: HeadingItem[];
  keywordDistribution: KeywordDistribution[];
  estimatedReadingTime: number;
}

interface HeadingItem {
  level: number;
  text: string;
  keywords: string[];
  wordCount: number;
}

interface SeoMetrics {
  keywordDensity: number;
  headingCount: { [key: string]: number };
  wordCount: number;
  readabilityScore: number;
  seoScore: number;
  keywordVariations: string[];
  lsiKeywords: string[];
  entities: string[];
}

interface KeywordDistribution {
  keyword: string;
  count: number;
  density: number;
  positions: string[];
}

interface ImagePrompt {
  alt: string;
  caption: string;
  placement: string;
  description: string;
}

interface FaqItem {
  question: string;
  answer: string;
  keywords: string[];
}

interface LinkSuggestion {
  anchor: string;
  url: string;
  type: 'internal' | 'external';
  relevanceScore: number;
}

export class GroqService {
  private client: Groq;
  private defaultConfig: GroqConfig;

  // Available models with their specifications
  private models = {
    'llama3-8b-8192': {
      name: 'Llama 3 8B',
      maxTokens: 8192,
      bestFor: ['general', 'fast'],
      costPer1kTokens: 0.05,
    },
    'llama3-70b-8192': {
      name: 'Llama 3 70B',
      maxTokens: 8192,
      bestFor: ['complex', 'detailed'],
      costPer1kTokens: 0.59,
    },
    'mixtral-8x7b-32768': {
      name: 'Mixtral 8x7B',
      maxTokens: 32768,
      bestFor: ['long-form', 'technical'],
      costPer1kTokens: 0.24,
    },
    'gemma-7b-it': {
      name: 'Gemma 7B',
      maxTokens: 8192,
      bestFor: ['efficient', 'instruction'],
      costPer1kTokens: 0.07,
    },
  };

  constructor() {
    this.client = new Groq({
      apiKey: config.apis.groq.apiKey,
    });

    this.defaultConfig = {
      model: 'llama3-8b-8192',
      temperature: 0.7,
      maxTokens: 4000,
      topP: 0.9,
      frequencyPenalty: 0.0,
      presencePenalty: 0.0,
    };
  }

  // Main content generation method
  async generateContent(
    request: ContentGenerationRequest,
    userId?: string
  ): Promise<ContentGenerationResult> {
    return withErrorHandling(
      'groq',
      async () => {
        const startTime = Date.now();
        
        // Select optimal model based on content type and word count
        const modelConfig = this.selectOptimalModel(request);
        
        // Generate system prompt
        const systemPrompt = this.buildSystemPrompt(request);
        
        // Generate user prompt
        const userPrompt = this.buildUserPrompt(request);
        
        console.log('Generating content with Groq...', {
          model: modelConfig.model,
          wordCount: request.targetWordCount,
          contentType: request.contentType,
        });

        // Make API call
        const completion = await this.client.chat.completions.create({
          model: modelConfig.model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt },
          ],
          temperature: modelConfig.temperature,
          max_tokens: modelConfig.maxTokens,
          top_p: modelConfig.topP,
          frequency_penalty: modelConfig.frequencyPenalty,
          presence_penalty: modelConfig.presencePenalty,
          stream: false,
        });

        const responseTime = Date.now() - startTime;
        const tokensUsed = completion.usage?.total_tokens || 0;
        const cost = this.calculateCost(modelConfig.model, tokensUsed);

        // Log API usage
        if (userId) {
          await logApiUsage({
            user_id: userId,
            api_name: 'groq',
            endpoint: 'chat/completions',
            method: 'POST',
            tokens_used: tokensUsed,
            cost,
            response_time_ms: responseTime,
            status_code: 200,
          });
        }

        // Parse and structure the response
        const rawContent = completion.choices[0]?.message?.content || '';
        const structuredResult = await this.parseAndStructureContent(
          rawContent,
          request
        );

        console.log('Content generation completed', {
          responseTime,
          tokensUsed,
          cost,
          wordCount: structuredResult.seoMetrics.wordCount,
        });

        return structuredResult;
      },
      {
        method: 'POST',
        endpoint: 'chat/completions',
        model: this.selectOptimalModel(request).model,
        ...request,
      },
      userId
    );
  }

  // Select optimal model based on requirements
  private selectOptimalModel(request: ContentGenerationRequest): GroqConfig {
    let selectedModel = 'llama3-8b-8192'; // Default

    // For long-form content, use Mixtral
    if (request.targetWordCount > 2000) {
      selectedModel = 'mixtral-8x7b-32768';
    }
    // For complex technical content, use Llama 3 70B
    else if (request.tone === 'technical' || request.contentType === 'blog') {
      selectedModel = 'llama3-70b-8192';
    }
    // For simple content, use the fastest model
    else if (request.targetWordCount < 500) {
      selectedModel = 'gemma-7b-it';
    }

    const modelInfo = this.models[selectedModel as keyof typeof this.models];
    
    return {
      model: selectedModel,
      temperature: this.getOptimalTemperature(request),
      maxTokens: Math.min(
        Math.floor(request.targetWordCount * 1.5), // Estimate tokens from words
        modelInfo.maxTokens - 1000 // Leave room for prompt
      ),
      topP: 0.9,
      frequencyPenalty: request.contentType === 'blog' ? 0.1 : 0.0,
      presencePenalty: 0.0,
    };
  }

  // Get optimal temperature based on content type
  private getOptimalTemperature(request: ContentGenerationRequest): number {
    switch (request.contentType) {
      case 'service':
      case 'product':
        return 0.3; // More factual, less creative
      case 'blog':
        return 0.7; // Balanced creativity and factuality
      case 'landing':
        return 0.5; // Persuasive but factual
      case 'faq':
        return 0.2; // Very factual
      default:
        return 0.7;
    }
  }

  // Build comprehensive system prompt
  private buildSystemPrompt(request: ContentGenerationRequest): string {
    const forbiddenWords = [
      'meticulous', 'navigating', 'complexities', 'realm', 'bespoke', 'tailored',
      'towards', 'underpins', 'ever-changing', 'ever-evolving', 'the world of',
      'not only', 'seeking more than just', 'designed to enhance', 'it\'s not merely',
      'our suite', 'it is advisable', 'daunting', 'in the heart of', 'when it comes to',
      'in the realm of', 'amongst', 'unlock the secrets', 'unveil the secrets', 'robust'
    ];

    return `You are an expert SEO content writer specializing in ${request.industry} industry content. Your task is to create ${request.contentType} content that ranks highly in search engines.

CRITICAL REQUIREMENTS:
1. Write in ${request.tone} tone for ${request.intent} intent
2. Target exactly ${request.targetWordCount} words
3. Use clear subject-verb-object sentence structure
4. NEVER use these forbidden words: ${forbiddenWords.join(', ')}
5. Focus on E-E-A-T: Expertise, Experience, Authoritativeness, Trustworthiness
6. Include natural keyword variations and LSI keywords
7. Write for humans first, search engines second

CONTENT STRUCTURE:
- Strong, compelling title with primary keyword
- Meta description (150-160 characters)
- Clear heading hierarchy (H1, H2, H3, H4)
- Natural keyword distribution throughout content
- Actionable insights and practical information
- Strong conclusion with clear next steps

SEO OPTIMIZATION:
- Primary keyword: "${request.keyword}"
- Target location: ${request.location}
- Industry context: ${request.industry}
- Content intent: ${request.intent}
${request.brandVoice ? `- Brand voice: ${request.brandVoice}` : ''}
${request.targetAudience ? `- Target audience: ${request.targetAudience}` : ''}

QUALITY STANDARDS:
- Write engaging, valuable content that users want to read
- Use data, statistics, and examples when relevant
- Include practical tips and actionable advice
- Maintain readability with short paragraphs and bullet points
- Ensure content flows naturally and logically

Return the content in this exact JSON format:
{
  "title": "SEO-optimized title with primary keyword",
  "metaDescription": "Compelling 150-160 character meta description",
  "content": "Full article content with proper HTML formatting",
  "outline": {
    "headings": [
      {"level": 1, "text": "H1 heading", "keywords": ["keyword1", "keyword2"]},
      {"level": 2, "text": "H2 heading", "keywords": ["keyword1"]}
    ]
  }
}`;
  }

  // Build detailed user prompt
  private buildUserPrompt(request: ContentGenerationRequest): string {
    let prompt = `Create ${request.contentType} content about "${request.keyword}" targeting ${request.location}.

CONTENT REQUIREMENTS:
- Industry: ${request.industry}
- Tone: ${request.tone}
- Intent: ${request.intent}
- Word count: ${request.targetWordCount} words
- Include practical examples and actionable advice
- Write for ${request.targetAudience || 'general audience'}`;

    if (request.competitorData) {
      prompt += `\n\nCOMPETITOR INSIGHTS:
Based on competitor analysis, include these elements:
- Average competitor word count: ${request.competitorData.averageWordCount || 'Not specified'}
- Common topics covered: ${request.competitorData.commonTopics?.join(', ') || 'Not specified'}
- Content gaps to address: ${request.competitorData.contentGaps?.join(', ') || 'Not specified'}`;
    }

    if (request.templateStructure) {
      prompt += `\n\nCONTENT STRUCTURE:
Follow this template structure:
${JSON.stringify(request.templateStructure, null, 2)}`;
    }

    if (request.includeSchema) {
      prompt += `\n\nSCHEMA MARKUP:
Include appropriate schema markup suggestions for this content type.`;
    }

    if (request.includeFaq) {
      prompt += `\n\nFAQ SECTION:
Include a comprehensive FAQ section with 5-8 relevant questions and detailed answers.`;
    }

    if (request.includeImages) {
      prompt += `\n\nIMAGE SUGGESTIONS:
Provide specific image prompts and alt text suggestions for optimal visual content.`;
    }

    prompt += `\n\nEnsure the content is original, engaging, and provides genuine value to readers searching for "${request.keyword}" in ${request.location}.`;

    return prompt;
  }

  // Parse and structure the AI response
  private async parseAndStructureContent(
    rawContent: string,
    request: ContentGenerationRequest
  ): Promise<ContentGenerationResult> {
    try {
      // Try to parse as JSON first
      const parsedContent = JSON.parse(rawContent);
      
      // Analyze the content for SEO metrics
      const seoMetrics = await this.analyzeSeoMetrics(
        parsedContent.content,
        request.keyword
      );

      // Generate additional elements if requested
      const result: ContentGenerationResult = {
        title: parsedContent.title,
        metaDescription: parsedContent.metaDescription,
        content: parsedContent.content,
        outline: parsedContent.outline || await this.generateOutline(parsedContent.content),
        seoMetrics,
        suggestions: await this.generateSuggestions(parsedContent.content, request),
      };

      // Add optional elements
      if (request.includeSchema) {
        result.schemaMarkup = await this.generateSchemaMarkup(request);
      }

      if (request.includeImages) {
        result.imagePrompts = await this.generateImagePrompts(parsedContent.content, request);
      }

      if (request.includeFaq) {
        result.faqSection = await this.extractFaqSection(parsedContent.content);
      }

      // Generate link suggestions
      result.internalLinks = await this.generateInternalLinks(parsedContent.content, request);
      result.externalLinks = await this.generateExternalLinks(parsedContent.content, request);

      return result;
    } catch (error) {
      console.error('Failed to parse structured content, falling back to text parsing:', error);
      
      // Fallback: parse as plain text
      return this.parseUnstructuredContent(rawContent, request);
    }
  }

  // Fallback method for unstructured content
  private async parseUnstructuredContent(
    content: string,
    request: ContentGenerationRequest
  ): Promise<ContentGenerationResult> {
    // Extract title (first line or H1)
    const lines = content.split('\n').filter(line => line.trim());
    const title = lines[0]?.replace(/^#\s*/, '') || `${request.keyword} - ${request.contentType}`;
    
    // Generate meta description from first paragraph
    const firstParagraph = lines.find(line => line.length > 50 && !line.startsWith('#'));
    const metaDescription = firstParagraph 
      ? firstParagraph.substring(0, 160).trim() + '...'
      : `Learn about ${request.keyword} in ${request.location}. Expert insights and practical advice.`;

    // Analyze SEO metrics
    const seoMetrics = await this.analyzeSeoMetrics(content, request.keyword);

    return {
      title,
      metaDescription,
      content,
      outline: await this.generateOutline(content),
      seoMetrics,
      suggestions: await this.generateSuggestions(content, request),
    };
  }

  // Analyze SEO metrics of generated content
  private async analyzeSeoMetrics(content: string, primaryKeyword: string): Promise<SeoMetrics> {
    const words = content.toLowerCase().split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    
    // Calculate keyword density
    const keywordWords = primaryKeyword.toLowerCase().split(/\s+/);
    const keywordCount = this.countKeywordOccurrences(content, primaryKeyword);
    const keywordDensity = (keywordCount / wordCount) * 100;
    
    // Count headings
    const headingCount = this.countHeadings(content);
    
    // Extract LSI keywords and entities
    const lsiKeywords = this.extractLsiKeywords(content, primaryKeyword);
    const entities = this.extractEntities(content);
    
    // Calculate readability score (simplified Flesch Reading Ease)
    const readabilityScore = this.calculateReadabilityScore(content);
    
    // Calculate overall SEO score
    const seoScore = this.calculateSeoScore({
      keywordDensity,
      headingCount,
      wordCount,
      readabilityScore,
      keywordCount,
    });

    return {
      keywordDensity: Math.round(keywordDensity * 100) / 100,
      headingCount,
      wordCount,
      readabilityScore,
      seoScore,
      keywordVariations: this.extractKeywordVariations(content, primaryKeyword),
      lsiKeywords,
      entities,
    };
  }

  // Helper methods for content analysis
  private countKeywordOccurrences(content: string, keyword: string): number {
    const regex = new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
    return (content.match(regex) || []).length;
  }

  private countHeadings(content: string): { [key: string]: number } {
    const headingCounts = { h1: 0, h2: 0, h3: 0, h4: 0, h5: 0, h6: 0 };
    
    for (let i = 1; i <= 6; i++) {
      const regex = new RegExp(`<h${i}[^>]*>.*?</h${i}>`, 'gi');
      headingCounts[`h${i}` as keyof typeof headingCounts] = (content.match(regex) || []).length;
    }
    
    return headingCounts;
  }

  private extractLsiKeywords(content: string, primaryKeyword: string): string[] {
    // Simplified LSI keyword extraction
    // In production, this would use more sophisticated NLP
    const words = content.toLowerCase().split(/\s+/);
    const stopWords = new Set(['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
    
    const wordFreq = new Map<string, number>();
    words.forEach(word => {
      word = word.replace(/[^\w]/g, '');
      if (word.length > 3 && !stopWords.has(word)) {
        wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
      }
    });
    
    return Array.from(wordFreq.entries())
      .filter(([word, freq]) => freq > 2 && !primaryKeyword.toLowerCase().includes(word))
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);
  }

  private extractEntities(content: string): string[] {
    // Simplified entity extraction
    // Look for capitalized words/phrases that might be entities
    const entityRegex = /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g;
    const entities = content.match(entityRegex) || [];
    
    // Filter and deduplicate
    const uniqueEntities = Array.from(new Set(entities))
      .filter(entity => entity.length > 3)
      .slice(0, 15);
    
    return uniqueEntities;
  }

  private extractKeywordVariations(content: string, primaryKeyword: string): string[] {
    const variations = new Set<string>();
    const words = primaryKeyword.toLowerCase().split(/\s+/);
    
    // Look for variations of each keyword word
    words.forEach(word => {
      const regex = new RegExp(`\\b${word}\\w*\\b`, 'gi');
      const matches = content.match(regex) || [];
      matches.forEach(match => variations.add(match.toLowerCase()));
    });
    
    return Array.from(variations).slice(0, 10);
  }

  private calculateReadabilityScore(content: string): number {
    // Simplified Flesch Reading Ease calculation
    const sentences = content.split(/[.!?]+/).length;
    const words = content.split(/\s+/).length;
    const syllables = this.countSyllables(content);
    
    const score = 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words));
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  private countSyllables(text: string): number {
    const words = text.toLowerCase().match(/\b\w+\b/g) || [];
    return words.reduce((total, word) => {
      const syllableCount = word.match(/[aeiouy]+/g)?.length || 1;
      return total + syllableCount;
    }, 0);
  }

  private calculateSeoScore(metrics: {
    keywordDensity: number;
    headingCount: { [key: string]: number };
    wordCount: number;
    readabilityScore: number;
    keywordCount: number;
  }): number {
    let score = 0;
    
    // Keyword density (optimal: 1-3%)
    if (metrics.keywordDensity >= 1 && metrics.keywordDensity <= 3) {
      score += 25;
    } else if (metrics.keywordDensity > 0.5 && metrics.keywordDensity < 5) {
      score += 15;
    }
    
    // Heading structure
    if (metrics.headingCount.h1 === 1) score += 10;
    if (metrics.headingCount.h2 >= 2) score += 15;
    if (metrics.headingCount.h3 >= 1) score += 10;
    
    // Word count
    if (metrics.wordCount >= 500) score += 15;
    if (metrics.wordCount >= 1000) score += 10;
    
    // Readability
    if (metrics.readabilityScore >= 60) score += 15;
    
    return Math.min(100, score);
  }

  // Generate content outline
  private async generateOutline(content: string): Promise<ContentOutline> {
    // Extract headings and create outline
    const headingRegex = /<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi;
    const headings: HeadingItem[] = [];
    let match;
    
    while ((match = headingRegex.exec(content)) !== null) {
      headings.push({
        level: parseInt(match[1]),
        text: match[2].replace(/<[^>]*>/g, ''),
        keywords: [],
        wordCount: 0,
      });
    }
    
    return {
      title: headings.find(h => h.level === 1)?.text || 'Untitled',
      metaDescription: '',
      headings,
      keywordDistribution: [],
      estimatedReadingTime: Math.ceil(content.split(/\s+/).length / 200),
    };
  }

  // Generate improvement suggestions
  private async generateSuggestions(content: string, request: ContentGenerationRequest): Promise<string[]> {
    const suggestions: string[] = [];
    
    const wordCount = content.split(/\s+/).length;
    if (wordCount < request.targetWordCount * 0.9) {
      suggestions.push(`Content is ${request.targetWordCount - wordCount} words short of target`);
    }
    
    const keywordCount = this.countKeywordOccurrences(content, request.keyword);
    if (keywordCount === 0) {
      suggestions.push('Primary keyword not found in content');
    } else if (keywordCount === 1) {
      suggestions.push('Consider adding the primary keyword 1-2 more times');
    }
    
    const headingCount = this.countHeadings(content);
    if (headingCount.h1 === 0) {
      suggestions.push('Add an H1 heading with the primary keyword');
    }
    if (headingCount.h2 < 2) {
      suggestions.push('Add more H2 headings to improve content structure');
    }
    
    return suggestions;
  }

  // Generate schema markup
  private async generateSchemaMarkup(request: ContentGenerationRequest): Promise<string> {
    // Generate appropriate schema based on content type
    const baseSchema = {
      '@context': 'https://schema.org',
      '@type': this.getSchemaType(request.contentType),
      'name': request.keyword,
      'description': `${request.keyword} in ${request.location}`,
    };
    
    return JSON.stringify(baseSchema, null, 2);
  }

  private getSchemaType(contentType: string): string {
    switch (contentType) {
      case 'service': return 'Service';
      case 'product': return 'Product';
      case 'blog': return 'Article';
      case 'faq': return 'FAQPage';
      default: return 'WebPage';
    }
  }

  // Generate image prompts
  private async generateImagePrompts(content: string, request: ContentGenerationRequest): Promise<ImagePrompt[]> {
    // This would be more sophisticated in production
    return [
      {
        alt: `${request.keyword} in ${request.location}`,
        caption: `Professional ${request.keyword} services`,
        placement: 'header',
        description: `High-quality image showing ${request.keyword} related to ${request.industry}`,
      },
    ];
  }

  // Extract FAQ section
  private async extractFaqSection(content: string): Promise<FaqItem[]> {
    // Look for Q&A patterns in the content
    const faqRegex = /(?:Q:|Question:|FAQ:)\s*(.*?)\n(?:A:|Answer:)\s*(.*?)(?=\n|$)/gi;
    const faqs: FaqItem[] = [];
    let match;
    
    while ((match = faqRegex.exec(content)) !== null && faqs.length < 8) {
      faqs.push({
        question: match[1].trim(),
        answer: match[2].trim(),
        keywords: [],
      });
    }
    
    return faqs;
  }

  // Generate internal link suggestions
  private async generateInternalLinks(content: string, request: ContentGenerationRequest): Promise<LinkSuggestion[]> {
    // This would integrate with the site's content database in production
    return [
      {
        anchor: `${request.keyword} services`,
        url: `/services/${request.keyword.toLowerCase().replace(/\s+/g, '-')}`,
        type: 'internal',
        relevanceScore: 0.9,
      },
    ];
  }

  // Generate external link suggestions
  private async generateExternalLinks(content: string, request: ContentGenerationRequest): Promise<LinkSuggestion[]> {
    // Suggest authoritative external sources
    return [
      {
        anchor: 'Industry Statistics',
        url: 'https://example.com/industry-stats',
        type: 'external',
        relevanceScore: 0.8,
      },
    ];
  }

  // Calculate API costs
  private calculateCost(model: string, tokens: number): number {
    const modelInfo = this.models[model as keyof typeof this.models];
    if (!modelInfo) return 0;
    
    return (tokens / 1000) * modelInfo.costPer1kTokens;
  }

  // Get available models
  getAvailableModels() {
    return this.models;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; model: string; latency: number }> {
    const startTime = Date.now();
    
    try {
      const response = await this.client.chat.completions.create({
        model: 'llama3-8b-8192',
        messages: [{ role: 'user', content: 'Hi' }],
        max_tokens: 10,
      });
      
      const latency = Date.now() - startTime;
      
      return {
        status: response ? 'healthy' : 'unhealthy',
        model: 'llama3-8b-8192',
        latency,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        model: 'llama3-8b-8192',
        latency: Date.now() - startTime,
      };
    }
  }
}

// Export singleton instance
export const groqService = new GroqService();
export default GroqService;