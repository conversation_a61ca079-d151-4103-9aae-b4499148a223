const cheerio = require('cheerio');
const natural = require('natural');
const axios = require('axios');

class HeadingStructureAnalyzer {
  constructor() {
    this.stemmer = natural.PorterStemmer;
    this.tokenizer = new natural.WordTokenizer();
    this.serperApiKey = process.env.SERPER_API_KEY;
  }

  // Main method to analyze competitor heading structures with surgical precision
  async analyzeCompetitorHeadingStructures(competitorMetrics, keyword) {
    try {
      console.log(`Analyzing heading structures for keyword: ${keyword}`);
      
      if (!competitorMetrics || competitorMetrics.length === 0) {
        throw new Error('No competitor metrics provided for heading analysis');
      }

      // Extract heading data from all competitors
      const headingData = this.extractHeadingData(competitorMetrics, keyword);
      
      // Calculate surgical benchmarks for top 5 competitors
      const headingBenchmarks = this.calculateHeadingBenchmarks(headingData.slice(0, 5), keyword);
      
      // Extract LSI keywords and entities from competitors
      const competitorLSIAndEntities = this.extractCompetitorLSIAndEntities(competitorMetrics.slice(0, 5), keyword);
      
      // Research internet-wide LSI keywords and entities
      const internetLSIAndEntities = await this.researchInternetLSIAndEntities(keyword);
      
      // Combine and prioritize LSI keywords and entities
      const combinedSemanticData = this.combineSemanticData(competitorLSIAndEntities, internetLSIAndEntities, keyword);
      
      // Analyze keyword optimization patterns
      const keywordOptimization = this.analyzeKeywordOptimization(headingData, keyword);
      
      // Generate heading structure recommendations with LSI/entities
      const recommendations = this.generateHeadingRecommendations(headingBenchmarks, keywordOptimization, keyword, combinedSemanticData);
      
      // Create optimal heading template with semantic integration
      const optimalTemplate = this.createOptimalHeadingTemplate(headingBenchmarks, keywordOptimization, keyword, combinedSemanticData);

      return {
        success: true,
        keyword,
        competitorCount: competitorMetrics.length,
        top5Analysis: {
          headingBenchmarks,
          averageHeadings: this.calculateTop5HeadingAverages(headingData.slice(0, 5))
        },
        semanticData: combinedSemanticData,
        keywordOptimization,
        recommendations,
        optimalTemplate,
        generatedAt: new Date().toISOString()
      };

    } catch (error) {
      console.error('Heading structure analysis error:', error);
      return {
        success: false,
        error: error.message,
        keyword,
        headingBenchmarks: null
      };
    }
  }

  // Extract detailed heading data from competitor metrics
  extractHeadingData(competitorMetrics, keyword) {
    const headingData = [];
    
    competitorMetrics.forEach((competitor, index) => {
      try {
        if (!competitor.headingStructure) {
          console.warn(`No heading structure data for competitor ${index + 1}`);
          return;
        }

        const data = {
          position: competitor.position || index + 1,
          domain: competitor.domain,
          url: competitor.url,
          headings: competitor.headingStructure.headings,
          statistics: competitor.headingStructure.statistics,
          keywordAnalysis: this.analyzeHeadingKeywords(competitor.headingStructure.headings, keyword)
        };

        headingData.push(data);
      } catch (error) {
        console.error(`Error extracting heading data for competitor ${index + 1}:`, error);
      }
    });

    return headingData;
  }

  // Analyze keyword usage in headings
  analyzeHeadingKeywords(headings, keyword) {
    const keywordLower = keyword.toLowerCase();
    const keywordComponents = keywordLower.split(/\s+/);
    
    const analysis = {
      totalHeadings: 0,
      keywordOptimized: 0,
      byLevel: {
        h1: { total: 0, withKeyword: 0, withComponents: 0 },
        h2: { total: 0, withKeyword: 0, withComponents: 0 },
        h3: { total: 0, withKeyword: 0, withComponents: 0 },
        h4: { total: 0, withKeyword: 0, withComponents: 0 },
        h5: { total: 0, withKeyword: 0, withComponents: 0 },
        h6: { total: 0, withKeyword: 0, withComponents: 0 }
      },
      keywordPlacements: [],
      componentUsage: {}
    };

    // Initialize component usage tracking
    keywordComponents.forEach(component => {
      analysis.componentUsage[component] = {
        total: 0,
        byLevel: { h1: 0, h2: 0, h3: 0, h4: 0, h5: 0, h6: 0 }
      };
    });

    // Analyze each heading level
    Object.keys(headings).forEach(level => {
      const levelHeadings = headings[level] || [];
      analysis.byLevel[level].total = levelHeadings.length;
      analysis.totalHeadings += levelHeadings.length;

      levelHeadings.forEach((heading, index) => {
        const headingText = heading.text.toLowerCase();
        
        // Check for main keyword
        if (headingText.includes(keywordLower)) {
          analysis.byLevel[level].withKeyword++;
          analysis.keywordOptimized++;
          analysis.keywordPlacements.push({
            level,
            position: index + 1,
            text: heading.text,
            type: 'main_keyword'
          });
        }

        // Check for keyword components
        let hasComponents = false;
        keywordComponents.forEach(component => {
          if (headingText.includes(component)) {
            analysis.componentUsage[component].total++;
            analysis.componentUsage[component].byLevel[level]++;
            hasComponents = true;
          }
        });

        if (hasComponents) {
          analysis.byLevel[level].withComponents++;
        }
      });
    });

    // Calculate optimization ratios
    analysis.mainKeywordRatio = analysis.totalHeadings > 0 ? 
      (analysis.keywordOptimized / analysis.totalHeadings) * 100 : 0;

    return analysis;
  }

  // Calculate surgical heading benchmarks
  calculateHeadingBenchmarks(headingData, keyword) {
    if (headingData.length === 0) {
      return null;
    }

    const benchmarks = {
      distribution: {
        h1: { min: 0, max: 0, average: 0, median: 0 },
        h2: { min: 0, max: 0, average: 0, median: 0 },
        h3: { min: 0, max: 0, average: 0, median: 0 },
        h4: { min: 0, max: 0, average: 0, median: 0 },
        h5: { min: 0, max: 0, average: 0, median: 0 },
        h6: { min: 0, max: 0, average: 0, median: 0 }
      },
      totalHeadings: { min: 0, max: 0, average: 0, median: 0 },
      keywordOptimization: {
        averageRatio: 0,
        minRatio: 0,
        maxRatio: 0,
        targetRatio: 0
      },
      keywordPlacements: {
        h1Priority: 0,
        h2Priority: 0,
        h3Priority: 0,
        optimalDistribution: {}
      },
      componentOptimization: {}
    };

    // Calculate distribution statistics for each heading level
    Object.keys(benchmarks.distribution).forEach(level => {
      const counts = headingData.map(data => data.statistics[`${level}Count`] || 0);
      
      if (counts.length > 0) {
        benchmarks.distribution[level] = {
          min: Math.min(...counts),
          max: Math.max(...counts),
          average: parseFloat((counts.reduce((sum, count) => sum + count, 0) / counts.length).toFixed(1)),
          median: this.calculateMedian(counts)
        };
      }
    });

    // Calculate total headings statistics
    const totalCounts = headingData.map(data => data.statistics.total || 0);
    if (totalCounts.length > 0) {
      benchmarks.totalHeadings = {
        min: Math.min(...totalCounts),
        max: Math.max(...totalCounts),
        average: parseFloat((totalCounts.reduce((sum, count) => sum + count, 0) / totalCounts.length).toFixed(1)),
        median: this.calculateMedian(totalCounts)
      };
    }

    // Calculate keyword optimization benchmarks
    const keywordRatios = headingData.map(data => data.keywordAnalysis.mainKeywordRatio || 0);
    if (keywordRatios.length > 0) {
      const avgRatio = keywordRatios.reduce((sum, ratio) => sum + ratio, 0) / keywordRatios.length;
      benchmarks.keywordOptimization = {
        averageRatio: parseFloat(avgRatio.toFixed(2)),
        minRatio: parseFloat(Math.min(...keywordRatios).toFixed(2)),
        maxRatio: parseFloat(Math.max(...keywordRatios).toFixed(2)),
        targetRatio: parseFloat(avgRatio.toFixed(2)) // Use average as target
      };
    }

    // Analyze keyword placement priorities
    this.calculateKeywordPlacementPriorities(headingData, benchmarks);

    // Analyze component optimization patterns
    this.calculateComponentOptimization(headingData, keyword, benchmarks);

    return benchmarks;
  }

  // Calculate keyword placement priorities across heading levels
  calculateKeywordPlacementPriorities(headingData, benchmarks) {
    const placementCounts = {
      h1: 0, h2: 0, h3: 0, h4: 0, h5: 0, h6: 0
    };

    let totalPlacements = 0;

    headingData.forEach(data => {
      Object.keys(placementCounts).forEach(level => {
        const count = data.keywordAnalysis.byLevel[level]?.withKeyword || 0;
        placementCounts[level] += count;
        totalPlacements += count;
      });
    });

    // Calculate priorities as percentages
    if (totalPlacements > 0) {
      benchmarks.keywordPlacements.h1Priority = (placementCounts.h1 / totalPlacements) * 100;
      benchmarks.keywordPlacements.h2Priority = (placementCounts.h2 / totalPlacements) * 100;
      benchmarks.keywordPlacements.h3Priority = (placementCounts.h3 / totalPlacements) * 100;
    }

    // Generate optimal distribution recommendation
    benchmarks.keywordPlacements.optimalDistribution = {
      h1: Math.round(benchmarks.keywordPlacements.h1Priority),
      h2: Math.round(benchmarks.keywordPlacements.h2Priority),
      h3: Math.round(benchmarks.keywordPlacements.h3Priority),
      recommendation: this.generateDistributionRecommendation(placementCounts, totalPlacements)
    };
  }

  // Calculate component optimization patterns
  calculateComponentOptimization(headingData, keyword, benchmarks) {
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    
    keywordComponents.forEach(component => {
      const componentData = {
        totalUsage: 0,
        averageUsage: 0,
        byLevel: { h1: 0, h2: 0, h3: 0, h4: 0, h5: 0, h6: 0 },
        priority: 0
      };

      // Aggregate component usage across competitors
      headingData.forEach(data => {
        const usage = data.keywordAnalysis.componentUsage[component];
        if (usage) {
          componentData.totalUsage += usage.total;
          Object.keys(componentData.byLevel).forEach(level => {
            componentData.byLevel[level] += usage.byLevel[level] || 0;
          });
        }
      });

      // Calculate averages
      componentData.averageUsage = headingData.length > 0 ? 
        componentData.totalUsage / headingData.length : 0;

      // Calculate priority score (how often this component appears in headings)
      componentData.priority = componentData.totalUsage;

      benchmarks.componentOptimization[component] = componentData;
    });
  }

  // Analyze keyword optimization patterns
  analyzeKeywordOptimization(headingData, keyword) {
    const optimization = {
      patterns: {
        h1Optimization: { count: 0, percentage: 0 },
        h2Optimization: { count: 0, percentage: 0 },
        h3Optimization: { count: 0, percentage: 0 },
        overallOptimization: { average: 0, best: 0, worst: 0 }
      },
      bestPractices: [],
      commonMistakes: [],
      optimalStrategy: {}
    };

    // Analyze optimization patterns
    let totalH1WithKeyword = 0;
    let totalH2WithKeyword = 0;
    let totalH3WithKeyword = 0;
    const optimizationRatios = [];

    headingData.forEach(data => {
      totalH1WithKeyword += data.keywordAnalysis.byLevel.h1.withKeyword;
      totalH2WithKeyword += data.keywordAnalysis.byLevel.h2.withKeyword;
      totalH3WithKeyword += data.keywordAnalysis.byLevel.h3.withKeyword;
      optimizationRatios.push(data.keywordAnalysis.mainKeywordRatio);
    });

    // Calculate percentages
    optimization.patterns.h1Optimization = {
      count: totalH1WithKeyword,
      percentage: headingData.length > 0 ? (totalH1WithKeyword / headingData.length) * 100 : 0
    };

    optimization.patterns.h2Optimization = {
      count: totalH2WithKeyword,
      percentage: headingData.length > 0 ? (totalH2WithKeyword / headingData.length) * 100 : 0
    };

    optimization.patterns.h3Optimization = {
      count: totalH3WithKeyword,
      percentage: headingData.length > 0 ? (totalH3WithKeyword / headingData.length) * 100 : 0
    };

    // Overall optimization analysis
    if (optimizationRatios.length > 0) {
      optimization.patterns.overallOptimization = {
        average: parseFloat((optimizationRatios.reduce((sum, ratio) => sum + ratio, 0) / optimizationRatios.length).toFixed(2)),
        best: parseFloat(Math.max(...optimizationRatios).toFixed(2)),
        worst: parseFloat(Math.min(...optimizationRatios).toFixed(2))
      };
    }

    // Identify best practices
    optimization.bestPractices = this.identifyBestPractices(headingData, keyword);
    
    // Identify common mistakes
    optimization.commonMistakes = this.identifyCommonMistakes(headingData, keyword);
    
    // Generate optimal strategy
    optimization.optimalStrategy = this.generateOptimalStrategy(headingData, keyword);

    return optimization;
  }

  // Identify best practices from competitor analysis
  identifyBestPractices(headingData, keyword) {
    const practices = [];
    
    // Find patterns from top performers
    const topPerformers = headingData
      .filter(data => data.position <= 3)
      .sort((a, b) => a.position - b.position);

    if (topPerformers.length > 0) {
      // H1 optimization
      const h1Optimized = topPerformers.filter(data => 
        data.keywordAnalysis.byLevel.h1.withKeyword > 0
      ).length;
      
      if (h1Optimized >= topPerformers.length * 0.6) {
        practices.push('Include main keyword in H1 tag for maximum SEO impact');
      }

      // H2 optimization patterns
      const avgH2Count = topPerformers.reduce((sum, data) => 
        sum + data.statistics.h2Count, 0) / topPerformers.length;
      
      if (avgH2Count > 3) {
        practices.push(`Use ${Math.round(avgH2Count)} H2 headings on average for optimal structure`);
      }

      // Keyword distribution
      const balancedDistribution = topPerformers.filter(data => {
        const ratio = data.keywordAnalysis.mainKeywordRatio;
        return ratio >= 15 && ratio <= 40; // Balanced keyword usage
      }).length;

      if (balancedDistribution >= topPerformers.length * 0.5) {
        practices.push('Maintain 15-40% keyword optimization ratio in headings');
      }
    }

    return practices;
  }

  // Identify common mistakes from competitor analysis
  identifyCommonMistakes(headingData, keyword) {
    const mistakes = [];
    
    // Over-optimization check
    const overOptimized = headingData.filter(data => 
      data.keywordAnalysis.mainKeywordRatio > 50
    ).length;
    
    if (overOptimized > headingData.length * 0.3) {
      mistakes.push('Avoid over-optimizing headings with excessive keyword usage');
    }

    // Under-optimization check
    const underOptimized = headingData.filter(data => 
      data.keywordAnalysis.mainKeywordRatio < 10
    ).length;
    
    if (underOptimized > headingData.length * 0.3) {
      mistakes.push('Ensure adequate keyword presence in heading structure');
    }

    // Missing H1 optimization
    const missingH1 = headingData.filter(data => 
      data.keywordAnalysis.byLevel.h1.withKeyword === 0
    ).length;
    
    if (missingH1 > headingData.length * 0.4) {
      mistakes.push('Always include target keyword in H1 for SEO relevance');
    }

    // Poor heading hierarchy
    const poorHierarchy = headingData.filter(data => {
      const stats = data.statistics;
      return stats.h1Count === 0 || (stats.h3Count > 0 && stats.h2Count === 0);
    }).length;
    
    if (poorHierarchy > headingData.length * 0.2) {
      mistakes.push('Maintain proper heading hierarchy (H1 → H2 → H3)');
    }

    return mistakes;
  }

  // Generate optimal heading strategy
  generateOptimalStrategy(headingData, keyword) {
    const strategy = {
      structure: {},
      keywordPlacement: {},
      recommendations: []
    };

    // Calculate optimal structure based on top performers
    const topPerformers = headingData
      .filter(data => data.position <= 3)
      .sort((a, b) => a.position - b.position);

    if (topPerformers.length > 0) {
      // Optimal heading counts
      strategy.structure = {
        h1: 1, // Always exactly 1
        h2: Math.round(topPerformers.reduce((sum, data) => sum + data.statistics.h2Count, 0) / topPerformers.length),
        h3: Math.round(topPerformers.reduce((sum, data) => sum + data.statistics.h3Count, 0) / topPerformers.length),
        total: Math.round(topPerformers.reduce((sum, data) => sum + data.statistics.total, 0) / topPerformers.length)
      };

      // Optimal keyword placement
      const avgOptimization = topPerformers.reduce((sum, data) => 
        sum + data.keywordAnalysis.mainKeywordRatio, 0) / topPerformers.length;

      strategy.keywordPlacement = {
        h1: 'Always include main keyword',
        h2: `Include keyword in ${Math.round(avgOptimization * 0.4)}% of H2 headings`,
        h3: `Include keyword components in ${Math.round(avgOptimization * 0.2)}% of H3 headings`,
        targetRatio: Math.round(avgOptimization)
      };

      // Generate specific recommendations
      strategy.recommendations = [
        `Create ${strategy.structure.h2} main H2 sections for optimal content structure`,
        `Use ${strategy.structure.h3} H3 subsections to break down complex topics`,
        `Target ${strategy.keywordPlacement.targetRatio}% keyword optimization across all headings`,
        'Place main keyword in H1 and primary H2 headings',
        'Use keyword components and variations in H3 and lower-level headings'
      ];
    }

    return strategy;
  }

  // Generate heading recommendations with LSI/entities
  generateHeadingRecommendations(benchmarks, keywordOptimization, keyword, semanticData = null) {
    const recommendations = [];

    if (!benchmarks) {
      return ['No competitor data available for recommendations'];
    }

    // Structure recommendations
    recommendations.push({
      type: 'structure',
      priority: 'high',
      title: 'Optimal Heading Structure',
      description: `Use ${benchmarks.totalHeadings.average} total headings with ${benchmarks.distribution.h2.average} H2 and ${benchmarks.distribution.h3.average} H3 headings`,
      benchmark: benchmarks.totalHeadings.average
    });

    // Keyword optimization recommendations
    recommendations.push({
      type: 'keyword_optimization',
      priority: 'critical',
      title: 'Keyword Optimization Target',
      description: `Optimize ${benchmarks.keywordOptimization.targetRatio}% of headings with target keyword`,
      benchmark: benchmarks.keywordOptimization.targetRatio
    });

    // H1 optimization
    if (benchmarks.keywordPlacements.h1Priority > 80) {
      recommendations.push({
        type: 'h1_optimization',
        priority: 'critical',
        title: 'H1 Keyword Inclusion',
        description: 'Include main keyword in H1 tag - this is used by most top-ranking competitors',
        benchmark: 100
      });
    }

    // H2 optimization
    if (benchmarks.keywordPlacements.h2Priority > 30) {
      recommendations.push({
        type: 'h2_optimization',
        priority: 'high',
        title: 'H2 Keyword Usage',
        description: `Include keyword in primary H2 headings based on competitor patterns`,
        benchmark: benchmarks.keywordPlacements.h2Priority
      });
    }

    // LSI Keywords recommendations
    if (semanticData && semanticData.headingOptimizedTerms.length > 0) {
      recommendations.push({
        type: 'lsi_optimization',
        priority: 'high',
        title: 'LSI Keywords in Headings',
        description: `Integrate ${Math.min(5, semanticData.headingOptimizedTerms.length)} high-priority LSI terms in headings: ${semanticData.headingOptimizedTerms.slice(0, 5).join(', ')}`,
        benchmark: semanticData.headingOptimizedTerms.slice(0, 5)
      });
    }

    // Entity optimization recommendations
    if (semanticData && semanticData.prioritizedEntities.length > 0) {
      recommendations.push({
        type: 'entity_optimization',
        priority: 'medium',
        title: 'Entity Integration',
        description: `Include relevant entities in headings: ${semanticData.prioritizedEntities.slice(0, 3).map(e => e.term).join(', ')}`,
        benchmark: semanticData.prioritizedEntities.slice(0, 3).map(e => e.term)
      });
    }

    // Semantic cluster recommendations
    if (semanticData && semanticData.semanticClusters) {
      if (semanticData.semanticClusters.primary.length > 0) {
        recommendations.push({
          type: 'semantic_primary',
          priority: 'high',
          title: 'Primary Semantic Terms',
          description: `Use primary semantic cluster terms in main headings: ${semanticData.semanticClusters.primary.slice(0, 3).map(item => item.term).join(', ')}`,
          benchmark: semanticData.semanticClusters.primary.slice(0, 3).map(item => item.term)
        });
      }

      if (semanticData.semanticClusters.commercial.length > 0) {
        recommendations.push({
          type: 'semantic_commercial',
          priority: 'medium',
          title: 'Commercial Intent Terms',
          description: `Include commercial terms for user intent: ${semanticData.semanticClusters.commercial.slice(0, 2).map(item => item.term).join(', ')}`,
          benchmark: semanticData.semanticClusters.commercial.slice(0, 2).map(item => item.term)
        });
      }
    }

    // Component keyword recommendations
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    keywordComponents.forEach(component => {
      const optimization = benchmarks.componentOptimization[component];
      if (optimization && optimization.averageUsage > 1) {
        recommendations.push({
          type: 'component_optimization',
          priority: 'medium',
          title: `"${component}" Component Usage`,
          description: `Use "${component}" in approximately ${Math.round(optimization.averageUsage)} headings`,
          benchmark: Math.round(optimization.averageUsage)
        });
      }
    });

    // Variation recommendations
    if (semanticData && semanticData.prioritizedVariations.length > 0) {
      recommendations.push({
        type: 'variation_optimization',
        priority: 'medium',
        title: 'Keyword Variations',
        description: `Use keyword variations in subheadings: ${semanticData.prioritizedVariations.slice(0, 3).map(v => v.term).join(', ')}`,
        benchmark: semanticData.prioritizedVariations.slice(0, 3).map(v => v.term)
      });
    }

    // Best practice recommendations
    if (keywordOptimization.bestPractices.length > 0) {
      keywordOptimization.bestPractices.forEach(practice => {
        recommendations.push({
          type: 'best_practice',
          priority: 'medium',
          title: 'Industry Best Practice',
          description: practice,
          benchmark: null
        });
      });
    }

    return recommendations;
  }

  // Create optimal heading template with semantic integration
  createOptimalHeadingTemplate(benchmarks, keywordOptimization, keyword, semanticData = null) {
    if (!benchmarks) {
      return null;
    }

    const template = {
      structure: {
        h1: {
          count: 1,
          shouldIncludeKeyword: true,
          examples: this.generateSemanticH1Examples(keyword, semanticData),
          lsiIntegration: semanticData ? semanticData.headingOptimizedTerms.slice(0, 2) : []
        },
        h2: {
          count: benchmarks.distribution.h2.average,
          keywordOptimization: Math.round(benchmarks.keywordOptimization.targetRatio * 0.6),
          examples: this.generateSemanticH2Examples(keyword, benchmarks.distribution.h2.average, semanticData),
          lsiIntegration: semanticData ? semanticData.headingOptimizedTerms.slice(0, 5) : [],
          entityIntegration: semanticData ? semanticData.prioritizedEntities.slice(0, 2).map(e => e.term) : []
        },
        h3: {
          count: benchmarks.distribution.h3.average,
          keywordOptimization: Math.round(benchmarks.keywordOptimization.targetRatio * 0.3),
          examples: this.generateSemanticH3Examples(keyword, benchmarks.distribution.h3.average, semanticData),
          variationIntegration: semanticData ? semanticData.prioritizedVariations.slice(0, 3).map(v => v.term) : []
        }
      },
      semanticOptimization: semanticData ? {
        lsiStrategy: {
          headings: `Integrate ${Math.min(5, semanticData.headingOptimizedTerms.length)} LSI terms in headings`,
          priority1: semanticData.headingOptimizedTerms.slice(0, 3),
          priority2: semanticData.headingOptimizedTerms.slice(3, 6)
        },
        entityStrategy: {
          primary: semanticData.prioritizedEntities.slice(0, 3).map(e => e.term),
          usage: 'Include 1-2 relevant entities per main heading section'
        },
        variationStrategy: {
          terms: semanticData.prioritizedVariations.slice(0, 5).map(v => v.term),
          usage: 'Use variations in H3 and lower-level headings'
        },
        clusters: semanticData.semanticClusters ? {
          primary: semanticData.semanticClusters.primary.slice(0, 3).map(item => item.term),
          commercial: semanticData.semanticClusters.commercial.slice(0, 2).map(item => item.term),
          supporting: semanticData.semanticClusters.supporting.slice(0, 4).map(item => item.term)
        } : null
      } : null,
      keywordDistribution: {
        mainKeyword: `${benchmarks.keywordOptimization.targetRatio}% of headings`,
        components: this.generateComponentDistribution(keyword, benchmarks),
        lsiKeywords: semanticData ? `Use ${semanticData.headingOptimizedTerms.length} LSI terms across headings` : 'LSI data not available',
        variations: semanticData ? `Include ${semanticData.prioritizedVariations.length} variations in content` : 'Use keyword variations in 20-30% of headings'
      },
      implementation: {
        totalHeadings: benchmarks.totalHeadings.average,
        semanticIntegration: semanticData ? semanticData.usageRecommendations : null,
        hierarchyRules: [
          'Start with one H1 containing main keyword and primary LSI term',
          'Use H2 for main sections with LSI keyword integration',
          'Use H3 for subsections with variations and supporting terms',
          'Include relevant entities in main headings',
          'Avoid skipping heading levels',
          'Integrate semantic terms naturally in headings'
        ],
        contentGuidelines: [
          'Distribute LSI keywords evenly throughout content',
          'Use entities to establish topical authority',
          'Include variations to capture long-tail searches',
          'Maintain natural language flow in all headings'
        ]
      }
    };

    return template;
  }

  // Generate semantic H1 examples
  generateSemanticH1Examples(keyword, semanticData) {
    const baseExamples = [
      `${keyword} - Complete Guide`,
      `Ultimate ${keyword} Guide`,
      `${keyword}: Everything You Need to Know`,
      `The Complete ${keyword} Guide`
    ];

    if (!semanticData || semanticData.headingOptimizedTerms.length === 0) {
      return baseExamples;
    }

    const lsiTerm = semanticData.headingOptimizedTerms[0];
    const entity = semanticData.prioritizedEntities.length > 0 ? semanticData.prioritizedEntities[0].term : null;

    const semanticExamples = [
      `${keyword} ${lsiTerm} - Complete Guide`,
      `Ultimate ${keyword} Guide for ${lsiTerm}`,
      `${keyword}: ${lsiTerm} and Best Practices`,
      entity ? `${keyword} Guide by ${entity}` : `Professional ${keyword} Guide`
    ];

    return [...semanticExamples, ...baseExamples].slice(0, 4);
  }

  // Generate semantic H2 examples
  generateSemanticH2Examples(keyword, count, semanticData) {
    const baseExamples = [
      `What is ${keyword}?`,
      `Benefits of ${keyword}`,
      `How to Choose ${keyword}`,
      `${keyword} Best Practices`,
      `Common ${keyword} Mistakes`,
      `${keyword} vs Alternatives`,
      `${keyword} Implementation Guide`,
      `${keyword} Cost and Pricing`
    ];

    if (!semanticData) {
      return baseExamples.slice(0, count);
    }

    const lsiTerms = semanticData.headingOptimizedTerms.slice(0, 3);
    const entities = semanticData.prioritizedEntities.slice(0, 2).map(e => e.term);
    const commercialTerms = semanticData.semanticClusters?.commercial 
      ? semanticData.semanticClusters.commercial.slice(0, 2).map(item => item.term)
      : [];

    const semanticExamples = [
      lsiTerms[0] ? `${keyword} ${lsiTerms[0]} Guide` : null,
      lsiTerms[1] ? `How ${lsiTerms[1]} Affects ${keyword}` : null,
      entities[0] ? `${keyword} Solutions by ${entities[0]}` : null,
      commercialTerms[0] ? `Best ${keyword} ${commercialTerms[0]}` : null,
      lsiTerms[2] ? `${keyword} ${lsiTerms[2]} Tips` : null,
      entities[1] ? `${entities[1]} ${keyword} Recommendations` : null
    ].filter(Boolean);

    return [...semanticExamples, ...baseExamples].slice(0, count);
  }

  // Generate semantic H3 examples
  generateSemanticH3Examples(keyword, count, semanticData) {
    const keywordComponents = keyword.split(/\s+/);
    const baseExamples = [
      `Key Features of ${keywordComponents[0]}`,
      `${keywordComponents[keywordComponents.length - 1]} Considerations`,
      'Step-by-Step Process',
      'Expert Tips and Tricks',
      'Common Challenges',
      'Measurement and Analytics',
      'Tools and Resources',
      'Future Trends'
    ];

    if (!semanticData) {
      return baseExamples.slice(0, count);
    }

    const variations = semanticData.prioritizedVariations.slice(0, 3).map(v => v.term);
    const supportingTerms = semanticData.semanticClusters?.supporting 
      ? semanticData.semanticClusters.supporting.slice(0, 3).map(item => item.term)
      : [];
    const technicalTerms = semanticData.semanticClusters?.technical 
      ? semanticData.semanticClusters.technical.slice(0, 2).map(item => item.term)
      : [];

    const semanticExamples = [
      variations[0] ? `Understanding ${variations[0]}` : null,
      supportingTerms[0] ? `${supportingTerms[0]} Best Practices` : null,
      variations[1] ? `${variations[1]} Implementation` : null,
      technicalTerms[0] ? `${technicalTerms[0]} Considerations` : null,
      supportingTerms[1] ? `${supportingTerms[1]} Guidelines` : null,
      variations[2] ? `Advanced ${variations[2]} Techniques` : null
    ].filter(Boolean);

    return [...semanticExamples, ...baseExamples].slice(0, count);
  }

  // Generate H2 heading examples (legacy method for compatibility)
  generateH2Examples(keyword, count) {
    return this.generateSemanticH2Examples(keyword, count, null);
  }

  // Generate H3 heading examples (legacy method for compatibility)
  generateH3Examples(keyword, count) {
    return this.generateSemanticH3Examples(keyword, count, null);
  }

  // Generate component distribution strategy
  generateComponentDistribution(keyword, benchmarks) {
    const components = keyword.toLowerCase().split(/\s+/);
    const distribution = {};

    components.forEach(component => {
      const optimization = benchmarks.componentOptimization[component];
      if (optimization) {
        distribution[component] = {
          targetUsage: Math.round(optimization.averageUsage),
          priority: optimization.priority > 5 ? 'high' : 'medium',
          recommendation: `Use "${component}" in ${Math.round(optimization.averageUsage)} headings`
        };
      }
    });

    return distribution;
  }

  // Generate distribution recommendation
  generateDistributionRecommendation(placementCounts, totalPlacements) {
    const recommendations = [];

    if (placementCounts.h1 / totalPlacements > 0.8) {
      recommendations.push('Prioritize H1 keyword inclusion (critical for SEO)');
    }

    if (placementCounts.h2 / totalPlacements > 0.4) {
      recommendations.push('Include keywords in primary H2 sections');
    }

    if (placementCounts.h3 / totalPlacements > 0.2) {
      recommendations.push('Use keyword components in H3 subsections');
    }

    return recommendations.length > 0 ? recommendations.join(', ') : 'Balanced keyword distribution across heading levels';
  }

  // Calculate top 5 competitors heading averages
  calculateTop5HeadingAverages(top5HeadingData) {
    if (top5HeadingData.length === 0) {
      return null;
    }

    const averages = {
      totalHeadings: 0,
      h1Count: 0,
      h2Count: 0,
      h3Count: 0,
      h4Count: 0,
      h5Count: 0,
      h6Count: 0,
      keywordOptimizedHeadings: 0,
      keywordOptimizationRatio: 0
    };

    // Calculate averages for top 5 competitors
    top5HeadingData.forEach(data => {
      averages.totalHeadings += data.statistics.total || 0;
      averages.h1Count += data.statistics.h1Count || 0;
      averages.h2Count += data.statistics.h2Count || 0;
      averages.h3Count += data.statistics.h3Count || 0;
      averages.h4Count += data.statistics.h4Count || 0;
      averages.h5Count += data.statistics.h5Count || 0;
      averages.h6Count += data.statistics.h6Count || 0;
      averages.keywordOptimizedHeadings += data.statistics.keywordOptimized || 0;
      averages.keywordOptimizationRatio += data.keywordAnalysis.mainKeywordRatio || 0;
    });

    // Calculate final averages
    const count = top5HeadingData.length;
    return {
      totalHeadings: parseFloat((averages.totalHeadings / count).toFixed(1)),
      h1Count: parseFloat((averages.h1Count / count).toFixed(1)),
      h2Count: parseFloat((averages.h2Count / count).toFixed(1)),
      h3Count: parseFloat((averages.h3Count / count).toFixed(1)),
      h4Count: parseFloat((averages.h4Count / count).toFixed(1)),
      h5Count: parseFloat((averages.h5Count / count).toFixed(1)),
      h6Count: parseFloat((averages.h6Count / count).toFixed(1)),
      keywordOptimizedHeadings: parseFloat((averages.keywordOptimizedHeadings / count).toFixed(1)),
      keywordOptimizationRatio: parseFloat((averages.keywordOptimizationRatio / count).toFixed(2)),
      competitorCount: count
    };
  }

  // Extract LSI keywords and entities from top 5 competitors
  extractCompetitorLSIAndEntities(top5Competitors, keyword) {
    const extracted = {
      lsiKeywords: new Set(),
      entities: new Set(),
      variations: new Set(),
      semanticTerms: new Set(),
      headingLSI: new Set(),
      contentLSI: new Set()
    };

    top5Competitors.forEach(competitor => {
      try {
        // Extract from semantic analysis if available
        if (competitor.semanticAnalysis) {
          competitor.semanticAnalysis.semanticWords?.forEach(word => extracted.semanticTerms.add(word));
          competitor.semanticAnalysis.lsiKeywords?.forEach(word => extracted.lsiKeywords.add(word));
          competitor.semanticAnalysis.entities?.forEach(entity => extracted.entities.add(entity));
        }

        // Extract from LSI keywords if available
        if (competitor.lsiKeywords) {
          competitor.lsiKeywords.forEach(word => extracted.lsiKeywords.add(word));
        }

        // Extract from heading content
        if (competitor.headingStructure && competitor.headingStructure.headings) {
          const headingText = this.extractAllHeadingText(competitor.headingStructure.headings);
          const headingLSI = this.extractLSIFromText(headingText, keyword);
          headingLSI.forEach(word => extracted.headingLSI.add(word));
        }

        // Extract variations from keyword density analysis
        if (competitor.keywordDensity && competitor.keywordDensity.related) {
          competitor.keywordDensity.related.forEach(phrase => extracted.variations.add(phrase));
        }

      } catch (error) {
        console.error(`Error extracting LSI/entities from competitor ${competitor.domain}:`, error);
      }
    });

    return {
      lsiKeywords: Array.from(extracted.lsiKeywords).slice(0, 50),
      entities: Array.from(extracted.entities).slice(0, 30),
      variations: Array.from(extracted.variations).slice(0, 25),
      semanticTerms: Array.from(extracted.semanticTerms).slice(0, 40),
      headingLSI: Array.from(extracted.headingLSI).slice(0, 20),
      contentLSI: Array.from(extracted.contentLSI).slice(0, 30),
      sourceCount: top5Competitors.length
    };
  }

  // Research internet-wide LSI keywords and entities for target keyword
  async researchInternetLSIAndEntities(keyword) {
    try {
      console.log(`Researching internet-wide LSI and entities for: ${keyword}`);

      const research = {
        popularLSI: [],
        commonEntities: [],
        keywordVariations: [],
        relatedTerms: [],
        topicalKeywords: [],
        searchSuggestions: []
      };

      // Get search suggestions and related queries
      const suggestions = await this.getSearchSuggestions(keyword);
      research.searchSuggestions = suggestions.slice(0, 15);

      // Get popular LSI keywords from search results
      const lsiResults = await this.getLSIFromSearchResults(keyword);
      research.popularLSI = lsiResults.slice(0, 40);

      // Extract entities from knowledge graphs and featured snippets
      const entityResults = await this.getEntitiesFromSearch(keyword);
      research.commonEntities = entityResults.slice(0, 25);

      // Get keyword variations and related terms
      const variationResults = await this.getKeywordVariations(keyword);
      research.keywordVariations = variationResults.slice(0, 30);

      // Get topical keywords based on industry
      const topicalResults = this.getTopicalKeywords(keyword);
      research.topicalKeywords = topicalResults.slice(0, 20);

      return research;

    } catch (error) {
      console.error('Error researching internet LSI/entities:', error);
      return {
        popularLSI: [],
        commonEntities: [],
        keywordVariations: [],
        relatedTerms: [],
        topicalKeywords: [],
        searchSuggestions: []
      };
    }
  }

  // Get search suggestions using Serper API
  async getSearchSuggestions(keyword) {
    try {
      const response = await axios.post('https://google.serper.dev/search', {
        q: keyword,
        gl: 'us',
        hl: 'en',
        num: 10
      }, {
        headers: {
          'X-API-KEY': this.serperApiKey,
          'Content-Type': 'application/json'
        }
      });

      const suggestions = [];
      
      // Extract from related searches
      if (response.data.relatedSearches) {
        response.data.relatedSearches.forEach(related => {
          suggestions.push(related.query);
        });
      }

      // Extract from people also ask
      if (response.data.peopleAlsoAsk) {
        response.data.peopleAlsoAsk.forEach(paa => {
          const question = paa.question.toLowerCase();
          const words = this.tokenizer.tokenize(question) || [];
          words.forEach(word => {
            if (word.length > 3 && !this.isStopWord(word) && !keyword.toLowerCase().includes(word)) {
              suggestions.push(word);
            }
          });
        });
      }

      return [...new Set(suggestions)];

    } catch (error) {
      console.error('Error getting search suggestions:', error);
      return [];
    }
  }

  // Get LSI keywords from search results content
  async getLSIFromSearchResults(keyword) {
    try {
      const response = await axios.post('https://google.serper.dev/search', {
        q: `"${keyword}" LSI keywords semantic related terms`,
        gl: 'us',
        hl: 'en',
        num: 10
      }, {
        headers: {
          'X-API-KEY': this.serperApiKey,
          'Content-Type': 'application/json'
        }
      });

      const lsiKeywords = new Set();
      
      // Extract from snippets
      if (response.data.organic) {
        response.data.organic.forEach(result => {
          if (result.snippet) {
            const lsi = this.extractLSIFromText(result.snippet, keyword);
            lsi.forEach(word => lsiKeywords.add(word));
          }
        });
      }

      return Array.from(lsiKeywords);

    } catch (error) {
      console.error('Error getting LSI from search results:', error);
      return [];
    }
  }

  // Get entities from search results
  async getEntitiesFromSearch(keyword) {
    try {
      const response = await axios.post('https://google.serper.dev/search', {
        q: `"${keyword}" entities organizations companies brands`,
        gl: 'us',
        hl: 'en',
        num: 10
      }, {
        headers: {
          'X-API-KEY': this.serperApiKey,
          'Content-Type': 'application/json'
        }
      });

      const entities = new Set();
      
      // Extract from knowledge graph
      if (response.data.knowledgeGraph) {
        const kg = response.data.knowledgeGraph;
        if (kg.title) entities.add(kg.title);
        if (kg.type) entities.add(kg.type);
        if (kg.attributes) {
          Object.values(kg.attributes).forEach(attr => {
            if (typeof attr === 'string' && attr.length > 2 && attr.length < 30) {
              entities.add(attr);
            }
          });
        }
      }

      // Extract from organic results
      if (response.data.organic) {
        response.data.organic.forEach(result => {
          // Extract capitalized words (potential entities)
          const text = `${result.title} ${result.snippet}`;
          const capitalizedWords = text.match(/\b[A-Z][a-z]+\b/g) || [];
          capitalizedWords.forEach(word => {
            if (word.length > 3 && !this.isCommonWord(word.toLowerCase())) {
              entities.add(word);
            }
          });
        });
      }

      return Array.from(entities);

    } catch (error) {
      console.error('Error getting entities from search:', error);
      return [];
    }
  }

  // Get keyword variations
  async getKeywordVariations(keyword) {
    try {
      const variations = new Set();
      const keywordComponents = keyword.toLowerCase().split(/\s+/);

      // Generate morphological variations
      keywordComponents.forEach(component => {
        // Plurals
        variations.add(component + 's');
        variations.add(component + 'es');
        
        // Common suffixes
        variations.add(component + 'ing');
        variations.add(component + 'ed');
        variations.add(component + 'er');
        variations.add(component + 'est');
        
        // Common prefixes
        variations.add('best ' + component);
        variations.add('top ' + component);
        variations.add('professional ' + component);
      });

      // Search for "keyword alternatives" and "keyword synonyms"
      const searchQueries = [
        `"${keyword}" alternatives synonyms`,
        `"${keyword}" similar terms`,
        `"${keyword}" related keywords`
      ];

      for (const query of searchQueries) {
        try {
          const response = await axios.post('https://google.serper.dev/search', {
            q: query,
            gl: 'us',
            hl: 'en',
            num: 5
          }, {
            headers: {
              'X-API-KEY': this.serperApiKey,
              'Content-Type': 'application/json'
            }
          });

          if (response.data.organic) {
            response.data.organic.forEach(result => {
              const text = result.snippet.toLowerCase();
              const words = this.tokenizer.tokenize(text) || [];
              words.forEach(word => {
                if (word.length > 3 && !this.isStopWord(word) && this.isRelatedToKeyword(word, keyword)) {
                  variations.add(word);
                }
              });
            });
          }
        } catch (error) {
          console.error(`Error searching for variations with query: ${query}`, error);
        }
      }

      return Array.from(variations);

    } catch (error) {
      console.error('Error getting keyword variations:', error);
      return [];
    }
  }

  // Get topical keywords based on industry context
  getTopicalKeywords(keyword) {
    const keywordLower = keyword.toLowerCase();
    const topicalMaps = {
      // Technology keywords
      'software': ['development', 'programming', 'coding', 'application', 'system', 'platform', 'tools', 'framework'],
      'app': ['mobile', 'application', 'download', 'features', 'interface', 'user', 'functionality'],
      'digital': ['marketing', 'strategy', 'online', 'internet', 'technology', 'transformation'],
      
      // Business keywords
      'business': ['strategy', 'management', 'growth', 'marketing', 'sales', 'revenue', 'profit', 'operations'],
      'marketing': ['strategy', 'campaign', 'advertising', 'promotion', 'branding', 'customers', 'leads'],
      'sales': ['revenue', 'customers', 'leads', 'conversion', 'pipeline', 'prospects', 'deals'],
      
      // Health keywords
      'health': ['wellness', 'fitness', 'medical', 'treatment', 'care', 'diagnosis', 'prevention', 'recovery'],
      'fitness': ['exercise', 'workout', 'training', 'gym', 'health', 'strength', 'cardio', 'nutrition'],
      
      // Finance keywords
      'finance': ['money', 'investment', 'banking', 'financial', 'budget', 'savings', 'credit', 'loans'],
      'investment': ['portfolio', 'returns', 'risk', 'stocks', 'bonds', 'mutual funds', 'retirement'],
      
      // Real Estate keywords
      'real estate': ['property', 'home', 'house', 'buying', 'selling', 'market', 'investment', 'location'],
      'property': ['real estate', 'investment', 'rental', 'value', 'market', 'location', 'ownership'],
      
      // Education keywords
      'education': ['learning', 'teaching', 'school', 'university', 'course', 'training', 'knowledge', 'skills'],
      'course': ['learning', 'education', 'training', 'curriculum', 'certification', 'online', 'instructor']
    };

    const topicalKeywords = new Set();
    
    // Find matching categories
    Object.keys(topicalMaps).forEach(category => {
      if (keywordLower.includes(category) || category.includes(keywordLower)) {
        topicalMaps[category].forEach(term => topicalKeywords.add(term));
      }
    });

    // Add generic business/industry terms
    const genericTerms = [
      'professional', 'expert', 'service', 'solution', 'guide', 'tips', 'best practices',
      'benefits', 'advantages', 'features', 'cost', 'price', 'quality', 'reviews'
    ];
    
    genericTerms.forEach(term => topicalKeywords.add(term));

    return Array.from(topicalKeywords);
  }

  // Combine competitor and internet LSI/entity data
  combineSemanticData(competitorData, internetData, keyword) {
    const combined = {
      prioritizedLSI: [],
      prioritizedEntities: [],
      prioritizedVariations: [],
      headingOptimizedTerms: [],
      contentOptimizedTerms: [],
      semanticClusters: {},
      usageRecommendations: {}
    };

    // Prioritize LSI keywords (competitor data gets higher priority)
    const allLSI = new Map();
    
    // Add competitor LSI with high priority
    competitorData.lsiKeywords.forEach(word => {
      allLSI.set(word, (allLSI.get(word) || 0) + 3);
    });
    
    // Add internet LSI with medium priority
    internetData.popularLSI.forEach(word => {
      allLSI.set(word, (allLSI.get(word) || 0) + 2);
    });
    
    // Add search suggestions with low priority
    internetData.searchSuggestions.forEach(word => {
      allLSI.set(word, (allLSI.get(word) || 0) + 1);
    });

    // Sort by priority and take top terms
    combined.prioritizedLSI = Array.from(allLSI.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 50)
      .map(entry => ({ term: entry[0], priority: entry[1] }));

    // Prioritize entities
    const allEntities = new Map();
    competitorData.entities.forEach(entity => {
      allEntities.set(entity, (allEntities.get(entity) || 0) + 3);
    });
    internetData.commonEntities.forEach(entity => {
      allEntities.set(entity, (allEntities.get(entity) || 0) + 2);
    });

    combined.prioritizedEntities = Array.from(allEntities.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 30)
      .map(entry => ({ term: entry[0], priority: entry[1] }));

    // Prioritize variations
    const allVariations = new Map();
    competitorData.variations.forEach(variation => {
      allVariations.set(variation, (allVariations.get(variation) || 0) + 3);
    });
    internetData.keywordVariations.forEach(variation => {
      allVariations.set(variation, (allVariations.get(variation) || 0) + 2);
    });

    combined.prioritizedVariations = Array.from(allVariations.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 25)
      .map(entry => ({ term: entry[0], priority: entry[1] }));

    // Create semantic clusters
    combined.semanticClusters = this.createSemanticClusters(combined.prioritizedLSI, keyword);

    // Generate usage recommendations
    combined.usageRecommendations = this.generateSemanticUsageRecommendations(combined, keyword);

    // Separate terms for headings vs content
    combined.headingOptimizedTerms = combined.prioritizedLSI
      .filter(item => item.priority >= 2)
      .slice(0, 15)
      .map(item => item.term);

    combined.contentOptimizedTerms = combined.prioritizedLSI
      .slice(0, 35)
      .map(item => item.term);

    return combined;
  }

  // Create semantic clusters for better organization
  createSemanticClusters(prioritizedLSI, keyword) {
    const clusters = {
      primary: [], // Direct keyword variations
      secondary: [], // Closely related terms
      supporting: [], // Supporting/context terms
      technical: [], // Technical/specific terms
      commercial: [] // Commercial intent terms
    };

    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    
    prioritizedLSI.forEach(item => {
      const term = item.term.toLowerCase();
      
      // Primary cluster: contains keyword components
      if (keywordComponents.some(component => term.includes(component) || component.includes(term))) {
        clusters.primary.push(item);
      }
      // Commercial cluster: commercial intent
      else if (/buy|purchase|price|cost|cheap|best|top|review|compare/.test(term)) {
        clusters.commercial.push(item);
      }
      // Technical cluster: technical terms
      else if (term.length > 8 || /tion|ing|ment|ness/.test(term)) {
        clusters.technical.push(item);
      }
      // Secondary cluster: related terms
      else if (item.priority >= 2) {
        clusters.secondary.push(item);
      }
      // Supporting cluster: context terms
      else {
        clusters.supporting.push(item);
      }
    });

    return clusters;
  }

  // Generate usage recommendations for semantic terms
  generateSemanticUsageRecommendations(combinedData, keyword) {
    return {
      headingStrategy: {
        h1: 'Include main keyword and 1 primary LSI term',
        h2: `Use ${Math.min(3, combinedData.headingOptimizedTerms.length)} LSI terms across H2 headings`,
        h3: 'Include variations and supporting terms in H3 headings',
        entities: `Incorporate ${Math.min(2, combinedData.prioritizedEntities.length)} relevant entities in main headings`
      },
      contentStrategy: {
        density: 'Use LSI terms at 0.5-1% density throughout content',
        distribution: 'Distribute semantic terms evenly across content sections',
        naturalness: 'Integrate terms naturally within context',
        variations: `Use ${Math.min(5, combinedData.prioritizedVariations.length)} keyword variations in body content`
      },
      implementation: {
        priority1: combinedData.headingOptimizedTerms.slice(0, 5),
        priority2: combinedData.headingOptimizedTerms.slice(5, 10),
        priority3: combinedData.contentOptimizedTerms.slice(10, 20),
        entities: combinedData.prioritizedEntities.slice(0, 8).map(e => e.term)
      }
    };
  }

  // Helper method to extract all heading text
  extractAllHeadingText(headings) {
    let allText = '';
    Object.values(headings).forEach(levelHeadings => {
      levelHeadings.forEach(heading => {
        allText += ' ' + heading.text;
      });
    });
    return allText;
  }

  // Helper method to extract LSI from text
  extractLSIFromText(text, keyword) {
    const words = this.tokenizer.tokenize(text.toLowerCase()) || [];
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    const lsiWords = [];

    words.forEach(word => {
      if (word.length > 3 && 
          !this.isStopWord(word) && 
          !keywordComponents.includes(word) &&
          !keyword.toLowerCase().includes(word)) {
        lsiWords.push(word);
      }
    });

    return [...new Set(lsiWords)];
  }

  // Helper method to check if word is related to keyword
  isRelatedToKeyword(word, keyword) {
    const keywordComponents = keyword.toLowerCase().split(/\s+/);
    const wordLower = word.toLowerCase();
    
    // Check semantic similarity (simplified)
    return keywordComponents.some(component => {
      return this.stemmer.stem(wordLower) === this.stemmer.stem(component) ||
             wordLower.includes(component) ||
             component.includes(wordLower);
    });
  }

  // Helper method to check if word is a stop word
  isStopWord(word) {
    const stopWords = [
      'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 
      'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 
      'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 
      'did', 'let', 'put', 'say', 'she', 'too', 'use', 'with', 'from', 'they',
      'have', 'this', 'that', 'will', 'your', 'what', 'when', 'where', 'why',
      'more', 'than', 'into', 'also', 'been', 'only', 'some', 'time', 'very',
      'after', 'other', 'many', 'them', 'these', 'well', 'were'
    ];
    return stopWords.includes(word.toLowerCase());
  }

  // Helper method to check if word is common
  isCommonWord(word) {
    const commonWords = [
      'about', 'would', 'there', 'could', 'other', 'after', 'first', 'never', 
      'these', 'think', 'where', 'being', 'every', 'great', 'might', 'shall', 
      'still', 'those', 'while', 'state', 'start', 'place', 'right', 'move',
      'thing', 'general', 'high', 'each', 'much', 'own', 'under', 'last'
    ];
    return commonWords.includes(word.toLowerCase());
  }

  // Utility method to calculate median
  calculateMedian(numbers) {
    const sorted = [...numbers].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    
    if (sorted.length % 2 === 0) {
      return (sorted[mid - 1] + sorted[mid]) / 2;
    } else {
      return sorted[mid];
    }
  }
}

module.exports = HeadingStructureAnalyzer;