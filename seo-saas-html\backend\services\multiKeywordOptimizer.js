const natural = require('natural');
const AdvancedKeywordDensityCalculator = require('./keywordDensityCalculator');

class MultiKeywordOptimizer {
  constructor() {
    this.densityCalculator = new AdvancedKeywordDensityCalculator();
    this.stemmer = natural.PorterStemmer;
    this.tokenizer = new natural.WordTokenizer();
  }

  // Main method for multi-keyword density optimization
  async optimizeMultiKeywordDensity(content, primaryKeyword, competitorBenchmarks, semanticData, options = {}) {
    try {
      console.log(`Starting multi-keyword density optimization for: ${primaryKeyword}`);

      // Extract all keywords that need optimization
      const keywordTargets = this.extractKeywordTargets(primaryKeyword, competitorBenchmarks, semanticData);
      
      // Analyze current keyword densities
      const currentAnalysis = this.analyzeCurrentDensities(content, keywordTargets);
      
      // Calculate precision optimization requirements
      const optimizationPlan = this.createOptimizationPlan(currentAnalysis, keywordTargets, competitorBenchmarks);
      
      // Perform surgical keyword density optimization
      const optimizedContent = await this.performSurgicalOptimization(content, optimizationPlan, options);
      
      // Validate optimization results
      const validationResults = this.validateOptimization(optimizedContent, keywordTargets);
      
      // Fine-tune if necessary
      const finalContent = await this.performFineTuning(optimizedContent, validationResults, keywordTargets);

      return {
        success: true,
        content: finalContent,
        optimization: {
          keywordTargets,
          currentAnalysis,
          optimizationPlan,
          validationResults: this.analyzeCurrentDensities(finalContent, keywordTargets),
          improvements: this.calculateImprovements(currentAnalysis, finalContent, keywordTargets)
        },
        metadata: {
          totalKeywordsOptimized: keywordTargets.length,
          optimizationAccuracy: this.calculateOptimizationAccuracy(finalContent, keywordTargets),
          processedAt: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('Multi-keyword optimization error:', error);
      return {
        success: false,
        error: error.message,
        content: content // Return original content on error
      };
    }
  }

  // Extract all keywords that need density optimization
  extractKeywordTargets(primaryKeyword, benchmarks, semanticData) {
    const targets = [];

    // Primary keyword target
    targets.push({
      type: 'primary',
      keyword: primaryKeyword,
      targetDensity: benchmarks.keywordDensity.main,
      priority: 'critical',
      tolerance: 0.1 // 0.1% tolerance
    });

    // Component keyword targets
    const components = primaryKeyword.toLowerCase().split(/\s+/);
    components.forEach(component => {
      if (component.length > 2 && benchmarks.keywordDensity.components[component]) {
        targets.push({
          type: 'component',
          keyword: component,
          targetDensity: benchmarks.keywordDensity.components[component],
          priority: 'high',
          tolerance: 0.15
        });
      }
    });

    // LSI keyword targets (if semantic data available)
    if (semanticData && semanticData.headingOptimizedTerms) {
      semanticData.headingOptimizedTerms.slice(0, 10).forEach(lsiTerm => {
        targets.push({
          type: 'lsi',
          keyword: lsiTerm,
          targetDensity: 0.5, // 0.5% target density for LSI keywords
          priority: 'medium',
          tolerance: 0.2
        });
      });
    }

    // Entity targets
    if (semanticData && semanticData.prioritizedEntities) {
      semanticData.prioritizedEntities.slice(0, 5).forEach(entity => {
        targets.push({
          type: 'entity',
          keyword: entity.term,
          targetDensity: 0.3, // 0.3% target density for entities
          priority: 'medium',
          tolerance: 0.25,
          minMentions: 2 // Minimum 2 mentions for entities
        });
      });
    }

    // Variation targets
    if (semanticData && semanticData.prioritizedVariations) {
      semanticData.prioritizedVariations.slice(0, 8).forEach(variation => {
        targets.push({
          type: 'variation',
          keyword: variation.term,
          targetDensity: 0.2, // 0.2% target density for variations
          priority: 'low',
          tolerance: 0.3,
          minMentions: 1 // Minimum 1 mention for variations
        });
      });
    }

    // Sort by priority
    const priorityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
    targets.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);

    console.log(`Extracted ${targets.length} keyword targets for optimization`);
    return targets;
  }

  // Analyze current keyword densities
  analyzeCurrentDensities(content, keywordTargets) {
    const analysis = {};
    const words = this.tokenizer.tokenize(content.toLowerCase()) || [];
    const totalWords = words.length;

    keywordTargets.forEach(target => {
      const keyword = target.keyword.toLowerCase();
      
      // Count occurrences
      let matches = 0;
      if (target.type === 'primary') {
        // For primary keyword, look for exact phrase matches
        const regex = new RegExp(keyword.replace(/\s+/g, '\\s+'), 'gi');
        matches = (content.toLowerCase().match(regex) || []).length;
      } else {
        // For other keywords, look for word boundary matches
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
        matches = (content.toLowerCase().match(regex) || []).length;
      }

      const currentDensity = totalWords > 0 ? (matches / totalWords) * 100 : 0;
      const targetCount = Math.round((target.targetDensity / 100) * totalWords);

      analysis[target.keyword] = {
        type: target.type,
        priority: target.priority,
        currentMatches: matches,
        currentDensity: parseFloat(currentDensity.toFixed(3)),
        targetDensity: target.targetDensity,
        targetCount,
        difference: parseFloat((currentDensity - target.targetDensity).toFixed(3)),
        status: this.getDensityStatus(currentDensity, target.targetDensity, target.tolerance),
        needsOptimization: Math.abs(currentDensity - target.targetDensity) > target.tolerance
      };
    });

    return analysis;
  }

  // Create optimization plan
  createOptimizationPlan(currentAnalysis, keywordTargets, benchmarks) {
    const plan = {
      addKeywords: [],
      removeKeywords: [],
      replaceKeywords: [],
      repositionKeywords: [],
      totalAdjustments: 0
    };

    Object.entries(currentAnalysis).forEach(([keyword, analysis]) => {
      if (!analysis.needsOptimization) return;

      const target = keywordTargets.find(t => t.keyword === keyword);
      if (!target) return;

      if (analysis.currentDensity < analysis.targetDensity) {
        // Need to add more instances
        const toAdd = analysis.targetCount - analysis.currentMatches;
        plan.addKeywords.push({
          keyword,
          type: target.type,
          priority: target.priority,
          count: toAdd,
          currentDensity: analysis.currentDensity,
          targetDensity: analysis.targetDensity,
          strategy: this.determineAdditionStrategy(target.type, keyword)
        });
        plan.totalAdjustments += toAdd;
      } else {
        // Need to remove instances
        const toRemove = analysis.currentMatches - analysis.targetCount;
        plan.removeKeywords.push({
          keyword,
          type: target.type,
          priority: target.priority,
          count: toRemove,
          currentDensity: analysis.currentDensity,
          targetDensity: analysis.targetDensity,
          strategy: this.determineRemovalStrategy(target.type, keyword)
        });
        plan.totalAdjustments += toRemove;
      }
    });

    // Sort by priority
    const priorityOrder = { 'critical': 4, 'high': 3, 'medium': 2, 'low': 1 };
    plan.addKeywords.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
    plan.removeKeywords.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);

    console.log(`Created optimization plan: +${plan.addKeywords.length} additions, -${plan.removeKeywords.length} removals`);
    return plan;
  }

  // Determine addition strategy based on keyword type
  determineAdditionStrategy(type, keyword) {
    switch (type) {
      case 'primary':
        return {
          method: 'strategic_placement',
          locations: ['headings', 'first_paragraph', 'last_paragraph', 'subheadings'],
          variations: [`effective ${keyword}`, `professional ${keyword}`, `quality ${keyword}`],
          naturalPhrases: [`when it comes to ${keyword}`, `${keyword} solutions`, `${keyword} strategies`]
        };
      
      case 'component':
        return {
          method: 'natural_integration',
          locations: ['throughout_content', 'lists', 'examples'],
          variations: [`${keyword}s`, `${keyword}-based`, `${keyword} related`],
          contextualPhrases: [`important ${keyword}`, `effective ${keyword}`, `${keyword} approach`]
        };
      
      case 'lsi':
        return {
          method: 'semantic_integration',
          locations: ['supporting_sentences', 'explanations', 'details'],
          variations: [`${keyword} methods`, `${keyword} techniques`, `${keyword} approaches`],
          semanticContext: true
        };
      
      case 'entity':
        return {
          method: 'authority_mentions',
          locations: ['credibility_sections', 'expert_references', 'industry_context'],
          variations: [`${keyword} experts`, `${keyword} professionals`, `according to ${keyword}`],
          authorityContext: true
        };
      
      case 'variation':
        return {
          method: 'long_tail_integration',
          locations: ['detailed_sections', 'faq_style', 'comprehensive_coverage'],
          variations: [`best ${keyword}`, `top ${keyword}`, `${keyword} guide`],
          longTailFocus: true
        };
      
      default:
        return {
          method: 'standard_placement',
          locations: ['body_content'],
          variations: [keyword]
        };
    }
  }

  // Determine removal strategy
  determineRemovalStrategy(type, keyword) {
    switch (type) {
      case 'primary':
        return {
          method: 'synonym_replacement',
          replacements: ['this solution', 'the approach', 'this method', 'the system'],
          keepInHeadings: true // Always keep primary keyword in headings
        };
      
      case 'component':
        return {
          method: 'pronoun_replacement',
          replacements: ['it', 'this', 'these', 'such solutions']
        };
      
      case 'lsi':
        return {
          method: 'selective_removal',
          criteria: 'remove_least_impactful' // Remove from less important sections
        };
      
      case 'entity':
        return {
          method: 'consolidation',
          strategy: 'merge_mentions' // Combine multiple mentions into fewer, stronger ones
        };
      
      case 'variation':
        return {
          method: 'alternative_phrasing',
          replacements: ['related concepts', 'similar approaches', 'alternative methods']
        };
      
      default:
        return {
          method: 'simple_removal'
        };
    }
  }

  // Perform surgical keyword density optimization
  async performSurgicalOptimization(content, plan, options) {
    let optimizedContent = content;

    // Process additions first (higher impact)
    for (const addition of plan.addKeywords) {
      optimizedContent = await this.addKeywordInstances(
        optimizedContent, 
        addition.keyword, 
        addition.count, 
        addition.strategy,
        addition.type
      );
    }

    // Process removals
    for (const removal of plan.removeKeywords) {
      optimizedContent = await this.removeKeywordInstances(
        optimizedContent, 
        removal.keyword, 
        removal.count, 
        removal.strategy,
        removal.type
      );
    }

    // Ensure content flows naturally
    optimizedContent = this.improveContentFlow(optimizedContent);

    return optimizedContent;
  }

  // Add keyword instances strategically
  async addKeywordInstances(content, keyword, count, strategy, type) {
    let modifiedContent = content;
    let addedCount = 0;

    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0);

    for (let i = 0; i < count && addedCount < count; i++) {
      switch (strategy.method) {
        case 'strategic_placement':
          modifiedContent = this.addToStrategicLocations(modifiedContent, keyword, strategy);
          addedCount++;
          break;

        case 'natural_integration':
          modifiedContent = this.integrateNaturally(modifiedContent, keyword, strategy);
          addedCount++;
          break;

        case 'semantic_integration':
          modifiedContent = this.integrateSemanticallly(modifiedContent, keyword, strategy);
          addedCount++;
          break;

        case 'authority_mentions':
          modifiedContent = this.addAuthorityMentions(modifiedContent, keyword, strategy);
          addedCount++;
          break;

        case 'long_tail_integration':
          modifiedContent = this.integrateLongTail(modifiedContent, keyword, strategy);
          addedCount++;
          break;

        default:
          // Standard addition
          modifiedContent = this.addStandardInstance(modifiedContent, keyword, sentences, i);
          addedCount++;
          break;
      }
    }

    return modifiedContent;
  }

  // Add to strategic locations (headings, first/last paragraphs)
  addToStrategicLocations(content, keyword, strategy) {
    const variations = strategy.variations || [keyword];
    const variation = variations[Math.floor(Math.random() * variations.length)];

    // Try to add to subheadings first
    const h3Regex = /^### (.+)$/gm;
    const h3Matches = content.match(h3Regex);
    
    if (h3Matches && h3Matches.length > 0) {
      const randomH3 = h3Matches[Math.floor(Math.random() * h3Matches.length)];
      if (!randomH3.toLowerCase().includes(keyword.toLowerCase())) {
        const newH3 = randomH3.replace(/^### /, `### ${variation} - `);
        return content.replace(randomH3, newH3);
      }
    }

    // Add to first paragraph if not present
    const paragraphs = content.split(/\n\s*\n/);
    if (paragraphs.length > 0 && !paragraphs[0].toLowerCase().includes(keyword.toLowerCase())) {
      const firstParagraph = paragraphs[0];
      const sentences = firstParagraph.split(/[.!?]+/);
      if (sentences.length > 1) {
        sentences[1] = ` This ${variation} approach` + sentences[1];
        paragraphs[0] = sentences.join('.');
        return paragraphs.join('\n\n');
      }
    }

    return content;
  }

  // Integrate naturally throughout content
  integrateNaturally(content, keyword, strategy) {
    const contextualPhrases = strategy.contextualPhrases || [`effective ${keyword}`, `${keyword} approach`];
    const phrase = contextualPhrases[Math.floor(Math.random() * contextualPhrases.length)];

    // Find a suitable sentence to enhance
    const sentences = content.split(/[.!?]+/);
    for (let i = 1; i < sentences.length - 1; i++) {
      const sentence = sentences[i].trim();
      if (sentence.length > 20 && !sentence.toLowerCase().includes(keyword.toLowerCase())) {
        // Add phrase to beginning or end of sentence
        if (Math.random() > 0.5) {
          sentences[i] = ` ${phrase} ensures that` + sentence;
        } else {
          sentences[i] = sentence + `, which is essential for ${phrase}`;
        }
        return sentences.join('.');
      }
    }

    return content;
  }

  // Integrate semantically
  integrateSemanticallly(content, keyword, strategy) {
    const semanticPhrases = [
      `understanding ${keyword}`,
      `implementing ${keyword}`,
      `${keyword} methodology`,
      `${keyword} principles`
    ];
    
    const phrase = semanticPhrases[Math.floor(Math.random() * semanticPhrases.length)];
    
    // Add to a paragraph that discusses concepts or methods
    const paragraphs = content.split(/\n\s*\n/);
    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i];
      if (paragraph.toLowerCase().includes('important') || 
          paragraph.toLowerCase().includes('essential') ||
          paragraph.toLowerCase().includes('method')) {
        if (!paragraph.toLowerCase().includes(keyword.toLowerCase())) {
          paragraphs[i] = paragraph + ` Additionally, ${phrase} plays a crucial role in achieving optimal results.`;
          return paragraphs.join('\n\n');
        }
      }
    }

    return content;
  }

  // Add authority mentions
  addAuthorityMentions(content, keyword, strategy) {
    const authorityPhrases = [
      `According to ${keyword} experts`,
      `${keyword} professionals recommend`,
      `Industry leaders in ${keyword}`,
      `${keyword} authorities suggest`
    ];
    
    const phrase = authorityPhrases[Math.floor(Math.random() * authorityPhrases.length)];
    
    // Add to a paragraph that could benefit from authority
    const paragraphs = content.split(/\n\s*\n/);
    for (let i = 1; i < paragraphs.length - 1; i++) {
      const paragraph = paragraphs[i];
      if (paragraph.length > 100 && !paragraph.toLowerCase().includes(keyword.toLowerCase())) {
        const sentences = paragraph.split(/[.!?]+/);
        if (sentences.length > 1) {
          sentences.splice(1, 0, ` ${phrase} that this approach is highly effective`);
          paragraphs[i] = sentences.join('.');
          return paragraphs.join('\n\n');
        }
      }
    }

    return content;
  }

  // Integrate long-tail variations
  integrateLongTail(content, keyword, strategy) {
    const longTailPhrases = [
      `best ${keyword}`,
      `top ${keyword}`,
      `ultimate ${keyword}`,
      `comprehensive ${keyword}`
    ];
    
    const phrase = longTailPhrases[Math.floor(Math.random() * longTailPhrases.length)];
    
    // Add to sections that discuss benefits or recommendations
    const sentences = content.split(/[.!?]+/);
    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i].trim();
      if ((sentence.toLowerCase().includes('benefit') || 
           sentence.toLowerCase().includes('advantage') ||
           sentence.toLowerCase().includes('recommend')) &&
          !sentence.toLowerCase().includes(keyword.toLowerCase())) {
        sentences[i] = sentence.replace(/\b(the|a|an)\b/i, `the ${phrase}`);
        return sentences.join('.');
      }
    }

    return content;
  }

  // Add standard instance
  addStandardInstance(content, keyword, sentences, index) {
    if (sentences.length > index + 2) {
      const targetSentence = sentences[index + 2].trim();
      if (targetSentence.length > 20 && !targetSentence.toLowerCase().includes(keyword.toLowerCase())) {
        sentences[index + 2] = targetSentence.replace(/\b(solution|approach|method)\b/i, keyword);
        return sentences.join('.');
      }
    }
    return content;
  }

  // Remove keyword instances strategically
  async removeKeywordInstances(content, keyword, count, strategy, type) {
    let modifiedContent = content;
    let removedCount = 0;

    const keywordRegex = new RegExp(`\\b${keyword}\\b`, 'gi');
    const matches = [...content.matchAll(keywordRegex)];

    // Sort matches by priority (keep important ones)
    const prioritizedMatches = this.prioritizeMatchesForRemoval(matches, content, keyword, strategy);

    for (let i = 0; i < Math.min(count, prioritizedMatches.length); i++) {
      const match = prioritizedMatches[i];
      
      switch (strategy.method) {
        case 'synonym_replacement':
          modifiedContent = this.replaceWithSynonym(modifiedContent, keyword, strategy.replacements, match);
          break;

        case 'pronoun_replacement':
          modifiedContent = this.replaceWithPronoun(modifiedContent, keyword, strategy.replacements, match);
          break;

        case 'selective_removal':
          modifiedContent = this.selectivelyRemove(modifiedContent, keyword, match);
          break;

        case 'consolidation':
          modifiedContent = this.consolidateMentions(modifiedContent, keyword, match);
          break;

        case 'alternative_phrasing':
          modifiedContent = this.useAlternativePhrasing(modifiedContent, keyword, strategy.replacements, match);
          break;

        default:
          modifiedContent = this.simpleRemoval(modifiedContent, keyword, match);
          break;
      }
      
      removedCount++;
    }

    return modifiedContent;
  }

  // Prioritize matches for removal (remove least important first)
  prioritizeMatchesForRemoval(matches, content, keyword, strategy) {
    return matches.map(match => {
      const context = this.getMatchContext(content, match);
      let priority = 0;

      // Lower priority (remove first) if:
      // - In middle of long paragraphs
      // - Not in headings
      // - Not in first/last paragraphs
      // - Redundant usage

      if (context.isInHeading) priority += 10;
      if (context.isInFirstParagraph) priority += 8;
      if (context.isInLastParagraph) priority += 6;
      if (context.isInListItem) priority += 4;
      if (context.hasStrongContext) priority += 3;

      return { match, priority, context };
    }).sort((a, b) => a.priority - b.priority); // Sort by ascending priority (remove lowest first)
  }

  // Get context information for a match
  getMatchContext(content, match) {
    const position = match.index;
    const beforeText = content.substring(Math.max(0, position - 100), position);
    const afterText = content.substring(position, Math.min(content.length, position + 100));
    
    return {
      isInHeading: /#{1,6}\s/.test(beforeText) || beforeText.includes('<h'),
      isInFirstParagraph: position < content.length * 0.1,
      isInLastParagraph: position > content.length * 0.9,
      isInListItem: beforeText.includes('- ') || beforeText.includes('* '),
      hasStrongContext: /important|essential|key|crucial|vital/.test(beforeText + afterText),
      surroundingText: beforeText + afterText
    };
  }

  // Replace with synonym
  replaceWithSynonym(content, keyword, replacements, match) {
    if (match.context.isInHeading && replacements.keepInHeadings) {
      return content; // Don't replace in headings
    }
    
    const replacement = replacements[Math.floor(Math.random() * replacements.length)];
    const start = match.match.index;
    const end = start + match.match[0].length;
    
    return content.substring(0, start) + replacement + content.substring(end);
  }

  // Replace with pronoun
  replaceWithPronoun(content, keyword, replacements, match) {
    const replacement = replacements[Math.floor(Math.random() * replacements.length)];
    const start = match.match.index;
    const end = start + match.match[0].length;
    
    return content.substring(0, start) + replacement + content.substring(end);
  }

  // Selective removal based on context
  selectivelyRemove(content, keyword, match) {
    // Only remove if not in important context
    if (match.context.isInHeading || match.context.hasStrongContext) {
      return content;
    }
    
    // Remove the entire phrase containing the keyword
    const sentences = content.split(/[.!?]+/);
    for (let i = 0; i < sentences.length; i++) {
      if (sentences[i].includes(match.match[0])) {
        // Simplify the sentence by removing redundant phrases
        sentences[i] = sentences[i].replace(new RegExp(`\\b${keyword}\\s+(approach|method|solution)\\b`, 'gi'), 'approach');
        break;
      }
    }
    
    return sentences.join('.');
  }

  // Consolidate mentions
  consolidateMentions(content, keyword, match) {
    // Combine multiple weak mentions into one strong mention
    const sentences = content.split(/[.!?]+/);
    let consolidated = false;
    
    for (let i = 0; i < sentences.length - 1; i++) {
      if (sentences[i].toLowerCase().includes(keyword.toLowerCase()) && 
          sentences[i + 1].toLowerCase().includes(keyword.toLowerCase())) {
        // Merge the sentences and remove one keyword mention
        const mergedSentence = sentences[i] + ', which, combined with ' + 
                              sentences[i + 1].replace(new RegExp(`\\b${keyword}\\b`, 'i'), 'these');
        sentences[i] = mergedSentence;
        sentences.splice(i + 1, 1);
        consolidated = true;
        break;
      }
    }
    
    return consolidated ? sentences.join('.') : content;
  }

  // Use alternative phrasing
  useAlternativePhrasing(content, keyword, replacements, match) {
    const replacement = replacements[Math.floor(Math.random() * replacements.length)];
    return content.replace(match.match[0], replacement);
  }

  // Simple removal
  simpleRemoval(content, keyword, match) {
    const start = match.match.index;
    const end = start + match.match[0].length;
    
    // Replace with appropriate article or remove entirely
    const beforeChar = content.charAt(start - 1);
    const afterChar = content.charAt(end);
    
    let replacement = '';
    if (beforeChar === ' ' && afterChar === ' ') {
      replacement = ' '; // Keep single space
    } else if (beforeChar.match(/[aA]/) && afterChar === ' ') {
      replacement = 'the '; // Replace "a keyword" with "the"
    }
    
    return content.substring(0, start) + replacement + content.substring(end);
  }

  // Improve content flow after modifications
  improveContentFlow(content) {
    // Fix double spaces
    content = content.replace(/\s+/g, ' ');
    
    // Fix punctuation issues
    content = content.replace(/\s+([.!?])/g, '$1');
    content = content.replace(/([.!?])\s*([a-z])/g, '$1 $2');
    
    // Ensure proper sentence capitalization
    content = content.replace(/([.!?]\s+)([a-z])/g, (match, punct, letter) => punct + letter.toUpperCase());
    
    // Fix paragraph spacing
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    return content.trim();
  }

  // Validate optimization results
  validateOptimization(content, keywordTargets) {
    const validation = {};
    const currentAnalysis = this.analyzeCurrentDensities(content, keywordTargets);
    
    keywordTargets.forEach(target => {
      const analysis = currentAnalysis[target.keyword];
      if (analysis) {
        validation[target.keyword] = {
          ...analysis,
          optimizationSuccess: !analysis.needsOptimization,
          accuracyScore: this.calculateAccuracyScore(analysis.currentDensity, target.targetDensity, target.tolerance)
        };
      }
    });

    return validation;
  }

  // Perform fine-tuning
  async performFineTuning(content, validationResults, keywordTargets) {
    let fineTunedContent = content;
    const needsFineTuning = Object.values(validationResults).some(result => !result.optimizationSuccess);
    
    if (!needsFineTuning) {
      console.log('No fine-tuning needed - all targets achieved');
      return fineTunedContent;
    }

    // Perform micro-adjustments for targets that are close but not perfect
    for (const [keyword, result] of Object.entries(validationResults)) {
      if (!result.optimizationSuccess && Math.abs(result.difference) <= 0.1) {
        // Very close - make micro-adjustment
        const target = keywordTargets.find(t => t.keyword === keyword);
        if (target) {
          fineTunedContent = await this.makeMicroAdjustment(fineTunedContent, keyword, result, target);
        }
      }
    }

    return fineTunedContent;
  }

  // Make micro-adjustment for fine-tuning
  async makeMicroAdjustment(content, keyword, result, target) {
    const difference = result.targetDensity - result.currentDensity;
    
    if (Math.abs(difference) <= 0.05) {
      // Very minor adjustment needed
      if (difference > 0) {
        // Add one instance
        return this.addSingleInstance(content, keyword, target.type);
      } else {
        // Remove one instance
        return this.removeSingleInstance(content, keyword, target.type);
      }
    }
    
    return content;
  }

  // Add single instance for micro-adjustment
  addSingleInstance(content, keyword, type) {
    const sentences = content.split(/[.!?]+/);
    
    // Find a sentence where we can naturally add the keyword
    for (let i = Math.floor(sentences.length / 2); i < sentences.length - 1; i++) {
      const sentence = sentences[i].trim();
      if (sentence.length > 30 && !sentence.toLowerCase().includes(keyword.toLowerCase())) {
        // Add keyword naturally
        sentences[i] = sentence.replace(/\b(this|the|such)\b/i, `this ${keyword}`);
        return sentences.join('.');
      }
    }
    
    return content;
  }

  // Remove single instance for micro-adjustment
  removeSingleInstance(content, keyword, type) {
    const regex = new RegExp(`\\b${keyword}\\b`, 'i');
    const match = content.match(regex);
    
    if (match) {
      const start = match.index;
      const end = start + match[0].length;
      
      // Replace with appropriate alternative
      const alternatives = ['this', 'the approach', 'such methods', 'these solutions'];
      const replacement = alternatives[Math.floor(Math.random() * alternatives.length)];
      
      return content.substring(0, start) + replacement + content.substring(end);
    }
    
    return content;
  }

  // Calculate improvements
  calculateImprovements(beforeAnalysis, afterContent, keywordTargets) {
    const afterAnalysis = this.analyzeCurrentDensities(afterContent, keywordTargets);
    const improvements = {};

    Object.keys(beforeAnalysis).forEach(keyword => {
      const before = beforeAnalysis[keyword];
      const after = afterAnalysis[keyword];
      
      if (before && after) {
        improvements[keyword] = {
          densityImprovement: parseFloat((after.currentDensity - before.currentDensity).toFixed(3)),
          accuracyImprovement: this.calculateAccuracyScore(after.currentDensity, after.targetDensity, 0.1) - 
                              this.calculateAccuracyScore(before.currentDensity, before.targetDensity, 0.1),
          statusChange: `${before.status} → ${after.status}`,
          optimized: !after.needsOptimization
        };
      }
    });

    return improvements;
  }

  // Calculate optimization accuracy
  calculateOptimizationAccuracy(content, keywordTargets) {
    const currentAnalysis = this.analyzeCurrentDensities(content, keywordTargets);
    let totalAccuracy = 0;
    let targetCount = 0;

    keywordTargets.forEach(target => {
      const analysis = currentAnalysis[target.keyword];
      if (analysis) {
        const accuracy = this.calculateAccuracyScore(analysis.currentDensity, target.targetDensity, target.tolerance);
        totalAccuracy += accuracy;
        targetCount++;
      }
    });

    return targetCount > 0 ? parseFloat((totalAccuracy / targetCount).toFixed(2)) : 0;
  }

  // Helper methods
  getDensityStatus(current, target, tolerance) {
    const difference = Math.abs(current - target);
    
    if (difference <= tolerance) return 'optimal';
    if (difference <= tolerance * 2) return 'acceptable';
    if (current < target) return 'too_low';
    return 'too_high';
  }

  calculateAccuracyScore(current, target, tolerance) {
    const difference = Math.abs(current - target);
    const maxAcceptableDifference = tolerance * 2;
    
    if (difference <= tolerance) return 100;
    if (difference <= maxAcceptableDifference) return 100 - ((difference - tolerance) / tolerance) * 50;
    return Math.max(0, 50 - ((difference - maxAcceptableDifference) / target) * 50);
  }
}

module.exports = MultiKeywordOptimizer;