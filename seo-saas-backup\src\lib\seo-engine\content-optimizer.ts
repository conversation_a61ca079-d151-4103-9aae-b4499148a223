// Advanced Content Optimization Algorithms
// Enterprise-grade content optimization with competitor benchmarking

interface OptimizationTarget {
  wordCount: number;
  keywordDensity: number;
  headingCount: number;
  readabilityScore: number;
  semanticScore: number;
}

interface OptimizationResult {
  score: number;
  improvements: ContentImprovement[];
  optimizedContent: string;
  metadata: OptimizedMetadata;
  performance: PerformancePrediction;
}

interface ContentImprovement {
  type: 'keyword' | 'structure' | 'readability' | 'semantic' | 'technical';
  priority: 'critical' | 'high' | 'medium' | 'low';
  description: string;
  before: string;
  after: string;
  impact: number; // 1-10 scale
  effort: number; // 1-10 scale
  roi: number; // impact/effort ratio
}

interface OptimizedMetadata {
  title: string;
  description: string;
  keywords: string[];
  schema: any;
  openGraph: Record<string, string>;
}

interface PerformancePrediction {
  rankingPotential: number; // 1-100
  clickThroughRate: number; // 0-1
  engagementScore: number; // 1-100
  conversionPotential: number; // 1-100
}

interface CompetitorBenchmark {
  averageWordCount: number;
  averageKeywordDensity: number;
  averageHeadingCount: number;
  averageReadabilityScore: number;
  topPerformingElements: string[];
}

export class ContentOptimizer {
  private primaryKeyword: string;
  private secondaryKeywords: string[];
  private lsiKeywords: string[];
  private industry: string;
  private contentType: string;
  private targetAudience: string;

  constructor(
    primaryKeyword: string,
    secondaryKeywords: string[] = [],
    lsiKeywords: string[] = [],
    industry: string = 'general',
    contentType: string = 'general',
    targetAudience: string = 'general'
  ) {
    this.primaryKeyword = primaryKeyword.toLowerCase();
    this.secondaryKeywords = secondaryKeywords.map(k => k.toLowerCase());
    this.lsiKeywords = lsiKeywords.map(k => k.toLowerCase());
    this.industry = industry.toLowerCase();
    this.contentType = contentType.toLowerCase();
    this.targetAudience = targetAudience.toLowerCase();
  }

  /**
   * Optimize content based on competitor analysis and SEO best practices
   */
  async optimizeContent(
    content: string,
    competitorBenchmark?: CompetitorBenchmark
  ): Promise<OptimizationResult> {
    // Calculate optimization targets
    const targets = this.calculateOptimizationTargets(competitorBenchmark);
    
    // Analyze current content
    const currentAnalysis = this.analyzeCurrentContent(content);
    
    // Generate improvements
    const improvements = this.generateImprovements(content, currentAnalysis, targets);
    
    // Apply optimizations
    const optimizedContent = this.applyOptimizations(content, improvements);
    
    // Generate optimized metadata
    const metadata = this.generateOptimizedMetadata(optimizedContent);
    
    // Predict performance
    const performance = this.predictPerformance(optimizedContent, targets);
    
    // Calculate overall optimization score
    const score = this.calculateOptimizationScore(optimizedContent, targets);

    return {
      score,
      improvements,
      optimizedContent,
      metadata,
      performance
    };
  }

  /**
   * Calculate optimization targets based on competitor analysis
   */
  private calculateOptimizationTargets(benchmark?: CompetitorBenchmark): OptimizationTarget {
    const baseTargets = {
      wordCount: 1500,
      keywordDensity: 1.5,
      headingCount: 8,
      readabilityScore: 70,
      semanticScore: 80
    };

    if (!benchmark) return baseTargets;

    // Adjust targets based on competitor analysis
    return {
      wordCount: Math.max(baseTargets.wordCount, benchmark.averageWordCount * 1.1),
      keywordDensity: Math.min(3.0, benchmark.averageKeywordDensity * 1.05),
      headingCount: Math.max(baseTargets.headingCount, benchmark.averageHeadingCount),
      readabilityScore: Math.max(baseTargets.readabilityScore, benchmark.averageReadabilityScore),
      semanticScore: baseTargets.semanticScore
    };
  }

  /**
   * Analyze current content performance
   */
  private analyzeCurrentContent(content: string): any {
    const words = this.extractWords(content);
    const headings = this.extractHeadings(content);
    const sentences = this.extractSentences(content);

    return {
      wordCount: words.length,
      keywordDensity: this.calculateKeywordDensity(words),
      headingCount: headings.length,
      readabilityScore: this.calculateReadabilityScore(sentences, words),
      semanticScore: this.calculateSemanticScore(content),
      keywordDistribution: this.analyzeKeywordDistribution(content),
      structureScore: this.analyzeContentStructure(content)
    };
  }

  /**
   * Generate content improvements
   */
  private generateImprovements(
    content: string,
    analysis: any,
    targets: OptimizationTarget
  ): ContentImprovement[] {
    const improvements: ContentImprovement[] = [];

    // Word count optimization
    if (analysis.wordCount < targets.wordCount * 0.8) {
      improvements.push({
        type: 'structure',
        priority: 'high',
        description: `Increase content length to ${targets.wordCount} words for better SEO performance`,
        before: `Current: ${analysis.wordCount} words`,
        after: `Target: ${targets.wordCount} words`,
        impact: 8,
        effort: 6,
        roi: 8/6
      });
    }

    // Keyword density optimization
    if (analysis.keywordDensity < targets.keywordDensity * 0.7) {
      improvements.push({
        type: 'keyword',
        priority: 'critical',
        description: `Increase primary keyword density to ${targets.keywordDensity}%`,
        before: `Current: ${analysis.keywordDensity.toFixed(2)}%`,
        after: `Target: ${targets.keywordDensity.toFixed(2)}%`,
        impact: 9,
        effort: 4,
        roi: 9/4
      });
    } else if (analysis.keywordDensity > targets.keywordDensity * 1.5) {
      improvements.push({
        type: 'keyword',
        priority: 'medium',
        description: 'Reduce keyword density to avoid over-optimization',
        before: `Current: ${analysis.keywordDensity.toFixed(2)}%`,
        after: `Target: ${targets.keywordDensity.toFixed(2)}%`,
        impact: 6,
        effort: 3,
        roi: 6/3
      });
    }

    // Heading structure optimization
    if (analysis.headingCount < targets.headingCount * 0.7) {
      improvements.push({
        type: 'structure',
        priority: 'high',
        description: `Add more headings for better content structure (target: ${targets.headingCount})`,
        before: `Current: ${analysis.headingCount} headings`,
        after: `Target: ${targets.headingCount} headings`,
        impact: 7,
        effort: 5,
        roi: 7/5
      });
    }

    // Readability optimization
    if (analysis.readabilityScore < targets.readabilityScore) {
      improvements.push({
        type: 'readability',
        priority: 'medium',
        description: 'Improve readability by simplifying sentences and using shorter paragraphs',
        before: `Current score: ${analysis.readabilityScore}`,
        after: `Target score: ${targets.readabilityScore}`,
        impact: 6,
        effort: 4,
        roi: 6/4
      });
    }

    // Semantic optimization
    if (analysis.semanticScore < targets.semanticScore) {
      improvements.push({
        type: 'semantic',
        priority: 'high',
        description: 'Add more LSI keywords and related terms for better semantic relevance',
        before: `Current score: ${analysis.semanticScore}`,
        after: `Target score: ${targets.semanticScore}`,
        impact: 8,
        effort: 5,
        roi: 8/5
      });
    }

    // LSI keyword integration
    const missingLSI = this.findMissingLSIKeywords(content);
    if (missingLSI.length > 0) {
      improvements.push({
        type: 'semantic',
        priority: 'medium',
        description: `Integrate ${missingLSI.length} missing LSI keywords: ${missingLSI.slice(0, 3).join(', ')}`,
        before: 'Missing important related keywords',
        after: 'LSI keywords naturally integrated',
        impact: 7,
        effort: 4,
        roi: 7/4
      });
    }

    // Technical SEO improvements
    const technicalIssues = this.identifyTechnicalIssues(content);
    for (const issue of technicalIssues) {
      improvements.push({
        type: 'technical',
        priority: issue.priority,
        description: issue.description,
        before: issue.current,
        after: issue.recommended,
        impact: issue.impact,
        effort: issue.effort,
        roi: issue.impact / issue.effort
      });
    }

    return improvements.sort((a, b) => b.roi - a.roi);
  }

  /**
   * Apply optimizations to content
   */
  private applyOptimizations(content: string, improvements: ContentImprovement[]): string {
    let optimizedContent = content;

    for (const improvement of improvements) {
      switch (improvement.type) {
        case 'keyword':
          optimizedContent = this.optimizeKeywordUsage(optimizedContent);
          break;
        case 'structure':
          optimizedContent = this.optimizeContentStructure(optimizedContent);
          break;
        case 'readability':
          optimizedContent = this.optimizeReadability(optimizedContent);
          break;
        case 'semantic':
          optimizedContent = this.optimizeSemanticContent(optimizedContent);
          break;
        case 'technical':
          optimizedContent = this.optimizeTechnicalElements(optimizedContent);
          break;
      }
    }

    return optimizedContent;
  }

  /**
   * Generate optimized metadata
   */
  private generateOptimizedMetadata(content: string): OptimizedMetadata {
    const title = this.generateOptimizedTitle();
    const description = this.generateOptimizedDescription(content);
    const keywords = [this.primaryKeyword, ...this.secondaryKeywords, ...this.lsiKeywords.slice(0, 8)];
    const schema = this.generateSchemaMarkup(content);
    const openGraph = this.generateOpenGraphTags(title, description);

    return {
      title,
      description,
      keywords,
      schema,
      openGraph
    };
  }

  /**
   * Predict content performance
   */
  private predictPerformance(content: string, targets: OptimizationTarget): PerformancePrediction {
    const analysis = this.analyzeCurrentContent(content);
    
    // Calculate ranking potential based on optimization score
    const optimizationScore = this.calculateOptimizationScore(content, targets);
    const rankingPotential = Math.min(100, optimizationScore * 1.2);
    
    // Predict CTR based on title and description quality
    const clickThroughRate = this.predictClickThroughRate(content);
    
    // Calculate engagement score based on readability and structure
    const engagementScore = (analysis.readabilityScore + analysis.structureScore) / 2;
    
    // Predict conversion potential based on content quality and CTA presence
    const conversionPotential = this.predictConversionPotential(content);

    return {
      rankingPotential,
      clickThroughRate,
      engagementScore,
      conversionPotential
    };
  }

  // Helper methods
  private extractWords(content: string): string[] {
    return content.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
  }

  private extractHeadings(content: string): string[] {
    const headingRegex = /<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi;
    const headings: string[] = [];
    let match;
    
    while ((match = headingRegex.exec(content)) !== null) {
      headings.push(match[1].replace(/<[^>]*>/g, '').trim());
    }
    
    return headings;
  }

  private extractSentences(content: string): string[] {
    return content.split(/[.!?]+/).filter(s => s.trim().length > 10);
  }

  private calculateKeywordDensity(words: string[]): number {
    const keywordCount = words.filter(word => 
      word.includes(this.primaryKeyword) || 
      this.primaryKeyword.includes(word)
    ).length;
    
    return words.length > 0 ? (keywordCount / words.length) * 100 : 0;
  }

  private calculateReadabilityScore(sentences: string[], words: string[]): number {
    if (sentences.length === 0 || words.length === 0) return 0;
    
    const avgSentenceLength = words.length / sentences.length;
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
    
    // Simplified Flesch Reading Ease calculation
    const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * (avgWordLength / 4.7));
    return Math.max(0, Math.min(100, score));
  }

  private calculateSemanticScore(content: string): number {
    const lowerContent = content.toLowerCase();
    let score = 0;
    
    // Check for LSI keywords
    const lsiFound = this.lsiKeywords.filter(keyword => 
      lowerContent.includes(keyword)
    ).length;
    
    score += (lsiFound / Math.max(this.lsiKeywords.length, 1)) * 50;
    
    // Check for secondary keywords
    const secondaryFound = this.secondaryKeywords.filter(keyword => 
      lowerContent.includes(keyword)
    ).length;
    
    score += (secondaryFound / Math.max(this.secondaryKeywords.length, 1)) * 30;
    
    // Check for semantic variety
    const uniqueWords = new Set(this.extractWords(content));
    score += Math.min(20, uniqueWords.size / 50);
    
    return Math.min(100, score);
  }

  private analyzeKeywordDistribution(content: string): any {
    // Analyze how keywords are distributed throughout the content
    const sections = content.split(/\n\s*\n/);
    const distribution = sections.map(section => {
      const words = this.extractWords(section);
      return this.calculateKeywordDensity(words);
    });
    
    return {
      sections: distribution.length,
      average: distribution.reduce((sum, d) => sum + d, 0) / distribution.length,
      variance: this.calculateVariance(distribution)
    };
  }

  private analyzeContentStructure(content: string): number {
    let score = 100;
    
    // Check for proper heading hierarchy
    const headings = this.extractHeadings(content);
    if (headings.length < 3) score -= 20;
    
    // Check for paragraph length
    const paragraphs = content.split(/\n\s*\n/);
    const longParagraphs = paragraphs.filter(p => p.split(' ').length > 150);
    score -= longParagraphs.length * 5;
    
    // Check for list usage
    const hasLists = /<[uo]l>/i.test(content);
    if (!hasLists) score -= 10;
    
    return Math.max(0, score);
  }

  private findMissingLSIKeywords(content: string): string[] {
    const lowerContent = content.toLowerCase();
    return this.lsiKeywords.filter(keyword => 
      !lowerContent.includes(keyword)
    );
  }

  private identifyTechnicalIssues(content: string): any[] {
    const issues: any[] = [];
    
    // Check for missing alt text
    const images = content.match(/<img[^>]*>/gi) || [];
    const imagesWithoutAlt = images.filter(img => !img.includes('alt='));
    
    if (imagesWithoutAlt.length > 0) {
      issues.push({
        priority: 'medium',
        description: 'Add alt text to images for better accessibility and SEO',
        current: `${imagesWithoutAlt.length} images without alt text`,
        recommended: 'All images should have descriptive alt text',
        impact: 6,
        effort: 3
      });
    }
    
    // Check for internal links
    const internalLinks = content.match(/<a[^>]*href="[^"]*"[^>]*>/gi) || [];
    if (internalLinks.length < 3) {
      issues.push({
        priority: 'medium',
        description: 'Add more internal links to improve site structure',
        current: `${internalLinks.length} internal links`,
        recommended: 'At least 3-5 relevant internal links',
        impact: 5,
        effort: 4
      });
    }
    
    return issues;
  }

  private optimizeKeywordUsage(content: string): string {
    // Add primary keyword to first paragraph if missing
    const paragraphs = content.split(/\n\s*\n/);
    if (paragraphs.length > 0 && !paragraphs[0].toLowerCase().includes(this.primaryKeyword)) {
      paragraphs[0] = `${this.primaryKeyword} is essential for ${paragraphs[0]}`;
    }
    
    return paragraphs.join('\n\n');
  }

  private optimizeContentStructure(content: string): string {
    // Add subheadings if content is too long without them
    const paragraphs = content.split(/\n\s*\n/);
    const optimized: string[] = [];
    
    for (let i = 0; i < paragraphs.length; i++) {
      if (i > 0 && i % 3 === 0 && !paragraphs[i].startsWith('<h')) {
        optimized.push(`<h3>Key Points About ${this.primaryKeyword}</h3>`);
      }
      optimized.push(paragraphs[i]);
    }
    
    return optimized.join('\n\n');
  }

  private optimizeReadability(content: string): string {
    // Break up long sentences and paragraphs
    return content
      .replace(/([.!?])\s+([A-Z])/g, '$1\n\n$2') // Add paragraph breaks
      .replace(/,\s+and\s+/g, ', and\n') // Break long lists
      .replace(/\s+/g, ' ') // Clean up whitespace
      .trim();
  }

  private optimizeSemanticContent(content: string): string {
    // Add LSI keywords naturally
    const missingLSI = this.findMissingLSIKeywords(content);
    let optimized = content;
    
    for (const keyword of missingLSI.slice(0, 3)) {
      const insertion = `This approach to ${keyword} ensures better results.`;
      optimized += `\n\n${insertion}`;
    }
    
    return optimized;
  }

  private optimizeTechnicalElements(content: string): string {
    // Add schema markup and improve technical elements
    return content.replace(
      /<img([^>]*?)>/gi,
      (match, attrs) => {
        if (!attrs.includes('alt=')) {
          return `<img${attrs} alt="${this.primaryKeyword} related image">`;
        }
        return match;
      }
    );
  }

  private generateOptimizedTitle(): string {
    return `${this.primaryKeyword} - Professional ${this.industry} Solutions | Expert Guide`;
  }

  private generateOptimizedDescription(content: string): string {
    const firstSentence = content.split('.')[0];
    return `${firstSentence}. Learn about ${this.primaryKeyword} with our comprehensive guide. ${this.secondaryKeywords.slice(0, 2).join(', ')} and more.`;
  }

  private generateSchemaMarkup(content: string): any {
    return {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": this.generateOptimizedTitle(),
      "description": this.generateOptimizedDescription(content),
      "keywords": [this.primaryKeyword, ...this.secondaryKeywords].join(', ')
    };
  }

  private generateOpenGraphTags(title: string, description: string): Record<string, string> {
    return {
      'og:title': title,
      'og:description': description,
      'og:type': 'article',
      'og:locale': 'en_US'
    };
  }

  private calculateOptimizationScore(content: string, targets: OptimizationTarget): number {
    const analysis = this.analyzeCurrentContent(content);
    
    let score = 0;
    let maxScore = 0;
    
    // Word count score (20 points)
    maxScore += 20;
    const wordCountRatio = analysis.wordCount / targets.wordCount;
    score += Math.min(20, wordCountRatio * 20);
    
    // Keyword density score (25 points)
    maxScore += 25;
    const densityRatio = Math.min(1, analysis.keywordDensity / targets.keywordDensity);
    score += densityRatio * 25;
    
    // Heading score (20 points)
    maxScore += 20;
    const headingRatio = Math.min(1, analysis.headingCount / targets.headingCount);
    score += headingRatio * 20;
    
    // Readability score (20 points)
    maxScore += 20;
    score += (analysis.readabilityScore / 100) * 20;
    
    // Semantic score (15 points)
    maxScore += 15;
    score += (analysis.semanticScore / 100) * 15;
    
    return Math.round((score / maxScore) * 100);
  }

  private predictClickThroughRate(content: string): number {
    // Simplified CTR prediction based on title and description quality
    const title = this.generateOptimizedTitle();
    const hasNumbers = /\d/.test(title);
    const hasActionWords = /(guide|tips|how|best|top|ultimate)/i.test(title);
    
    let ctr = 0.02; // Base CTR
    if (hasNumbers) ctr += 0.01;
    if (hasActionWords) ctr += 0.015;
    if (title.length >= 50 && title.length <= 60) ctr += 0.005;
    
    return Math.min(0.1, ctr);
  }

  private predictConversionPotential(content: string): number {
    let score = 50; // Base score
    
    // Check for CTAs
    const hasCTA = /(contact|call|click|buy|get|start|try|download)/i.test(content);
    if (hasCTA) score += 20;
    
    // Check for trust signals
    const hasTrustSignals = /(certified|licensed|experienced|professional|guarantee)/i.test(content);
    if (hasTrustSignals) score += 15;
    
    // Check for social proof
    const hasSocialProof = /(review|testimonial|client|customer|rating)/i.test(content);
    if (hasSocialProof) score += 15;
    
    return Math.min(100, score);
  }

  private calculateVariance(numbers: number[]): number {
    const mean = numbers.reduce((sum, n) => sum + n, 0) / numbers.length;
    const squaredDiffs = numbers.map(n => Math.pow(n - mean, 2));
    return squaredDiffs.reduce((sum, sq) => sum + sq, 0) / numbers.length;
  }
}

export const contentOptimizer = new ContentOptimizer('', [], [], 'general', 'general', 'general');

// Export types for use in other modules
export type {
  OptimizationTarget,
  OptimizationResult,
  ContentImprovement,
  OptimizedMetadata,
  PerformancePrediction,
  CompetitorBenchmark
};
