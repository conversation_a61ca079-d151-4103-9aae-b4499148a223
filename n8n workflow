{"nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 9 * * *"}]}}, "id": "64339dc0-1350-47a9-baa7-becc47864702", "name": "Daily News Trigger1", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-200, -20], "executeOnce": false}, {"parameters": {"promptType": "define", "text": "You are a senior digital marketing strategist working for God Digital Marketing.\n\n🎯 Your task is to analyze today's news and create engaging social media content.\n\n📋 Instructions:\n1. Use the newsapi tool to fetch latest news about digital marketing, AI, and business automation\n2. Analyze the news articles and extract key insights\n3. Create platform-specific content for LinkedIn, Twitter, Facebook, and Instagram\n4. Focus on actionable insights that would interest business owners and marketers\n\n🔧 Available Tools:\n- newsapi: Use this to fetch current news articles\n- Think: Use this to analyze and plan your content strategy\n\n📊 Output Format:\nReturn a JSON object with:\n{\n  \"insights\": [\"insight1\", \"insight2\", \"insight3\"],\n  \"linkedinPost\": \"Professional LinkedIn content\",\n  \"twitterPost\": \"Twitter content with hashtags\",\n  \"facebookPost\": \"Facebook content\",\n  \"instagramPost\": \"Instagram content with emojis\",\n  \"hashtags\": [\"#DigitalMarketing\", \"#AI\", \"#BusinessAutomation\"],\n  \"metadata\": {\n    \"generatedAt\": \"2025-01-XX\",\n    \"articlesProcessed\": 5\n  }\n}\n\n🚀 Start by fetching news with the newsapi tool using these parameters:\n- q: \"digital marketing OR AI OR business automation OR Google algorithm\"\n- sortBy: \"publishedAt\"\n- language: \"en\"\n- pageSize: 10", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [20, -20], "id": "451e8245-a227-4655-a67c-62632853c437", "name": "AI Agent1"}, {"parameters": {"model": "meta-llama/llama-4-scout-17b-16e-instruct", "options": {"temperature": 0.8}}, "id": "d60ad0c7-4d41-4dd6-8049-57f13cff9d78", "name": "Generate Marketing Content1", "type": "@n8n/n8n-nodes-langchain.lmChatGroq", "typeVersion": 1, "position": [-20, 180], "credentials": {"groqApi": {"id": "7mnRPxIiDsoggo51", "name": "Groq account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [320, 180], "id": "a795af4d-b5fe-4bd3-8d71-dd78382c20b2", "name": "Think1"}, {"parameters": {"assignments": {"assignments": [{"id": "2l3m4n5o-6p7q-8r9s-0t1u-v2w3x4y5z6a7", "name": "generatedAt", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "3m4n5o6p-7q8r-9s0t-1u2v-w3x4y5z6a7b8", "name": "sourceArticles", "value": "={{ $json.metadata?.articlesProcessed || 0 }}", "type": "number"}, {"id": "4n5o6p7q-8r9s-0t1u-2v3w-x4y5z6a7b8c9", "name": "insights", "value": "={{ $json.insights || [] }}", "type": "array"}, {"id": "5o6p7q8r-9s0t-1u2v-3w4x-y5z6a7b8c9d0", "name": "socialContent", "value": "={{ { linkedin: $json.linkedinPost, twitter: $json.twitterPost, facebook: $json.facebookPost, instagram: $json.instagramPost } }}", "type": "object"}, {"id": "6p7q8r9s-0t1u-2v3w-4x5y-z6a7b8c9d0e1", "name": "hashtags", "value": "={{ $json.hashtags || [] }}", "type": "array"}]}, "options": {}}, "id": "a6ae3c64-d083-49a3-9de3-7f1c222df2c9", "name": "Format Final Output1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [440, -20]}, {"parameters": {"jsCode": "// Generate platform-specific social media content from the AI-generated report\nconst aiOutput = $json;\n\n// Safely extract data with fallbacks\nconst insights = aiOutput.insights || ['Latest digital marketing trends', 'AI automation updates', 'Business growth strategies'];\nconst socialContent = aiOutput.socialContent || {};\nconst hashtags = aiOutput.hashtags || ['#DigitalMarketing', '#AI', '#BusinessAutomation'];\nconst currentDate = new Date().toLocaleDateString();\n\n// Create enhanced social media content\nconst enhancedContent = {\n  twitter: {\n    content: socialContent.twitter || `🚀 Today's Marketing Intelligence (${currentDate})\\n\\n${insights[0]?.substring(0, 120) || 'Key industry insights detected'}...\\n\\n${hashtags.slice(0, 4).join(' ')} #GodDigitalMarketing`,\n    hashtags: hashtags.slice(0, 4),\n    characterCount: 0\n  },\n  linkedin: {\n    content: socialContent.linkedin || `📊 Daily Marketing Intelligence Brief - ${currentDate}\\n\\n🎯 Today's Key Insights:\\n\\n${insights.map((insight, index) => `${index + 1}. ${insight}`).join('\\n\\n')}\\n\\n💡 At God Digital Marketing, we analyze industry trends daily to keep our clients ahead of the curve.\\n\\n${hashtags.slice(0, 6).join(' ')}`,\n    hashtags: hashtags.slice(0, 6),\n    characterCount: 0\n  },\n  facebook: {\n    content: socialContent.facebook || `🎯 Daily Marketing Intelligence Update\\n\\n📈 Today's top insights:\\n\\n${insights.slice(0, 3).map((insight, index) => `${index + 1}. ${insight}`).join('\\n\\n')}\\n\\n💼 Ready to transform your marketing strategy? Let's talk!\\n\\n${hashtags.slice(0, 5).join(' ')}`,\n    hashtags: hashtags.slice(0, 5),\n    characterCount: 0\n  },\n  instagram: {\n    content: socialContent.instagram || `📊 DAILY MARKETING INTEL 📊\\n\\n${currentDate}\\n\\n🎯 TOP INSIGHTS:\\n${insights.slice(0, 3).map((insight, index) => `\\n${index + 1}️⃣ ${insight.substring(0, 80)}...`).join('')}\\n\\n💡 Stay ahead with God Digital Marketing\\n\\n${hashtags.join(' ')}`,\n    hashtags: hashtags,\n    characterCount: 0\n  }\n};\n\n// Calculate character counts\nenhancedContent.twitter.characterCount = enhancedContent.twitter.content.length;\nenhancedContent.linkedin.characterCount = enhancedContent.linkedin.content.length;\nenhancedContent.facebook.characterCount = enhancedContent.facebook.content.length;\nenhancedContent.instagram.characterCount = enhancedContent.instagram.content.length;\n\nreturn [{\n  json: {\n    ...enhancedContent,\n    metadata: {\n      generatedAt: new Date().toISOString(),\n      sourceArticles: aiOutput.metadata?.articlesProcessed || 0,\n      totalInsights: insights.length,\n      executionId: `${$workflow.id}-${$execution.id}`\n    }\n  }\n}];"}, "id": "8b56e65b-265b-46b3-ba1e-a1712c18948d", "name": "Prepare Social Media Content1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, -20]}, {"parameters": {"postAs": "organization", "organization": "107882909", "text": "={{ $json.linkedin.content || 'Professional digital marketing content sharing valuable insights for business growth. Visit https://godigitalmarketing.com for more educational resources. #DigitalMarketing #BusinessGrowth #GODDigitalMarketing' }}", "shareMediaCategory": "=", "binaryPropertyName": "linkedin_image", "additionalFields": {}}, "type": "n8n-nodes-base.linkedIn", "typeVersion": 1, "position": [960, 0], "id": "ff44ab4b-a492-4e50-91f4-f5a31eb87fc0", "name": "LinkedIn with Image Link1", "credentials": {"linkedInOAuth2Api": {"id": "Gg3X9KirNLs7kfyM", "name": "LinkedIn account"}}, "notes": "LinkedIn post with clean, professional content - [FIXED: Binary data pipeline] [ENHANCED: Conditional image posting]"}, {"parameters": {"url": "https://newsapi.org/v2/everything", "sendQuery": true, "queryParameters": {"parameters": [{"name": "q", "value": "digital marketing OR AI OR business automation OR Google algorithm"}, {"name": "sortBy", "value": "publishedAt"}, {"name": "language", "value": "en"}, {"name": "pageSize", "value": "10"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "value": "05bde0076f924d83808fad7f5d7b219e"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [180, 220], "id": "566c85e9-0d3f-4da7-87c4-5ac4de7f7b19", "name": "newsapi"}], "connections": {"Daily News Trigger1": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "Format Final Output1", "type": "main", "index": 0}]]}, "Generate Marketing Content1": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}, "Think1": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}, "Format Final Output1": {"main": [[{"node": "Prepare Social Media Content1", "type": "main", "index": 0}]]}, "Prepare Social Media Content1": {"main": [[{"node": "LinkedIn with Image Link1", "type": "main", "index": 0}]]}, "newsapi": {"ai_tool": [[{"node": "AI Agent1", "type": "ai_tool", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "fa71618849152fdf81b026b7e79a6c24770db503a9228ddbbcab15c2a292ea40"}}