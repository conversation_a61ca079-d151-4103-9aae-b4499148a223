// Advanced Rate Limiting System
// Redis-based rate limiting with subscription-based limits

interface RateLimitRule {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (identifier: string) => string;
}

interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  totalHits: number;
  retryAfter?: number;
}

interface SubscriptionLimits {
  daily: number;
  monthly: number;
  concurrent: number;
  apiCalls: number;
}

// In-memory store for development (Redis would be used in production)
class MemoryStore {
  private store = new Map<string, { count: number; resetTime: number }>();
  
  async get(key: string): Promise<{ count: number; resetTime: number } | null> {
    const data = this.store.get(key);
    if (!data) return null;
    
    // Clean up expired entries
    if (data.resetTime < Date.now()) {
      this.store.delete(key);
      return null;
    }
    
    return data;
  }
  
  async increment(key: string, windowMs: number): Promise<{ count: number; resetTime: number }> {
    const now = Date.now();
    const existing = await this.get(key);
    
    if (existing && existing.resetTime > now) {
      existing.count++;
      this.store.set(key, existing);
      return existing;
    }
    
    // Create new entry
    const newEntry = {
      count: 1,
      resetTime: now + windowMs,
    };
    this.store.set(key, newEntry);
    return newEntry;
  }
  
  async reset(key: string): Promise<void> {
    this.store.delete(key);
  }
  
  // Cleanup expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, data] of this.store.entries()) {
      if (data.resetTime < now) {
        this.store.delete(key);
      }
    }
  }
}

export class RateLimiter {
  private store: MemoryStore;
  private defaultRule: RateLimitRule;
  
  // Subscription-based limits
  private subscriptionLimits: Record<string, SubscriptionLimits> = {
    free: {
      daily: 10,
      monthly: 100,
      concurrent: 1,
      apiCalls: 50,
    },
    pro: {
      daily: 100,
      monthly: 2000,
      concurrent: 3,
      apiCalls: 1000,
    },
    enterprise: {
      daily: 1000,
      monthly: 20000,
      concurrent: 10,
      apiCalls: 10000,
    },
  };

  constructor(defaultRule?: RateLimitRule) {
    this.store = new MemoryStore();
    this.defaultRule = defaultRule || {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 100,
    };
    
    // Start cleanup interval
    setInterval(() => this.store.cleanup(), 5 * 60 * 1000); // 5 minutes
  }

  // Check rate limit for general API usage
  async checkLimit(
    identifier: string, 
    rule?: RateLimitRule
  ): Promise<RateLimitResult> {
    const effectiveRule = rule || this.defaultRule;
    const key = effectiveRule.keyGenerator?.(identifier) || `rate_limit:${identifier}`;
    
    const result = await this.store.increment(key, effectiveRule.windowMs);
    const remaining = Math.max(0, effectiveRule.maxRequests - result.count);
    const allowed = result.count <= effectiveRule.maxRequests;
    
    return {
      allowed,
      remaining,
      resetTime: result.resetTime,
      totalHits: result.count,
      retryAfter: allowed ? undefined : Math.ceil((result.resetTime - Date.now()) / 1000),
    };
  }

  // Check subscription-based limits
  async checkSubscriptionLimit(
    userId: string,
    subscriptionTier: string,
    limitType: 'daily' | 'monthly' | 'concurrent' | 'apiCalls'
  ): Promise<RateLimitResult> {
    const limits = this.subscriptionLimits[subscriptionTier] || this.subscriptionLimits.free;
    const maxRequests = limits[limitType];
    
    let windowMs: number;
    switch (limitType) {
      case 'daily':
        windowMs = 24 * 60 * 60 * 1000; // 24 hours
        break;
      case 'monthly':
        windowMs = 30 * 24 * 60 * 60 * 1000; // 30 days
        break;
      case 'concurrent':
        windowMs = 60 * 1000; // 1 minute for concurrent limit
        break;
      case 'apiCalls':
        windowMs = 60 * 60 * 1000; // 1 hour
        break;
      default:
        windowMs = 60 * 60 * 1000;
    }
    
    const key = `subscription_limit:${userId}:${limitType}`;
    const result = await this.store.increment(key, windowMs);
    const remaining = Math.max(0, maxRequests - result.count);
    const allowed = result.count <= maxRequests;
    
    return {
      allowed,
      remaining,
      resetTime: result.resetTime,
      totalHits: result.count,
      retryAfter: allowed ? undefined : Math.ceil((result.resetTime - Date.now()) / 1000),
    };
  }

  // Check API-specific limits
  async checkApiLimit(
    userId: string,
    apiName: 'groq' | 'serper' | 'openai',
    subscriptionTier: string
  ): Promise<RateLimitResult> {
    // API-specific limits based on subscription
    const apiLimits: Record<string, Record<string, { requests: number; windowMs: number }>> = {
      groq: {
        free: { requests: 5, windowMs: 60 * 1000 }, // 5 per minute
        pro: { requests: 30, windowMs: 60 * 1000 }, // 30 per minute
        enterprise: { requests: 100, windowMs: 60 * 1000 }, // 100 per minute
      },
      serper: {
        free: { requests: 10, windowMs: 60 * 1000 }, // 10 per minute
        pro: { requests: 50, windowMs: 60 * 1000 }, // 50 per minute
        enterprise: { requests: 200, windowMs: 60 * 1000 }, // 200 per minute
      },
      openai: {
        free: { requests: 3, windowMs: 60 * 1000 }, // 3 per minute
        pro: { requests: 20, windowMs: 60 * 1000 }, // 20 per minute
        enterprise: { requests: 60, windowMs: 60 * 1000 }, // 60 per minute
      },
    };
    
    const limits = apiLimits[apiName]?.[subscriptionTier] || apiLimits[apiName]?.free;
    
    if (!limits) {
      throw new Error(`No limits defined for API: ${apiName}`);
    }
    
    const key = `api_limit:${userId}:${apiName}`;
    const result = await this.store.increment(key, limits.windowMs);
    const remaining = Math.max(0, limits.requests - result.count);
    const allowed = result.count <= limits.requests;
    
    return {
      allowed,
      remaining,
      resetTime: result.resetTime,
      totalHits: result.count,
      retryAfter: allowed ? undefined : Math.ceil((result.resetTime - Date.now()) / 1000),
    };
  }

  // Check cost-based limits
  async checkCostLimit(
    userId: string,
    subscriptionTier: string,
    cost: number
  ): Promise<{ allowed: boolean; remainingBudget: number; totalSpent: number }> {
    // Monthly cost limits by subscription
    const costLimits: Record<string, number> = {
      free: 5.00, // $5 per month
      pro: 50.00, // $50 per month
      enterprise: 500.00, // $500 per month
    };
    
    const monthlyLimit = costLimits[subscriptionTier] || costLimits.free;
    const key = `cost_limit:${userId}:monthly`;
    
    // Get current month's spending
    const now = Date.now();
    const monthStart = new Date(now);
    monthStart.setDate(1);
    monthStart.setHours(0, 0, 0, 0);
    const monthKey = `${key}:${monthStart.getTime()}`;
    
    const currentSpending = await this.store.get(monthKey);
    const totalSpent = currentSpending ? currentSpending.count / 100 : 0; // Stored in cents
    const newTotal = totalSpent + cost;
    
    const allowed = newTotal <= monthlyLimit;
    const remainingBudget = Math.max(0, monthlyLimit - newTotal);
    
    if (allowed) {
      // Update spending
      const nextMonth = new Date(monthStart);
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      await this.store.increment(monthKey, nextMonth.getTime() - now);
    }
    
    return {
      allowed,
      remainingBudget,
      totalSpent: newTotal,
    };
  }

  // Reset specific limits (for admin use)
  async resetLimit(identifier: string, limitType?: string): Promise<void> {
    if (limitType) {
      const key = `${limitType}:${identifier}`;
      await this.store.reset(key);
    } else {
      // Reset all limits for identifier
      const patterns = [
        `rate_limit:${identifier}`,
        `subscription_limit:${identifier}:*`,
        `api_limit:${identifier}:*`,
        `cost_limit:${identifier}:*`,
      ];
      
      // In a real Redis implementation, we'd use SCAN with patterns
      // For memory store, we'll just reset known keys
      for (const pattern of patterns) {
        await this.store.reset(pattern);
      }
    }
  }

  // Get current usage statistics
  async getUsageStats(userId: string): Promise<{
    rateLimit: any;
    subscriptionLimits: any;
    apiLimits: any;
    costUsage: any;
  }> {
    const stats = {
      rateLimit: {},
      subscriptionLimits: {},
      apiLimits: {},
      costUsage: {},
    };

    // This would be more sophisticated with Redis
    // For now, return basic structure
    return stats;
  }

  // Middleware function for Express/Next.js
  createMiddleware(rule?: RateLimitRule) {
    return async (identifier: string) => {
      const result = await this.checkLimit(identifier, rule);
      
      if (!result.allowed) {
        const error = new Error('Rate limit exceeded');
        (error as any).statusCode = 429;
        (error as any).retryAfter = result.retryAfter;
        (error as any).resetTime = result.resetTime;
        throw error;
      }
      
      return {
        'X-RateLimit-Limit': rule?.maxRequests || this.defaultRule.maxRequests,
        'X-RateLimit-Remaining': result.remaining,
        'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
      };
    };
  }
}

// Predefined rate limiters for different use cases
export const generalRateLimiter = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100,
});

export const strictRateLimiter = new RateLimiter({
  windowMs: 60 * 1000, // 1 minute
  maxRequests: 10,
});

export const authRateLimiter = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // Only 5 auth attempts per 15 minutes
});

// Helper function for API routes
export async function withRateLimit<T>(
  identifier: string,
  subscriptionTier: string,
  apiName: 'groq' | 'serper' | 'openai',
  operation: () => Promise<T>
): Promise<T> {
  const rateLimiter = new RateLimiter();
  
  // Check API-specific limits
  const apiLimit = await rateLimiter.checkApiLimit(identifier, apiName, subscriptionTier);
  
  if (!apiLimit.allowed) {
    const error = new Error(`${apiName} API rate limit exceeded`);
    (error as any).statusCode = 429;
    (error as any).retryAfter = apiLimit.retryAfter;
    (error as any).resetTime = apiLimit.resetTime;
    (error as any).remaining = apiLimit.remaining;
    throw error;
  }
  
  return operation();
}

// Helper function for subscription limits
export async function withSubscriptionLimit<T>(
  userId: string,
  subscriptionTier: string,
  limitType: 'daily' | 'monthly' | 'concurrent' | 'apiCalls',
  operation: () => Promise<T>
): Promise<T> {
  const rateLimiter = new RateLimiter();
  
  const subscriptionLimit = await rateLimiter.checkSubscriptionLimit(
    userId,
    subscriptionTier,
    limitType
  );
  
  if (!subscriptionLimit.allowed) {
    const error = new Error(`${limitType} subscription limit exceeded`);
    (error as any).statusCode = 429;
    (error as any).retryAfter = subscriptionLimit.retryAfter;
    (error as any).resetTime = subscriptionLimit.resetTime;
    (error as any).remaining = subscriptionLimit.remaining;
    throw error;
  }
  
  return operation();
}

// Helper function for cost limits
export async function withCostLimit<T>(
  userId: string,
  subscriptionTier: string,
  estimatedCost: number,
  operation: () => Promise<T>
): Promise<T> {
  const rateLimiter = new RateLimiter();
  
  const costLimit = await rateLimiter.checkCostLimit(
    userId,
    subscriptionTier,
    estimatedCost
  );
  
  if (!costLimit.allowed) {
    const error = new Error(
      `Monthly cost limit exceeded. Remaining budget: $${costLimit.remainingBudget.toFixed(2)}`
    );
    (error as any).statusCode = 402; // Payment Required
    (error as any).remainingBudget = costLimit.remainingBudget;
    (error as any).totalSpent = costLimit.totalSpent;
    throw error;
  }
  
  return operation();
}

export default RateLimiter;