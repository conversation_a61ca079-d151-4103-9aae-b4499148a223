const { createClient } = require('@supabase/supabase-js');
const ContentGenerator = require('./contentGenerator');
const CompetitorAnalyzer = require('./competitorAnalyzer');
const MetaTagGenerator = require('./metaTagGenerator');
const csv = require('csv-parser');
const XLSX = require('xlsx');
const fs = require('fs').promises;
const path = require('path');

class BulkProcessor {
  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_KEY
    );
    
    // Processing queues for different operation types
    this.processingQueues = new Map();
    this.isProcessing = new Map();
    
    // Rate limiting configuration
    this.rateLimits = {
      content_generation: 10, // keywords per minute
      seo_analysis: 15,
      meta_tags: 20,
      competitor_analysis: 5
    };
    
    // Maximum concurrent operations per user
    this.maxConcurrentOperations = 3;
  }

  // Parse uploaded file and extract keywords
  async parseKeywordFile(filePath, fileType) {
    try {
      const keywords = [];
      
      if (fileType === 'csv') {
        return await this.parseCSVFile(filePath);
      } else if (fileType === 'xlsx' || fileType === 'xls') {
        return await this.parseExcelFile(filePath);
      } else {
        throw new Error('Unsupported file type. Please use CSV or Excel files.');
      }
    } catch (error) {
      console.error('Error parsing keyword file:', error);
      throw new Error(`Failed to parse file: ${error.message}`);
    }
  }

  // Parse CSV file
  async parseCSVFile(filePath) {
    return new Promise((resolve, reject) => {
      const keywords = [];
      const stream = require('fs').createReadStream(filePath);
      
      stream
        .pipe(csv())
        .on('data', (row) => {
          // Extract keyword and optional parameters
          const keyword = row.keyword || row.Keyword || row.KEYWORD || Object.values(row)[0];
          if (keyword && keyword.trim()) {
            keywords.push({
              keyword: keyword.trim(),
              industry: row.industry || row.Industry || '',
              contentType: row.content_type || row.contentType || row['Content Type'] || 'article',
              tone: row.tone || row.Tone || 'professional',
              wordCount: parseInt(row.word_count || row.wordCount || row['Word Count']) || 1000,
              location: row.location || row.Location || 'United States',
              language: row.language || row.Language || 'en'
            });
          }
        })
        .on('end', () => {
          resolve(keywords);
        })
        .on('error', (error) => {
          reject(error);
        });
    });
  }

  // Parse Excel file
  async parseExcelFile(filePath) {
    try {
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const data = XLSX.utils.sheet_to_json(worksheet);
      
      const keywords = data
        .map(row => {
          const keyword = row.keyword || row.Keyword || row.KEYWORD || Object.values(row)[0];
          if (keyword && keyword.trim()) {
            return {
              keyword: keyword.trim(),
              industry: row.industry || row.Industry || '',
              contentType: row.content_type || row.contentType || row['Content Type'] || 'article',
              tone: row.tone || row.Tone || 'professional',
              wordCount: parseInt(row.word_count || row.wordCount || row['Word Count']) || 1000,
              location: row.location || row.Location || 'United States',
              language: row.language || row.Language || 'en'
            };
          }
          return null;
        })
        .filter(item => item !== null);
      
      return keywords;
    } catch (error) {
      throw new Error(`Failed to parse Excel file: ${error.message}`);
    }
  }

  // Parse textarea input (one keyword per line)
  parseTextareaInput(textInput, defaultSettings = {}) {
    const lines = textInput.split('\n').filter(line => line.trim());
    return lines.map(keyword => ({
      keyword: keyword.trim(),
      industry: defaultSettings.industry || '',
      contentType: defaultSettings.contentType || 'article',
      tone: defaultSettings.tone || 'professional',
      wordCount: defaultSettings.wordCount || 1000,
      location: defaultSettings.location || 'United States',
      language: defaultSettings.language || 'en'
    }));
  }

  // Create bulk operation
  async createBulkOperation(userId, operationType, keywords, settings = {}, projectId = null) {
    try {
      // Check if user has reached concurrent operation limit
      await this.checkConcurrentOperationLimit(userId);
      
      // Validate keywords
      if (!keywords || keywords.length === 0) {
        throw new Error('No keywords provided');
      }
      
      if (keywords.length > 500) {
        throw new Error('Maximum 500 keywords allowed per bulk operation');
      }
      
      // Create bulk operation record
      const { data: operation, error: operationError } = await this.supabase
        .from('bulk_operations')
        .insert({
          user_id: userId,
          project_id: projectId,
          operation_type: operationType,
          total_keywords: keywords.length,
          settings,
          estimated_completion: this.calculateEstimatedCompletion(operationType, keywords.length)
        })
        .select()
        .single();
      
      if (operationError) {
        throw new Error(`Failed to create bulk operation: ${operationError.message}`);
      }
      
      // Create keyword records
      const keywordRecords = keywords.map((keyword, index) => ({
        bulk_operation_id: operation.id,
        keyword: keyword.keyword,
        processing_order: index + 1,
        input_data: {
          industry: keyword.industry,
          contentType: keyword.contentType,
          tone: keyword.tone,
          wordCount: keyword.wordCount,
          location: keyword.location,
          language: keyword.language
        }
      }));
      
      const { error: keywordsError } = await this.supabase
        .from('bulk_keywords')
        .insert(keywordRecords);
      
      if (keywordsError) {
        throw new Error(`Failed to create keyword records: ${keywordsError.message}`);
      }
      
      // Start processing
      this.startBulkProcessing(operation.id, userId);
      
      return {
        success: true,
        operationId: operation.id,
        totalKeywords: keywords.length,
        estimatedCompletion: operation.estimated_completion
      };
      
    } catch (error) {
      console.error('Error creating bulk operation:', error);
      throw error;
    }
  }

  // Check concurrent operation limit
  async checkConcurrentOperationLimit(userId) {
    const { data: activeOperations } = await this.supabase
      .from('bulk_operations')
      .select('id')
      .eq('user_id', userId)
      .in('status', ['queued', 'processing']);
    
    if (activeOperations && activeOperations.length >= this.maxConcurrentOperations) {
      throw new Error(`Maximum ${this.maxConcurrentOperations} concurrent bulk operations allowed`);
    }
  }

  // Calculate estimated completion time
  calculateEstimatedCompletion(operationType, keywordCount) {
    const processingRatesPerMinute = {
      content_generation: 10,
      seo_analysis: 15,
      meta_tags: 20,
      competitor_analysis: 5
    };
    
    const ratePerMinute = processingRatesPerMinute[operationType] || 10;
    const estimatedMinutes = Math.ceil(keywordCount / ratePerMinute);
    
    const estimatedCompletion = new Date();
    estimatedCompletion.setMinutes(estimatedCompletion.getMinutes() + estimatedMinutes);
    
    return estimatedCompletion.toISOString();
  }

  // Start bulk processing
  async startBulkProcessing(operationId, userId) {
    try {
      // Update operation status to processing
      await this.supabase
        .from('bulk_operations')
        .update({
          status: 'processing',
          started_at: new Date().toISOString()
        })
        .eq('id', operationId);
      
      // Get operation details
      const { data: operation } = await this.supabase
        .from('bulk_operations')
        .select('*')
        .eq('id', operationId)
        .single();
      
      if (!operation) {
        throw new Error('Operation not found');
      }
      
      // Process keywords in batches
      await this.processKeywordsBatch(operation);
      
    } catch (error) {
      console.error('Error starting bulk processing:', error);
      
      // Mark operation as failed
      await this.supabase
        .from('bulk_operations')
        .update({
          status: 'failed',
          error_message: error.message
        })
        .eq('id', operationId);
    }
  }

  // Process keywords in batches
  async processKeywordsBatch(operation) {
    const batchSize = Math.min(10, this.rateLimits[operation.operation_type] || 10);
    
    // Get all keywords for this operation
    const { data: keywords } = await this.supabase
      .from('bulk_keywords')
      .select('*')
      .eq('bulk_operation_id', operation.id)
      .eq('status', 'queued')
      .order('processing_order');
    
    if (!keywords || keywords.length === 0) {
      return;
    }
    
    // Process keywords in chunks
    for (let i = 0; i < keywords.length; i += batchSize) {
      const batch = keywords.slice(i, i + batchSize);
      
      // Process batch in parallel
      const promises = batch.map(keyword => 
        this.processKeyword(operation, keyword)
      );
      
      await Promise.allSettled(promises);
      
      // Add delay between batches to respect rate limits
      if (i + batchSize < keywords.length) {
        await new Promise(resolve => setTimeout(resolve, 60000 / this.rateLimits[operation.operation_type]));
      }
    }
  }

  // Process individual keyword
  async processKeyword(operation, keyword) {
    try {
      // Update keyword status to processing
      await this.supabase
        .from('bulk_keywords')
        .update({
          status: 'processing',
          processing_started_at: new Date().toISOString()
        })
        .eq('id', keyword.id);
      
      let result = null;
      
      // Process based on operation type
      switch (operation.operation_type) {
        case 'content_generation':
          result = await this.generateContent(keyword, operation.settings);
          break;
        case 'meta_tags':
          result = await this.generateMetaTags(keyword, operation.settings);
          break;
        case 'competitor_analysis':
          result = await this.analyzeCompetitors(keyword, operation.settings);
          break;
        default:
          throw new Error(`Unsupported operation type: ${operation.operation_type}`);
      }
      
      // Save result
      await this.saveResult(operation.id, keyword.id, operation.operation_type, result);
      
      // Update keyword status to completed
      await this.supabase
        .from('bulk_keywords')
        .update({
          status: 'completed',
          processing_completed_at: new Date().toISOString(),
          output_data: result
        })
        .eq('id', keyword.id);
      
    } catch (error) {
      console.error(`Error processing keyword ${keyword.keyword}:`, error);
      
      // Update keyword status to failed
      await this.supabase
        .from('bulk_keywords')
        .update({
          status: 'failed',
          processing_completed_at: new Date().toISOString(),
          error_message: error.message
        })
        .eq('id', keyword.id);
    }
  }

  // Generate content for keyword
  async generateContent(keyword, settings) {
    const generator = new ContentGenerator();
    const inputData = keyword.input_data;
    
    const params = {
      keyword: keyword.keyword,
      contentType: inputData.contentType || 'article',
      tone: inputData.tone || 'professional',
      wordCount: inputData.wordCount || 1000,
      industry: inputData.industry || '',
      location: inputData.location || 'United States',
      searchEngine: settings.searchEngine || 'google',
      intent: settings.intent || 'informational',
      ...settings
    };
    
    return await generator.generateContent(params);
  }

  // Generate meta tags for keyword
  async generateMetaTags(keyword, settings) {
    // For meta tags, we need content first, so generate minimal content
    const content = `Content about ${keyword.keyword}. This is sample content for meta tag generation.`;
    const generator = new MetaTagGenerator(content, keyword.keyword, {
      contentType: keyword.input_data.contentType || 'article',
      industry: keyword.input_data.industry || '',
      ...settings
    });
    
    return generator.generateAllMetaTags();
  }

  // Analyze competitors for keyword
  async analyzeCompetitors(keyword, settings) {
    const analyzer = new CompetitorAnalyzer();
    
    return await analyzer.analyzeCompetitors(keyword.keyword, {
      location: keyword.input_data.location || 'United States',
      searchEngine: settings.searchEngine || 'google',
      ...settings
    });
  }

  // Save processing result
  async saveResult(operationId, keywordId, resultType, resultData) {
    const result = {
      bulk_operation_id: operationId,
      bulk_keyword_id: keywordId,
      result_type: resultType === 'content_generation' ? 'content' : resultType,
      result_data: resultData,
      quality_score: this.calculateQualityScore(resultData),
      word_count: this.extractWordCount(resultData),
      seo_score: this.extractSEOScore(resultData)
    };
    
    const { error } = await this.supabase
      .from('bulk_results')
      .insert(result);
    
    if (error) {
      throw new Error(`Failed to save result: ${error.message}`);
    }
  }

  // Calculate quality score based on result data
  calculateQualityScore(resultData) {
    if (resultData.seoScore) {
      return resultData.seoScore;
    }
    
    // Simple quality scoring based on content length and structure
    if (resultData.content) {
      const wordCount = resultData.content.split(' ').length;
      if (wordCount > 800) return 85.0;
      if (wordCount > 500) return 75.0;
      if (wordCount > 300) return 65.0;
      return 50.0;
    }
    
    return 70.0; // Default score
  }

  // Extract word count from result data
  extractWordCount(resultData) {
    if (resultData.wordCount) return resultData.wordCount;
    if (resultData.content) {
      return resultData.content.split(' ').length;
    }
    return 0;
  }

  // Extract SEO score from result data
  extractSEOScore(resultData) {
    return resultData.seoScore || resultData.analysis?.overallScore || null;
  }

  // Get operation status
  async getOperationStatus(operationId, userId) {
    const { data: operation } = await this.supabase
      .from('bulk_operation_summary')
      .select('*')
      .eq('id', operationId)
      .eq('user_id', userId)
      .single();
    
    if (!operation) {
      throw new Error('Operation not found');
    }
    
    return operation;
  }

  // Get operation results
  async getOperationResults(operationId, userId, page = 1, limit = 50) {
    // Verify user owns this operation
    const { data: operation } = await this.supabase
      .from('bulk_operations')
      .select('id')
      .eq('id', operationId)
      .eq('user_id', userId)
      .single();
    
    if (!operation) {
      throw new Error('Operation not found or access denied');
    }
    
    const offset = (page - 1) * limit;
    
    const { data: results, count } = await this.supabase
      .from('bulk_results')
      .select(`
        *,
        bulk_keywords (
          keyword,
          status,
          error_message,
          input_data
        )
      `, { count: 'exact' })
      .eq('bulk_operation_id', operationId)
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });
    
    return {
      results: results || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: Math.ceil((count || 0) / limit)
      }
    };
  }

  // Cancel bulk operation
  async cancelOperation(operationId, userId) {
    const { data: operation } = await this.supabase
      .from('bulk_operations')
      .select('status')
      .eq('id', operationId)
      .eq('user_id', userId)
      .single();
    
    if (!operation) {
      throw new Error('Operation not found');
    }
    
    if (operation.status === 'completed') {
      throw new Error('Cannot cancel completed operation');
    }
    
    // Update operation status
    await this.supabase
      .from('bulk_operations')
      .update({
        status: 'cancelled',
        completed_at: new Date().toISOString()
      })
      .eq('id', operationId);
    
    // Cancel queued keywords
    await this.supabase
      .from('bulk_keywords')
      .update({ status: 'failed', error_message: 'Operation cancelled by user' })
      .eq('bulk_operation_id', operationId)
      .eq('status', 'queued');
    
    return { success: true };
  }

  // Get user's bulk operations
  async getUserOperations(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const { data: operations, count } = await this.supabase
      .from('bulk_operation_summary')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });
    
    return {
      operations: operations || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: Math.ceil((count || 0) / limit)
      }
    };
  }
}

module.exports = BulkProcessor;